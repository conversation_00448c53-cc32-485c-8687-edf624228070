import { customAxios } from "@/utils/axio-interceptor";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Suspend admin client - Updated to match backend requirements
export const adminClientSuspendApi = async (clientId: string, duration: string) => {
  try {
    const response = await customAxios.put(`/v1/admin/suspend-client/${clientId}`, {
      duration  // Changed from suspensionPeriod to duration
    });
    // toast.success("Client suspended successfully");
    return response.data;
  } catch (error: any) {
    toast.error(error.response?.data?.message || "Failed to suspend client");
    throw error;
  }
};

// Activate admin client
export const adminClientActiveApi = async (clientId: string) => {
  try {
    const response = await customAxios.put(`/v1/admin/activate-client/${clientId}`);
    // toast.success("Client activated successfully");
    return response.data;
  } catch (error: any) {
    toast.error(error.response?.data?.message || "Failed to activate client");
    throw error;
  }
};

// Delete admin client
export const adminClientDeleteApi = async (clientId: string) => {
  try {
    const response = await customAxios.put(`/v1/admin/delete-client/${clientId}`);
    toast.success("Client deleted successfully");
    return response.data;
  } catch (error: any) {
    toast.error(error.response?.data?.message || "Failed to delete client");
    throw error;
  }
};