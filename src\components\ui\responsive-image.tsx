import React from "react";
import { useResponsive } from "@/hooks/use-responsive";

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
}

/**
 * A component that displays an image responsively across different screen sizes
 */
export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = "",
  containerClassName = ""
}) => {
  const {  isLaptopLg } = useResponsive();

  // Determine the appropriate object-fit based on screen size
  const getObjectFit = () => {
    if (isLaptopLg) return "contain"; // 4K/2560px - changed from cover to contain
    return "fill"; // 1440px and 1024px
  };

  return (
    <div className={`relative overflow-hidden ${containerClassName}`}>
      <img
        src={src}
        alt={alt}
        className={`${isLaptopLg ? 'max-h-screen' : 'w-full h-full'} ${className}`}
        style={{
          objectFit: getObjectFit() as any,
          objectPosition: "center",
          maxWidth: isLaptopLg ? '100%' : 'none'
        }}
      />
    </div>
  );
};

export default ResponsiveImage;
