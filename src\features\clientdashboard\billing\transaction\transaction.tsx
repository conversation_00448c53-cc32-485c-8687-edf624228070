import { columns } from "./column";
import { useState, useEffect } from "react";
import { TransactionDataTable } from "./datatable";
import { usePagination } from "@/components/globalfiles/usePagination";
import { BackButton } from "@/_components/common";
import { getBillingList } from "../billing_api/billingapi";

const TransactionList = () => {
  const { onPaginationChange, pagination } = usePagination();
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [pageCount, setPageCount] = useState(1);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getBillingList();
        if (response.status === 1) {
          // Transform the data to match your table structure
          const transformedData = response.data.map((item: any) => ({
            name: item.subscription.package.name,
            paymentId: item.paymentId,
            amount: item.amount,
            status: item.status,
            paymentMethod: item.paymentMethod,
            next_billing_date: new Date(item.subscription.next_billing_date).toLocaleDateString(),
          }));
          setData(transformedData);
          setPageCount(Math.ceil(transformedData.length / pagination.pageSize));
        }
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [pagination.pageIndex, pagination.pageSize]);

  return (
    <div className="p-3 bg-white h-full space-y-6">
      <div className="flex flex-col justify-center">
        <div className="flex flex-row items-center">
          <BackButton />
          <p className="font-bold font-poppins text-[34px] text-[#F97B9A]">
            Transactions
          </p>
        </div>
       
      </div>
      <div className=" rounded-lg p-4 shadow-sm">
        <TransactionDataTable
          title="Forms"
          columns={columns}
          data={data}
          onPaginationChange={onPaginationChange}
          loading={loading}
          pagination={pagination}
          pageCount={pageCount}
        />
      </div>
    </div>
  );
};

export default TransactionList;