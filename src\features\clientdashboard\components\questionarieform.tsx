import React, { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { useNavigate } from "react-router-dom";

const ClientForm: React.FC = () => {
  // Get user and accessToken from Redux
  const user = useSelector((state: RootState) => state.auth.user);
  // const token = useSelector((state: RootState) => state.auth.accessToken);
  const navigate = useNavigate();

  // Local state for form data
  const [formData, setFormData] = useState({
    packageCategory: "",
    timezone: "",
    industry: "",
    annotationCategory: "",
    fromTime: "",
    toTime: "",
    description: "",
  });

  // Update form data on input/select changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // When component mounts or Redux state changes, automatically redirect if the user is an authenticated client  
//   useEffect(() => {
//     if (user && user.role === "client" && token) {
//       console.log("Authenticated Client User:", user);
//       navigate("/dashboard");
//     }
//   }, [user, token, navigate]);

  // This handles the payment process
//   const handlePayment = () => {
//     if (user && user.role === "client" && token) {
//       console.log("User Role:", user.role);
//       console.log("User Details:", user);
//       console.log("Access Token:", token);
//       console.log("Form Data:", formData);

//       // You can perform your API call/payment processing here

//       navigate("/dashboard");
//     } else {
//       alert("Access Denied: User not authenticated or not a client.");
//     }
//   };

  // If the user is not authenticated or not a client, display an error message.
  if (!user || user.role !== "client") {
    return (
      <div className="text-center text-red-600 mt-10">
        Access Denied. This form is only for clients.
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto bg-white shadow-lg rounded-xl p-6 mt-10">
      <h2 className="text-2xl font-semibold mb-6 text-center text-gray-800">
        Questionnaire
      </h2>

      <div className="space-y-4">
        <select
          name="packageCategory"
          value={formData.packageCategory}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
        >
          <option value="">Select Package Category</option>
          <option>Basic</option>
          <option>Premium</option>
        </select>

        <select
          name="timezone"
          value={formData.timezone}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
        >
          <option value="">Select your preferred time zone</option>
          <option>IST (India)</option>
          <option>GMT</option>
        </select>

        <select
          name="industry"
          value={formData.industry}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
        >
          <option value="">Enter Industry</option>
          <option>IT</option>
          <option>Education</option>
          <option>Healthcare</option>
        </select>

        <select
          name="annotationCategory"
          value={formData.annotationCategory}
          onChange={handleChange}
          className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
        >
          <option value="">Enter Annotation Category</option>
          <option>Image</option>
          <option>Text</option>
          <option>Video</option>
        </select>

        <div>
          <label className="block mb-1 font-medium">
            Preferred Work Duration
          </label>
          <div className="flex gap-4">
            <input
              type="time"
              name="fromTime"
              value={formData.fromTime}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-lg p-2"
            />
            <input
              type="time"
              name="toTime"
              value={formData.toTime}
              onChange={handleChange}
              className="w-full border border-gray-300 rounded-lg p-2"
            />
          </div>
        </div>

        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Description (optional)"
          className="w-full border border-gray-300 rounded-lg p-3 resize-none h-32"
        ></textarea>

        <button
          className="w-full bg-gradient-to-r from-pink-500 to-indigo-500 text-white py-3 rounded-lg font-semibold hover:opacity-90 transition"
          onClick={() => navigate("/dashboard")}
        >
          Process for Payment →
        </button>
      </div>
    </div>
  );
};

export default ClientForm;
