import { useEffect } from "react";
import { Socket } from "socket.io-client";

interface UseDMRoomProps {
  socket: Socket | null;
  userId: string;
  otherUserId: string;
}

export const useJoinDMRoom = ({
  socket,
  userId,
  otherUserId,
}: UseDMRoomProps) => {
  useEffect(() => {
    if (!socket || !userId || !otherUserId) return;

    // Create a consistent room name (sorted to avoid duplicates)
    const roomId =
      userId < otherUserId
        ? `dm:${userId}-${otherUserId}`
        : `dm:${otherUserId}-${userId}`;

    socket.emit("join_dm", { userId, otherUserId });

    return () => {
      socket.emit("leave_dm_room", { roomId });
    };
  }, [socket, userId, otherUserId]);
};