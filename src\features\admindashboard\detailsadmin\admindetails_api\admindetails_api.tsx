// admindetails_api.tsx
import { customAxios } from "@/utils/axio-interceptor";

// annonators only all apis
export const getAdminAnnotators = async () => {
  const response = await customAxios.get(
    "/v1/annotator/get-all-admin-annotators"
  );
  return response.data;
};

// coordinator all apis

export const getAdminCoordinators = async () => {
  const response = await customAxios.get(
    "/v1/annotator/get-all-admin-coordinators"
  );
  return response.data;
};

export const getAdminCoordinatorById = async (id: string) => {
  try {
    console.log("Fetching clients for coordinator ID:", id);
    const response = await customAxios.get(
      `/v1/annotator/get-all-coordinator-clients?coordinatorId=${id}`
    );
    console.log("Coordinator clients API response:", response);
    return response.data;
  } catch (error) {
    console.error("Error fetching coordinator clients:", error);
    throw error;
  }
};

// clientds apis all
export const getAdminProjects = async () => {
  const response = await customAxios.get(
    "/v1/annotator/clients-with-subscriptions"
  );
  return response.data;
};

//clientds id tables
export const getAdminClientById = async (id: string) => {
  try {
    console.log("Fetching projects for client ID:", id);
    const response = await customAxios.get(
      `/v1/projects/projects-by-client/${id}`
    );
    console.log("Client projects API response:", response);
    return response.data;
  } catch (error) {
    console.error("Error fetching client projects:", error);
    throw error;
  }
};

export const getAdminClient = async () => {
  try {
    const response = await customAxios.get(
      `v1/annotator/clients-with-subscriptions`
    );
    console.log("Raw API response:", response);
    return response.data;
  } catch (error) {
    console.error("Error in getAdminClient:", error);
    throw error;
  }
};

export const getProjectDetails = async (Id: string) => {
  try {
    console.log("API call: getProjectDetails with ID:", Id);
    const response = await customAxios.get(`/v1/projects/project-by-id/${Id}`);
    console.log("API response from getProjectDetails:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching project details:", error);
    throw error;
  }
};


// In admindetails_api.tsx, update AdminAnnonatorShiftTiming to exclude reason
export const AdminAnnonatorShiftTiming = async (data: {
  annotatorId: string;
  newFrom: string;
  newTo: string;
}) => {
  try {
    const response = await customAxios.post("/v1/shift/create-shift-change", {
      annotatorId: data.annotatorId,
      newFrom: data.newFrom,
      newTo: data.newTo,
    });
    console.log("Shift change API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error creating shift change:", error);
    throw error;
  }
};