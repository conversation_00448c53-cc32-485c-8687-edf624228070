import ProjectList from "./component/annotatorproject";
import TopSection from "./component/topSection";
import AnnotatorKanbanBoard from "../annotatordashboard";

const AnnotatorDashboard = () => {
  return (
    <div>
      <div className="w-full flex flex-col mx-auto gap-4 p-4 px-8">
        <div>
          <TopSection />
          {/* Responsive layout: side-by-side on xl+ screens, stacked on lg screens */}
          <div className="lg-only:flex lg-only:flex-col xl:flex xl:flex-row xl:justify-between xl:gap-12 2xl:gap-16 mt-5">
            {/* Kanban board - full width on lg, flex-grow on xl+ */}
            <div className="lg-only:w-full xl:flex-grow">
              <AnnotatorKanbanBoard />
            </div>

            {/* Project list - bottom on lg, right side on xl+ */}
            <div className="lg-only:mt-20 xl:mt-4 xl:flex xl:flex-col xl:gap-4">
              <ProjectList />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnotatorDashboard;
