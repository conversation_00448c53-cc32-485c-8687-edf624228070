import React from "react";
import { useNavigate } from "react-router-dom";

const Password: React.FC = () => {
  const navigate = useNavigate();
  return (
    <div className="w-full flex items-center justify-center">
      <button
        className="text-pink-500 left-4 hover:text-pink-600 absolute text-lg font-medium flex items-center top-3"
        onClick={() => navigate(-1)}
      >
        ← Go Back
      </button>
      <div className="w-full max-w-sm">
        <div className="flex mt-6 mb-6 space-x-2">
          <div className="h-1 w-1/3 bg-pink-500       rounded"></div>
          <div className="h-1 w-1/3 bg-pink-500  rounded"></div>
          <div className="h-1 w-1/3 bg-gray-200 rounded"></div>
        </div>
        <h2 className="text-[36px] w-[321px] font-bold   font-inter text-[#282828]mb-4">
          Sign Up to get started
        </h2>
        <p className="text-gray-600  mt-4 mb-4 font-inter ">
          <EMAIL>{" "}
          <a href="#" className="text-pink-500 hover:underline font-medium">
            Change
          </a>
        </p>

        {/* Password Field */}
        <div className="mb-7">
          <label className="block text-[18px]   font-inter   text-gray-700 font-medium mb-1">
            Password
          </label>
          <input
            type="password"
            placeholder="Enter your Password"
            className="w-full p-3 border outline outline-2 outline-[bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]] focus:outline-blue-700 rounded-lg transition-all focus:outline-none"
          />
        </div>

        {/* Password Rules */}
        <ul className="text-sm text-gray-700 mb-4 mt-3 space-y-2">
          <li className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-500 text-white text-sm">
              ✔
            </span>
            Enter between 8 to 14 characters.
          </li>
          <li className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-500 text-white text-sm">
              ✔
            </span>
            An upper and lowercase letter.
          </li>
          <li className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-500 text-white text-sm">
              ✔
            </span>
            At least one number.
          </li>
          <li className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center  w-6 h-6 rounded-full bg-green-500 text-white text-sm">
              ✔
            </span>
            At least one symbol.
          </li>
        </ul>

        {/* Button */}
        <button
          onClick={() => navigate("/otp-verification")}
          className="relative mt-6 w-full p-4 bg-gradient-to-r from-pink-500 to-blue-500 text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-lg"
        >
          <span className="block text-center w-full text-lg">Next</span>
          <span className="absolute right-4 top-1/2 -translate-y-1/2">→</span>
        </button>

        <p className="text-lg text-gray-600 mt-8 text-center">
          Already have a account?{" "}
          <a href="#" className="text-pink-500 hover:underline">
            Log in
          </a>
        </p>
      </div>
    </div>
  );
};

export default Password;
