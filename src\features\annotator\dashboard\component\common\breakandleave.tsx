import { useState, useEffect } from "react";
import RequestLeave from "./requestleave";
import { AttendanceRecord } from "@/features/annotator/types/attendence.types";
import {
  useTakeBreakMutation,
  useUndoBreakMutation,
} from "@/features/annotator/annonator_api/annotator.mutation";
import BreakInOutModal from "../modal/breakinout";

interface BreakProps {
  data: AttendanceRecord | undefined;
}

const Break: React.FC<BreakProps> = ({ data }) => {
  const [onBreak, setOnBreak] = useState<boolean>(false);
  const [showLeaveDrawer, setShowLeaveDrawer] = useState<boolean>(false);
  const [showBreakModal, setShowBreakModal] = useState<boolean>(false);

  // Mutation hooks
  const takeBreakMutation = useTakeBreakMutation();
  const undoBreakMutation = useUndoBreakMutation();

  // Sync break state from backend
  useEffect(() => {
    if (data?.status === "ON_BREAK") {
      setOnBreak(true);
    } else {
      setOnBreak(false);
    }
  }, [data]);

  const handleBreakToggle = () => {
    setShowBreakModal(true);
  };

  const confirmBreakAction = () => {
    setShowBreakModal(false);
    if (onBreak) {
      undoBreakMutation.mutate(undefined, {
        onSuccess: () => {
          setOnBreak(false);
        },
      });
    } else {
      takeBreakMutation.mutate(undefined, {
        onSuccess: () => {
          setOnBreak(true);
        },
      });
    }
  };

  const handleLeaveRequestSuccess = () => {
    console.log("Leave requested successfully!");
  };

  return (
    <>
      {/* Break & Leave Section */}
      <div
        className="flex flex-col justify-between border-gradient rounded-xl
        lg-only:p-4 lg-only:min-h-[140px] lg-only:max-w-[48vw]
        xl:p-5 xl:min-h-[160px] xl:max-w-[30vw]
        2xl:p-6 2xl:min-h-[180px] 2xl:max-w-[28vw]
        bg-white shadow-md"
      >
        {/* Buttons */}
        <div className="flex flex-col lg-only:flex-col xl:flex-row 2xl:flex-row justify-center gap-2 lg-only:gap-2 xl:gap-3 2xl:gap-3 mb-3">
          <button
            onClick={handleBreakToggle}
            className={`px-4 lg-only:px-5 xl:px-5 2xl:px-5 py-2 rounded-lg font-semibold lg-only:text-xs xl:text-sm 2xl:text-sm transition-all duration-200 border border-[#4B4B4B] text-[#4B4B4B] hover:bg-gray-100`}
            disabled={takeBreakMutation.isPending || undoBreakMutation.isPending}
          >
            {takeBreakMutation.isPending || undoBreakMutation.isPending
              ? "Processing..."
              : onBreak
              ? "Remove Break"
              : "Start Break"}
          </button>
          <button
            className="px-4 lg-only:px-5 xl:px-5 2xl:px-5 py-2 rounded-lg font-semibold lg-only:text-xs xl:text-sm 2xl:text-sm border border-[#FF577F] text-[#FF577F] hover:bg-[#ffe5e9] transition duration-300"
            onClick={() => setShowLeaveDrawer(true)}
          >
            Request Leave
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-x-2 lg-only:gap-x-3 xl:gap-x-4 2xl:gap-x-4 lg-only:text-xs xl:text-sm 2xl:text-sm text-gray-800">
          <div className="space-y-1">
            <p className="flex justify-between">
              <span className="font-semibold">Total Break:</span>
              <span>
                {data?.totalBreak ?? 0} <span className="text-xs">*</span>
              </span>
            </p>
            <p className="flex justify-between">
              <span className="font-semibold">Available:</span>
              <span>
                {data?.availableBreak ?? 0} <span className="text-xs">*</span>
              </span>
            </p>
            <p className="flex justify-between">
              <span className="font-semibold">Consumed:</span>
              <span>
                {data?.consumedBreak ?? 0} <span className="text-xs">*</span>
              </span>
            </p>
          </div>
          <div className="space-y-1">
            <p className="flex justify-between">
              <span className="font-semibold">Total Leave:</span>
              <span>{data?.totalLeave ?? 0}</span>
            </p>
            <p className="flex justify-between">
              <span className="font-semibold">Available:</span>
              <span>{data?.availableLeave ?? 0}</span>
            </p>
            <p className="flex justify-between">
              <span className="font-semibold">Consumed:</span>
              <span>{data?.consumedLeave ?? 0}</span>
            </p>
          </div>
        </div>

        {/* Footer Note */}
        <p className="text-xs text-gray-500 mt-2 font-medium text-center">
          * Time in Minutes
        </p>
      </div>

      {showLeaveDrawer && (
        <RequestLeave
          onClose={() => setShowLeaveDrawer(false)}
          onSuccess={handleLeaveRequestSuccess}
        />
      )}

      <BreakInOutModal
        isOpen={showBreakModal}
        onClose={() => setShowBreakModal(false)}
        onConfirm={confirmBreakAction}
        isOnBreak={onBreak}
      />
    </>
  );
};

export default Break;