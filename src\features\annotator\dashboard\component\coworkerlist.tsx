import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { getAnnonatorProjectDetails } from "../../annonator_api/annonator_api";
import { customAxios } from "@/utils/axio-interceptor";

// Define coworker type
type Coworker = {
  id: string;
  name: string;
  email?: string;
  accountStatus?: string;
  projects?: number;
};

// Map status values to colors
const statusColors: Record<string, string> = {
  ACTIVE: "bg-green-500",
  INACTIVE: "bg-red-500",
  SUSPENDED: "bg-red-500",
  PENDING: "bg-blue-500",
};

const CoworkerList: React.FC = () => {
  // const navigate = useNavigate();
  const location = useLocation();
  const [coworkers, setCoworkers] = useState<Coworker[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCoworkers = async () => {
      try {
        setLoading(true);

        // Check if we're on a project details page
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get('id');

        if (projectId) {
          // If we're on a project details page, get the coworkers from the project details
          const projectResponse = await getAnnonatorProjectDetails(projectId);

          if (projectResponse && projectResponse.data && projectResponse.data.coworkers && projectResponse.data.coworkers.length > 0) {
            // Use the coworker data from the project details
            const coworkerData = projectResponse.data.coworkers.map((coworker: any) => ({
              id: coworker.id,
              name: coworker.name,
              email: coworker.email,
              accountStatus: coworker.accountStatus,
              projects: 1 // We don't know the exact count, so we'll just show 1
            }));

            setCoworkers(coworkerData);
          } else {
            // If no coworkers in project details, set empty array
            setCoworkers([]);
          }
        } else {
          // If we're not on a project details page, get all coworkers
          await fetchAllCoworkers();
        }
      } catch (error) {
        console.error("Error fetching coworkers:", error);
        setCoworkers([]);
      } finally {
        setLoading(false);
      }
    };

    const fetchAllCoworkers = async () => {
      try {
        // Get coworkers data from API
        const response = await customAxios.get("/v1/coworker/get-coworkers");

        if (response.data && response.data.data && response.data.data.items) {
          // Transform the API response to match our component's data structure
          const transformedCoworkers = response.data.data.items.map((coworker: any) => ({
            id: coworker.id,
            name: coworker.name || "Unknown Coworker",
            email: coworker.email,
            accountStatus: coworker.accountStatus,
            projects: coworker.projects || 0
          }));

          // Limit to 8 coworkers for the dashboard widget
          setCoworkers(transformedCoworkers.slice(0, 8));
        } else {
          setCoworkers([]);
        }
      } catch (error) {
        console.error("Error fetching all coworkers:", error);
        setCoworkers([]);
      }
    };

    fetchCoworkers();
  }, [location.search]);

  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:h-[170px] xl-only:h-[170px] 2xl-only:h-[180px] flex flex-col">
      <div className="flex justify-between items-center lg-only:mb-2 xl-only:mb-2.5 2xl-only:mb-3">
        <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold font-poppins">Co-Workers</h2>
        
      </div>

      <ul className="CustomScroll overflow-y-auto pr-1">
        {loading ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500 text-center">Loading coworkers...</p>
        ) : coworkers.length === 0 ? (
          <p className="lg-only:text-md xl-only:text-sm 2xl-only:text-base text-gray-500 text-center">No assigned coworker</p>
        ) : (
          coworkers.map((coworker, index) => (
            <li key={coworker.id || index} className="flex items-center lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3 lg-only:mb-2 xl-only:mb-2.5 2xl-only:mb-3 last:mb-0">
              <img
                src={getAvatarUrl(coworker.name)}
                alt="coworker avatar"
                className="lg-only:w-5 lg-only:h-5 xl-only:w-5.5 xl-only:h-5.5 2xl-only:w-6 2xl-only:h-6 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-[#282828]">
                    {coworker.name.split(" ").slice(0, 2).join(" ")}
                  </p>
                  {coworker.accountStatus && (
                    <span className={`lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] lg-only:px-1 lg-only:py-0.5 xl-only:px-1.5 xl-only:py-0.5 2xl-only:px-2 2xl-only:py-0.5 rounded-full text-white ${
                      statusColors[coworker.accountStatus] || "bg-gray-500"
                    }`}>
                      {coworker.accountStatus.toLowerCase()}
                    </span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <p className="lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] text-[#727272]">
                    {coworker.projects} {coworker.projects === 1 ? "project" : "projects"}
                  </p>
                  {coworker.email && (
                    <p className="lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] text-[#727272] truncate max-w-[80px]">
                      {coworker.email}
                    </p>
                  )}
                </div>
              </div>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default CoworkerList;
