import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { UserRole } from "@/utils/constants"; // Removed the unused USER_ROLES import
import { Role } from "@/utils/permissions";

// Define the shape of the user profile object
type UserProfile = {
  name: string;
  email: string;
  role: UserRole; // Role must be one of the defined roles (ADMIN, CLIENT, etc.)
  permissions: Role | null;
  availableFrom?: string;
  availableTo?: string
};

// Define the initial state for the user slice
type UserState = {
  profile: UserProfile | null; // Profile can be null initially until the user is logged in
};

const initialState: UserState = {
  profile: null, // Initially no user is logged in
};

// Create the user slice using createSlice from Redux Toolkit
const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    // Action to set the user profile when the user logs in
    setUserProfile: (state, action: PayloadAction<UserProfile>) => {
      state.profile = action.payload;
    },

    // Action to clear the user profile (e.g., when the user logs out)
    clearUserProfile: (state) => {
      state.profile = null;
    },
  },
});

// Export the actions to be used in your components
export const { setUserProfile, clearUserProfile } = userSlice.actions;

// Export the reducer to be added to the Redux store
export default userSlice.reducer;
