import { useState, useEffect } from "react";
import { IoMdAdd } from "react-icons/io";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  getZohoDepartments,
  createZohoTicket,
  ZohoDepartment,
} from "./api/zoho-desk-api";
import { useToast } from "@/hooks/use-toast";

// Define form schema
const formSchema = z.object({
  subject: z.string().min(1, "Subject is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  status: z.string().min(1, "Status is required"),
  departmentId: z.string().min(1, "Department is required"),
  firstName: z.string().min(1, "firstName Required"),
  lastName: z.string().min(1, "lirstName Required"),
  email: z.string().email("Invalid email address").min(1, "Email is required"),
});

type FormValues = z.infer<typeof formSchema>;

const AddSupportTicket = () => {
  const [open, setOpen] = useState(false);
  const [departments, setDepartments] = useState<ZohoDepartment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: "",
      description: "",
      category: "",
      status: "",
      departmentId: "",
      firstName: "",
      lastName: "",
      email: "",
    },
  });

  // Fetch departments from Zoho Desk API
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setIsLoading(true);
        const departmentsData = await getZohoDepartments();
        console.log(departmentsData, "dept");
        setDepartments(departmentsData);
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast({
          title: "Error",
          description: "Failed to load departments. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      fetchDepartments();
    }
  }, [open, toast]);

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    try {
      const response = await createZohoTicket({
        subject: data.subject,
        description: data.description,
        departmentId: data.departmentId,
        category: data.category,
        status: data.status,
        contact: {
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
        },
      });

      toast({
        title: "Success",
        description: `Support ticket #${response.ticketNumber} has been created successfully.`,
      });

      setOpen(false);
      reset();
    } catch (error) {
      console.error("Error creating ticket:", error);
      toast({
        title: "Error",
        description: "Failed to create support ticket. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <div className="bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] flex items-center gap-2 text-white px-2 py-2 rounded-md">
          <IoMdAdd />
          <span>Add Support</span>
        </div>
      </DialogTrigger>
      <DialogContent className="overflow-y-scroll max-h-[700px]">
        <DialogHeader>
          <div className="flex justify-between">
            <DialogTitle className="text-[24px]">
              Add Support Ticket
            </DialogTitle>
          </div>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* First row - Category and Status */}
          <div className="grid grid-cols-2 gap-x-8 gap-y-4 mb-4">
            {/* Category */}
            <div>
              <Label className="text-sm mb-2 block">Category *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <select
                  {...register("category")}
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                >
                  <option value="">Select category</option>
                  <option value="technical">Technical</option>
                  <option value="billing">Billing</option>
                  <option value="general">General</option>
                </select>
              </div>
              {errors.category && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.category.message}
                </p>
              )}
            </div>

            {/* Status */}
            <div>
              <Label className="text-sm mb-2 block">Status *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <select
                  {...register("status")}
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                >
                  <option value="">Select status</option>
                  <option value="open">Open</option>
                  <option value="in-progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                </select>
              </div>
              {errors.status && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.status.message}
                </p>
              )}
            </div>

            {/* Second row - Department and Subject */}
            {/* Department */}
            <div>
              <Label className="text-sm mb-2 block">Department *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <select
                  {...register("departmentId")}
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                  disabled={isLoading || departments.length === 0}
                >
                  <option value="">
                    {isLoading ? "Loading departments..." : "Select department"}
                  </option>
                  {!isLoading &&
                    departments.map((department) => (
                      <option key={department.id} value={department.id}>
                        {department.name}
                      </option>
                    ))}
                </select>
              </div>
              {errors.departmentId && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.departmentId.message}
                </p>
              )}
            </div>

            {/* Subject */}
            <div>
              <Label className="text-sm mb-2 block">Subject *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <input
                  type="text"
                  {...register("subject")}
                  placeholder="Enter subject"
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                />
              </div>
              {errors.subject && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.subject.message}
                </p>
              )}
            </div>
          </div>

          {/* Description (full width below the grid) */}
          <div className="mb-4">
            <Label className="text-sm mb-2 block">Description *</Label>
            <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
              <textarea
                {...register("description")}
                placeholder="Enter description"
                className="bg-[#F9EFEF] w-full p-3 outline-none text-[#5E5E5E] text-sm resize-none"
                rows={4}
              />
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs mt-1">
                {errors.description.message}
              </p>
            )}
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-2 gap-x-8 gap-y-4 mb-4">
            {/* First Name */}
            <div>
              <Label className="text-sm mb-2 block">First Name *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <input
                  type="text"
                  {...register("firstName")}
                  placeholder="Enter first name"
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                />
              </div>
              {errors.firstName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.firstName.message}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <Label className="text-sm mb-2 block">Last Name *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <input
                  type="text"
                  {...register("lastName")}
                  placeholder="Enter last name"
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                />
              </div>
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.lastName.message}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="col-span-2">
              <Label className="text-sm mb-2 block">Email *</Label>
              <div className="border-gradient rounded-lg p-[2px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]">
                <input
                  type="email"
                  {...register("email")}
                  placeholder="Enter email address"
                  className="bg-[#F9EFEF] w-full p-3 h-11 outline-none text-[#5E5E5E] text-sm"
                />
              </div>
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-end mt-5">
            <DialogFooter className="gap-4">
              <Button
                variant="outline"
                size="lg"
                onClick={() => setOpen(false)}
                type="button"
              >
                Cancel
              </Button>
              <Button
                size="lg"
                className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] rounded-md text-white"
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? "Submitting..." : "SAVE"}
              </Button>
            </DialogFooter>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSupportTicket;
