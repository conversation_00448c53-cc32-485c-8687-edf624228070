import {
  Paperclip,
  SendIcon,
  Loader2,
  X,
  Image,
  FileText,
  FileSpreadsheet,
  Archive,
} from "lucide-react";
import React, { useRef, useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileUpload,
  FileUploadItem,
  FileUploadItemDelete,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
} from "@/components/ui/file-upload";
import { useQuery } from "@tanstack/react-query";
import { getGroupMember } from "../../apis/api";

interface Member {
  id: string;
  name: string;
  isOnline: boolean;
}

interface Props {
  newMessage: string;
  setNewMessage: React.Dispatch<React.SetStateAction<string>>;
  handleSend: () => void;
  handleKeyDown: (e: React.KeyboardEvent<Element>) => void;
  paperclipOpen: boolean;
  setPaperclipOpen: React.Dispatch<React.SetStateAction<boolean>>;
  disabled: boolean;
  userId: string;
  selectedUser: {
    id?: string;
    userId?: string;
    name: string;
    avatar: string;
    isGroup?: boolean;
  };
  onFileSelect: (files: File[]) => void;
  onFileUpload: (file: File) => void;
  files: File[];
}

const ChatInput: React.FC<Props> = ({
  newMessage,
  setNewMessage,
  handleSend,
  handleKeyDown,
  paperclipOpen,
  setPaperclipOpen,
  disabled,
  onFileSelect,
  //onFileUpload,
  files,
  selectedUser,
}) => {
  const dropAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const mentionDropdownRef = useRef<HTMLDivElement>(null);
  const [input, setInput] = useState(newMessage);
  const [isUploading, setIsUploading] = useState(false);
  const [mentionQuery, setMentionQuery] = useState("");
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionPosition, setMentionPosition] = useState(0);
  console.log(setIsUploading)

  const groupId = selectedUser.isGroup ? selectedUser.id : undefined;
  const isGroup = !!selectedUser.isGroup;

  const {
    data: members = [],
    isLoading: membersLoading,
    error: membersError,
  } = useQuery<Member[], Error>({
    queryKey: ["groupMembers", groupId],
    queryFn: async () => {
      if (!groupId) {
        throw new Error("Invalid group ID");
      }
      const response = await getGroupMember(groupId);
      console.log("Group members response:", response);
      return response.data.map((member: Member) => ({
        id: member.id,
        name: member.name,
        isOnline: false,
      }));
    },
    enabled: !!groupId && isGroup,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    setInput(newMessage);
  }, [newMessage]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        !target.closest("#paperclip-dropdown") &&
        !target.closest(".paperclip-button")
      ) {
        setPaperclipOpen(false);
      }
      if (
        mentionDropdownRef.current &&
        !mentionDropdownRef.current.contains(target) &&
        !target.closest(".chat-input")
      ) {
        setShowMentionDropdown(false);
        setMentionQuery("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [paperclipOpen, setPaperclipOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewMessage(value);
    setInput(value);

    const cursorPosition = e.target.selectionStart || 0;
    const textBeforeCursor = value.slice(0, cursorPosition);
    const lastAtIndex = textBeforeCursor.lastIndexOf("@");

    if (lastAtIndex !== -1 && cursorPosition > lastAtIndex) {
      const query = textBeforeCursor.slice(lastAtIndex + 1);
      if (!query.includes(" ")) {
        setMentionQuery(query);
        setShowMentionDropdown(true);
        setMentionPosition(lastAtIndex);
        return;
      }
    }
    setShowMentionDropdown(false);
    setMentionQuery("");
  };

  const handleSelectMention = (member: Member) => {
    const beforeMention = newMessage.slice(0, mentionPosition);
    const afterMention = newMessage.slice(
      mentionPosition + mentionQuery.length + 1
    );
    const newValue = `${beforeMention}@${member.name} ${afterMention}`;
    setNewMessage(newValue);
    setInput(newValue);
    setShowMentionDropdown(false);
    setMentionQuery("");
    inputRef.current?.focus();
  };

  const deduplicateFiles = React.useCallback((newFiles: File[]): File[] => {
    // Only use the newFiles array, avoiding reintroduction of existing files
    return [...newFiles]; // Simplified to just return newFiles without deduplication against current files
  }, []);

  useEffect(() => {
    const dropArea = dropAreaRef.current;
    if (!dropArea) return;

    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      dropArea.classList.add(
        "bg-gray-100",
        "border-dashed",
        "border-2",
        "border-[#FF577F]"
      );
    };

    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      dropArea.classList.add(
        "bg-gray-100",
        "border-dashed",
        "border-2",
        "border-[#FF577F]"
      );
    };

    const handleDragLeave = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      dropArea.classList.remove(
        "bg-gray-100",
        "border-dashed",
        "border-2",
        "border-[#FF577F]"
      );
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      dropArea.classList.remove(
        "bg-gray-100",
        "border-dashed",
        "border-2",
        "border-[#FF577F]"
      );

      if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
        const newFiles = Array.from(e.dataTransfer.files).filter((file) => {
          const maxSize = 10 * 1024 * 1024;
          if (file.size > maxSize) {
            alert(
              `File size exceeds 10MB limit (${(
                file.size /
                (1024 * 1024)
              ).toFixed(2)}MB)`
            );
            return false;
          }
          const isImage = file.type.startsWith("image/");
          const isPDF = file.type === "application/pdf";
          const isDocument =
            /\.(doc|docx|txt|ppt|pptx|xls|xlsx|zip|rar)$/i.test(file.name);
          if (!isImage && !isPDF && !isDocument) {
            alert(
              `Unsupported file type: ${file.type}. Please select an image, PDF, or document.`
            );
            return false;
          }
          return true;
        });
        const deduplicatedFiles = deduplicateFiles(newFiles);
        console.log(
          "Drag-and-drop files selected:",
          deduplicatedFiles.map((f) => f.name)
        );
        onFileSelect(deduplicatedFiles.slice(0, 5));
      }
    };

    dropArea.addEventListener("dragover", handleDragOver);
    dropArea.addEventListener("dragenter", handleDragEnter);
    dropArea.addEventListener("dragleave", handleDragLeave);
    dropArea.addEventListener("drop", handleDrop);

    return () => {
      dropArea.removeEventListener("dragover", handleDragOver);
      dropArea.removeEventListener("dragenter", handleDragEnter);
      dropArea.removeEventListener("dragleave", handleDragLeave);
      dropArea.removeEventListener("drop", handleDrop);
    };
  }, [onFileSelect, deduplicateFiles]);

  const getFileIcon = (file: File) => {
    const fileType = file.type;
    const fileName = file.name.toLowerCase();

    if (fileType.startsWith("image/")) {
      return <Image className="w-5 h-5" />;
    } else if (fileType === "application/pdf" || fileName.endsWith(".pdf")) {
      return <FileText className="w-5 h-5 text-red-500" />;
    } else if (
      fileType.includes("spreadsheet") ||
      fileName.endsWith(".xlsx") ||
      fileName.endsWith(".xls")
    ) {
      return <FileSpreadsheet className="w-5 h-5" />;
    } else if (
      fileType.includes("presentation") ||
      fileName.endsWith(".pptx") ||
      fileName.endsWith(".ppt")
    ) {
      return <FileSpreadsheet className="w-5 h-5" />;
    } else if (
      fileType.includes("archive") ||
      fileName.endsWith(".zip") ||
      fileName.endsWith(".rar")
    ) {
      return <Archive className="w-5 h-5" />;
    } else {
      return <FileText className="w-5 h-5" />;
    }
  };

  return (
    <div
      className="flex flex-col gap-2 border-t p-2 rounded-b-2xl bg-white"
      ref={dropAreaRef}
    >
      <div className="flex items-center gap-2 w-full">
        <FileUpload
          key={files.length} // Force re-render on file count change
          value={files}
          onValueChange={(newFiles) => {
            console.log(
              "onValueChange triggered with new files:",
              newFiles.map((f) => f.name)
            );
            const validFiles = newFiles.filter((file) => {
              const maxSize = 10 * 1024 * 1024;
              if (file.size > maxSize) {
                alert(
                  `File size exceeds 10MB limit (${(
                    file.size /
                    (1024 * 1024)
                  ).toFixed(2)}MB)`
                );
                return false;
              }
              const isImage = file.type.startsWith("image/");
              const isPDF = file.type === "application/pdf";
              const isDocument =
                /\.(doc|docx|txt|ppt|pptx|xls|xlsx|zip|rar)$/i.test(file.name);
              if (!isImage && !isPDF && !isDocument) {
                alert(
                  `Unsupported file type: ${file.type}. Please select an image, PDF, or document.`
                );
                return false;
              }
              return true;
            });
            const deduplicatedFiles = deduplicateFiles(validFiles); // Use only newFiles
            console.log(
              "Processed files after removal:",
              deduplicatedFiles.map((f) => f.name)
            );
            onFileSelect(deduplicatedFiles.slice(0, 5));
          }}
          maxFiles={5}
          maxSize={10 * 1024 * 1024}
          className="relative w-full"
          multiple
          disabled={isUploading}
        >
          <div className="relative flex w-full flex-col gap-2.5 rounded-md px-1 py-1 outline-none">
            <FileUploadList
              orientation="horizontal"
              className="overflow-x-auto px-0 py-1"
            >
              {files.map((file, index) => (
                <FileUploadItem
                  key={`${file.name}-${file.size}-${index}-${Date.now()}`} // Unique key
                  value={file}
                  className="max-w-52 p-1.5"
                >
                  <FileUploadItemPreview className="size-8 [&>svg]:size-5">
                    {file.type.startsWith("image/") ? (
                      <img
                        src={URL.createObjectURL(file)}
                        alt={file.name}
                        className="w-full h-full object-cover rounded"
                        onLoad={(e) => URL.revokeObjectURL(e.currentTarget.src)}
                      />
                    ) : (
                      getFileIcon(file)
                    )}
                  </FileUploadItemPreview>
                  <FileUploadItemMetadata size="sm" />
                  <FileUploadItemDelete asChild>
                    <Button
                      variant="secondary"
                      size="icon"
                      className="-top-1 -right-1 absolute size-4 shrink-0 cursor-pointer rounded-full"
                    >
                      <X className="size-2.5" />
                    </Button>
                  </FileUploadItemDelete>
                </FileUploadItem>
              ))}
            </FileUploadList>

            <div className="flex items-center flex-1 border rounded px-2 py-1 bg-gray-100 relative">
              <FileUploadTrigger asChild>
                <Button
                  type="button"
                  size="icon"
                  variant="ghost"
                  className="size-7 rounded-sm mr-1 paperclip-button"
                >
                  <Paperclip className="size-3.5" />
                  <span className="sr-only">Attach file</span>
                </Button>
              </FileUploadTrigger>

              <input
                ref={inputRef}
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder={
                  files.length > 0
                    ? "Add a message (optional)"
                    : "Type a message or drop files here"
                }
                className="flex-1 outline-none bg-transparent chat-input"
                disabled={disabled || isUploading}
              />

              <button
                onClick={() => {
                  console.log(
                    "Send button clicked, files to send:",
                    files.map((f) => f.name)
                  );
                  handleSend();
                }}
                className="bg-[#FF577F] text-white p-2 rounded-md ml-2 flex items-center justify-center"
                disabled={
                  disabled ||
                  isUploading ||
                  (!newMessage.trim() && files.length === 0)
                }
                aria-label="Send message"
              >
                {isUploading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <SendIcon className="w-4 h-4" />
                )}
              </button>

              {showMentionDropdown && isGroup && (
                <div
                  ref={mentionDropdownRef}
                  className="absolute bottom-12 left-10 bg-white border rounded-md shadow-lg max-h-40 overflow-y-auto z-10 w-48"
                >
                  {membersLoading ? (
                    <div className="px-4 py-2 text-gray-500">Loading...</div>
                  ) : membersError ? (
                    <div className="px-4 py-2 text-red-500">
                      Error loading members
                    </div>
                  ) : members.length === 0 ? (
                    <div className="px-4 py-2 text-gray-500">
                      No members found
                    </div>
                  ) : (
                    members
                      .filter((member) =>
                        member.name
                          .toLowerCase()
                          .includes(mentionQuery.toLowerCase())
                      )
                      .map((member) => (
                        <div
                          key={member.id}
                          className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center gap-2"
                          onClick={() => handleSelectMention(member)}
                        >
                          <span>{member.name}</span>
                          {member.isOnline && (
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          )}
                        </div>
                      ))
                  )}
                </div>
              )}
            </div>
          </div>
        </FileUpload>
      </div>
    </div>
  );
};

export default ChatInput;
