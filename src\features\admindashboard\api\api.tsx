import { customAxios } from "@/utils/axio-interceptor";

export const fetchAllAdmins = async () => {
  try {
    const response = await customAxios.get(`/v1/admin/get-all-admin`);
    if (response.data?.status === 1) {
      return response.data.data; // returns array of admins
    } else {
      return [];
    }
  } catch (error) {
    console.error("Error fetching admins:", error);
    return [];
  }
};

// Create Task API
export const createTaskApi = async (taskData: any) => {
  try {
    const response = await customAxios.post(
      "/v1/admin-tasks/create-task",
      taskData
    ); // Replace with your actual endpoint
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getTasks = async () => {
  try {
    const response = await customAxios.get("/v1/admin-tasks/get-all-task");
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(
      `/v1/admin-tasks/get-single-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateTaskStatus = async (taskId: string, taskData: any) => {
  try {
    const response = await customAxios.put(
      `/v1/admin-tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/admin-tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};

// src/types.ts
export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  status: string;
  createdAt?: string;
  priority?: string;
  name?: string; // Some APIs might return name instead of title
};

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};




//topsection dashboard total numbers list
export const getAdminTopDashboardData = async () => {
  try {
    const response = await customAxios.get("/v1/dashboard/admin-dashboard");
    return response.data;
  } catch (error) {
    console.error("Error in getAdminDashboardData:", error);
    throw error;
  }
};
// filter 1.totalprojects, 2.coordinatorCount, 3.annotatorCount, 4.clientCount