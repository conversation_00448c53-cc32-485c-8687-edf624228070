// /pages/DashboardPage.tsx
import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
// import TaskRecord from "@/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/taskrecord";
import { getProjectDetails } from "@/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails_api/projectdetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { BackButton, NoData } from "@/_components/common";
import { baseUrl } from "@/globalurl/baseurl";
import AdminTaskRecord from "../admindashboard/detailsadmin/admintask";
import CoordinatorAnnotatorDetails from "./annotatordetails";
import ClientDescription from "./clientcoordinator";
import CoordintatorProjectDetailsKanban from "./coordinator-projects-details/coordintator_details_kan";

// Interface for project details
interface ProjectData {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  createdBy: {
    name: string;
  };
  annotator: {
    name: string;
    email: string;
  };
  tasks: any[];
}

const CoordinatorProjectDetails: React.FC = () => {
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Helper function to format date to dd/mm/yy
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = String(date.getFullYear()).slice(-2);
      return `${day}/${month}/${year}`;
    } catch (error) {
      return "Invalid date";
    }
  };

  // Helper function to calculate duration in weeks
  const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return "N/A";
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const diffWeeks = Math.ceil(diffDays / 7);
      return `${diffWeeks} ${diffWeeks === 1 ? "Week" : "Weeks"}`;
    } catch (error) {
      return "N/A";
    }
  };

  // Helper function to get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority?.toUpperCase()) {
      case "HIGH":
        return "bg-red-500";
      case "MEDIUM":
        return "bg-orange-500";
      case "LOW":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Helper function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case "COMPLETED":
        return "bg-[#009A51]";
      case "PENDING":
        return "bg-yellow-500";
      case "IN_PROGRESS":
      case "INPROGRESS":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Helper function to format status text
  const formatStatus = (status: string) => {
    if (!status) return "Unknown";

    // Replace underscores with spaces and capitalize each word
    return status
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Function to handle attachment download
  const handleDownloadAttachment = (attachmentPath: string) => {
    if (!attachmentPath) return;

    // Check if attachmentPath is an absolute URL (starts with http:// or https://)
    const isAbsoluteUrl = /^https?:\/\//i.test(attachmentPath);
    let attachmentUrl = attachmentPath;

    // If it's not an absolute URL, prepend baseUrl
    if (!isAbsoluteUrl) {
      const formattedBaseUrl = baseUrl.endsWith("/")
        ? baseUrl.slice(0, -1)
        : baseUrl;
      const formattedPath = attachmentPath.startsWith("/")
        ? attachmentPath
        : `/${attachmentPath}`;
      attachmentUrl = `${formattedBaseUrl}${formattedPath}`;
    }

    console.log("Downloading attachment from URL:", attachmentUrl);

    const link = document.createElement("a");
    link.href = attachmentUrl;
    link.target = "_blank";
    link.rel = "noopener noreferrer";
    const fileName = attachmentPath.split("/").pop() || "attachment";
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  // Get project ID from URL parameters
  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get project ID from URL query parameters
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get("id");

        // Log the project ID for debugging
        console.log("Project ID from URL:", projectId);

        // If no project ID is provided, use a default or show an error
        if (!projectId) {
          console.error("No project ID provided in URL");
          setError(
            "Project ID is missing. Please go back and select a project."
          );
          setLoading(false);
          return;
        }

        // Fetch project details using the API
        console.log("Fetching project details for ID:", projectId);
        const response = await getProjectDetails(projectId);
        console.log("API response:", response);

        if (response && response.status === 1 && response.data) {
          console.log("Project details:", response.data);
          setProject(response.data);
        } else {
          console.error("Invalid API response format:", response);
          setError("Failed to load project details. Please try again later.");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        setError("An error occurred while loading project details.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [location.search]);

  // Show loading state
  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message={error} />
      </div>
    );
  }

  // Show no data state if project is null
  if (!project) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message="No project details available" />
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col">
      <div className="lg-only:px-2 xl-only:px-4 2xl-only:px-6">
        <div className="flex flex-col lg-only:flex-col xl-only:flex-row 2xl-only:flex-row justify-between lg-only:justify-between xl-only:justify-between 2xl-only:justify-between lg-only:gap-y-4 xl-only:gap-x-10 2xl-only:gap-x-14 items-start">
          {/* top section */}
          <div className="flex flex-col lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5 w-full lg-only:w-full xl-only:w-[78%] 2xl-only:w-[83%]">
            <div className="flex lg-only:flex-col xl-only:items-center 2xl-only:items-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-3">
              {/* title */}
              <div className="flex lg-only:flex-wrap xl-only:flex-nowrap 2xl-only:flex-nowrap lg-only:gap-x-1 xl-only:gap-x-1 2xl-only:gap-x-1 lg-only:items-center xl-only:items-center 2xl-only:items-center">
                <BackButton />
                <h1 className="text-[#122539] font-poppins font-semibold lg-only:text-lg xl-only:text-xl 2xl-only:text-2xl">
                  {project.name}
                </h1>
              </div>
              <div className="flex lg-only:gap-2 xl-only:gap-3 2xl-only:gap-3">
                {/* priority */}
                <span
                  className={`text-white lg-only:text-[10px] xl-only:text-xs 2xl-only:text-xs lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-1 2xl-only:px-2 2xl-only:py-1 rounded-full ${getPriorityColor(
                    project.priority
                  )}`}
                >
                  {project.priority}
                </span>
                {/* status */}
                <span
                  className={`text-white lg-only:text-[10px] xl-only:text-xs 2xl-only:text-xs lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-1 2xl-only:px-2 2xl-only:py-1 rounded-full ${getStatusColor(
                    project.status
                  )}`}
                >
                  {formatStatus(project.status)}
                </span>
              </div>
            </div>
            {/* Description */}
            <div className="w-full">
              <h1 className="text-[#122539] font-sans font-normal text-sm lg:text-sm xl:text-sm 2xl:text-xl break-words whitespace-normal">
                {project.description}
              </h1>
            </div>
            {/* duration, start date, end date */}
            <div className="flex lg-only:flex-col xl-only:flex-row 2xl-only:flex-row lg-only:gap-2 xl-only:justify-between 2xl-only:justify-between items-center">
              <div className="flex lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5 items-center lg-only:w-full xl-only:w-auto 2xl-only:w-auto lg-only:justify-between xl-only:justify-start 2xl-only:justify-start">
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  Duration
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  {calculateDuration(project.startDate, project.dueDate)}
                </p>
              </div>
              <div className="flex lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5 items-center lg-only:w-full xl-only:w-auto 2xl-only:w-auto lg-only:justify-between xl-only:justify-start 2xl-only:justify-start">
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  Start Date
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  {formatDate(project.startDate)}
                </p>
              </div>
              <div className="flex lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5 items-center lg-only:w-full xl-only:w-auto 2xl-only:w-auto lg-only:justify-between xl-only:justify-start 2xl-only:justify-start">
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  End Date
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-[14px]">
                  {formatDate(project.dueDate)}
                </p>
              </div>
            </div> 
            <div className="mt-3">
              <CoordintatorProjectDetailsKanban projectId={project.id} />
            </div>
          </div>
          <div className="lg-only:w-full lg-only:mt-4 xl-only:w-[30%] 2xl-only:w-[30%] flex lg-only:flex-row xl-only:flex-col 2xl-only:flex-col lg-only:items-start lg-only:justify-between xl-only:items-center xl-only:justify-between 2xl-only:items-center 2xl-only:justify-between lg-only:gap-2 xl-only:gap-0 2xl-only:gap-4 lg-only:flex-wrap ">
            <div className="flex flex-col gap-1 w-full">
              <button
                onClick={() => navigate("/coordinator/chat")}
                className="lg-only:w-[48%] xl-only:w-full 2xl-only:w-full lg-only:p-2 xl-only:p-2.5 2xl-only:p-3 lg-only:text-xs xl-only:text-sm 2xl-only:text-sm font-medium bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-md"
              >
                Chat
              </button>
              <button
                className="w-full lg:flex-1 xl:w-full 2xl:w-full p-2 lg:p-3 xl:p-3 2xl:p-4 text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium border-gradient text-[#FF577F] rounded-md"
                onClick={() => {
                  if (project.attachment && project.attachment.length > 0) {
                    handleDownloadAttachment(project.attachment[0]);
                  } else {
                    alert("No attachment available for this project");
                  }
                }}
                disabled={
                  !project.attachment || project.attachment.length === 0
                }
              >
                {project.attachment && project.attachment.length > 0
                  ? "Download Attachment"
                  : "No Attachment"}
              </button>
            </div>
            <div className="lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:flex lg-only:flex-row lg-only:flex-wrap xl-only:flex-col 2xl-only:flex-col lg-only:items-start lg-only:justify-between xl-only:items-end xl-only:justify-end 2xl-only:items-end 2xl-only:justify-end lg-only:gap-2 xl-only:gap-20 2xl-only:gap-4 xl:mt-4 lg:mt-0 2xl:mt-7">
              <div className="lg-only:w-[32%] xl-only:w-full 2xl-only:w-full xl:rounded-md lg:rounded-md 2xl:rounded  ">
                <AdminTaskRecord projectId={project.id} />
              </div>
              <div className="lg-only:w-[32%] xl-only:w-full 2xl-only:w-full xl-only:mt-4 2xl-only:mt-5">
                <CoordinatorAnnotatorDetails />
              </div>
              <div className="lg-only:w-[32%] xl-only:w-[100%] 2xl-only:w-full xl-only:mt-4 2xl-only:mt-5">
                <ClientDescription />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoordinatorProjectDetails;
