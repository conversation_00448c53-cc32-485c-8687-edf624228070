import logo from "@/assets/darklogo.png";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { AttendanceType } from "../attendace.type";

/**
 * Generate and download a PDF report of attendance history
 * @param attendanceData - Array of attendance records
 * @param annotatorName - Name of the annotator
 */
export const generateAttendancePDF = (
  attendanceData: AttendanceType[],
  annotatorName: string
): void => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Add logo at top-left with increased width
  try {
    // Convert the imported logo to a data URL if needed
    doc.addImage(logo, 'PNG', 14, 10, 40, 15);  
  } catch (error) {
    console.error("Error adding logo to PDF:", error);
    // Continue without the logo if there's an error
  }


  // Add title
  doc.setFontSize(14);
  doc.setFont("helvetica", "normal");
  doc.text(`${annotatorName}'s Attendance Report`, 14, 35);

  // Add current date
  const today = new Date();
  const formattedDate = `${today.getDate().toString().padStart(2, '0')}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getFullYear()}`;
  doc.setFontSize(10);
  doc.text(`Generated on: ${formattedDate}`, 14, 42);

  // Add the table data
  const tableColumn = ["Date", "Time In", "Time Out", "Arrival", "Break Hours", "Working Hours"];
  const tableRows = attendanceData.map(item => [
    item.date,
    item.timein,
    item.timeout,
    item.arrival,
    item.breakhours,
    item.workinghours,
  ]);

  autoTable(doc, {
    head: [tableColumn],
    body: tableRows,
    startY: 50,
    styles: {
      fontSize: 9,
      cellPadding: 3,
    },
    headStyles: {
      fillColor: [255, 87, 127], // #FF577F
      textColor: [255, 255, 255],
      fontStyle: 'bold',
    },
    alternateRowStyles: {
      fillColor: [245, 245, 245],
    },
  });

  // Get the final y position after the table
  const finalY = (doc as any).lastAutoTable.finalY || 150;

  // Add a horizontal line (border-t) above the footer
  doc.setDrawColor(200, 200, 200); // Light gray color for the border
  doc.setLineWidth(0.5);
  doc.line(14, finalY + 10, doc.internal.pageSize.width - 14, finalY + 10);

  // Add footer with contact information in a more professional layout
  const footerY = finalY + 20;

  // Footer title
  doc.setFontSize(11);
  doc.setFont("helvetica", "bold");
  doc.text("Contact Information", 14, footerY);

  // Footer content in a row-like layout
  doc.setFontSize(9);
  doc.setFont("helvetica", "normal");

  // Create a row-like layout by positioning text at specific x-coordinates
  const pageWidth = doc.internal.pageSize.width;
  const col1 = 14;                  // First column
  const col2 = pageWidth / 3;       // Second column
  const col3 = (pageWidth * 2) / 3; // Third column

  // Company info in first column
  doc.setFont("helvetica", "bold");
  doc.text("Company:", col1, footerY + 10);
  doc.setFont("helvetica", "normal");
  doc.text("Magance", col1, footerY + 15);

  // Address in second column
  doc.setFont("helvetica", "bold");
  doc.text("Address:", col2, footerY + 10);
  doc.setFont("helvetica", "normal");
  doc.text("Noida-62", col2, footerY + 15);

  // Email in third column
  doc.setFont("helvetica", "bold");
  doc.text("Email:", col3, footerY + 10);
  doc.setFont("helvetica", "normal");
  doc.text("<EMAIL>", col3, footerY + 15);

  // Add page number at the bottom
  const pageCount = (doc as any).internal.pages.length - 1;
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width / 2, doc.internal.pageSize.height - 10, { align: 'center' });
  }

  // Save the PDF
  doc.save(`${annotatorName.replace(/\s+/g, '_')}_attendance_report.pdf`);
};