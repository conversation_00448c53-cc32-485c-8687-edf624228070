// src/components/AdminColumn.tsx
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { FaEdit } from "react-icons/fa";
import { RiDeleteBin6Fill } from "react-icons/ri";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON>alogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import EditFeatures from "../../modal/editfeatures";
import { FeaturedType } from "./attendancetype";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteFeature } from "../../../api/api";
import { toast } from "react-toastify";
import { useRef } from "react";

export const useAdminColumns = (): ColumnDef<FeaturedType>[] => {
  const queryClient = useQueryClient();
  const dialogCloseRef = useRef<HTMLButtonElement>(null);

  const deleteMutation = useMutation({
    mutationFn: deleteFeature,
    onSuccess: async () => {
      if (dialogCloseRef.current) {
        dialogCloseRef.current.click();
      }
      toast.success("Feature deleted successfully");
      await queryClient.refetchQueries({
        queryKey: ["Features"],
        exact: false, // Match partial keys like ["Features", { limit }]
        type: "active",
      });
    },
    onError: (error) => {
      toast.error("Failed to delete feature");
      console.error("Delete error:", error);
    },
  });

  const handleDelete = (id: string) => {
    const previousData = queryClient.getQueryData<any>(["Features"]);
    if (previousData && previousData.pages) {
      queryClient.setQueryData(["Features"], {
        ...previousData,
        pages: previousData.pages.map((page: any) => ({
          ...page,
          data: page.data.filter((feat: any) => feat.id !== id),
        })),
      });
    }
    deleteMutation.mutate(id);
  };

  return [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <div
          className="w-[50px] cursor-pointer text-[16px] font-medium"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          S.No.
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[16px] font-normal pl-2 mb-2">{row.index + 1}</div>
      ),
    },
    {
      accessorKey: "rule",
      header: ({ column }) => (
        <div
          className="w-[100px] text-[16px] font-medium cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Features
        </div>
      ),
      cell: ({ row }) => (
        <div className="w-full text-[16px] font-normal mb-2">{row.getValue("rule")}</div>
      ),
    },
    {
      accessorKey: "Package.name",
      header: ({ column }) => (
        <div
          className="w-[100px] text-[16px] font-medium cursor-pointer"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Package name
        </div>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[16px] font-normal mb-4">{row.original.Package?.name || "N/A"}</div>
      ),
    },
    {
      accessorKey: "Actions",
      header: () => <div className="pl-6 text-[16px] font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <div className="flex flex-row gap-2 pl-3">
            <div className="px-2 text-[18px] font-medium cursor-pointer hover:text-[#398bc2]">
              <Dialog>
                <DialogTrigger asChild>
                  <FaEdit />
                </DialogTrigger>
                <DialogContent>
                  <EditFeatures
                    feature={{
                      id: row.original.featureId || row.original.id,
                      rule: row.original.rule,
                      packageIds: row.original.packageIds || [],
                    }}
                  />
                </DialogContent>
              </Dialog>
            </div>
            <div className="px-2 text-xl text-[#ff3c3c] hover:text-[#eb6767] cursor-pointer">
              <Dialog>
                <DialogTrigger asChild>
                  <RiDeleteBin6Fill />
                </DialogTrigger>
                <DialogContent className="px-[2rem] py-14">
                  <DialogHeader>
                    <DialogTitle className="text-[24px]">
                      Do you want to delete this feature?
                    </DialogTitle>
                  </DialogHeader>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button
                        type="button"
                        className="border-gradient bg-white hover:bg-[#faf5f5] px-14 py-6 text-black"
                      >
                        Cancel
                      </Button>
                    </DialogClose>
                    <Button
                      variant="gradient"
                      className="px-[4rem] py-6"
                      onClick={() => handleDelete(row.original.featureId || row.original.id)}
                      disabled={deleteMutation.isPending}
                    >
                      {deleteMutation.isPending ? "Deleting..." : "Delete"}
                    </Button>
                    <DialogClose ref={dialogCloseRef} className="hidden" />
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        );
      },
    },
  ];
};