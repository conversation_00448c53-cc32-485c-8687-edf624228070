// import { Route, Routes } from "react-router-dom";
// // import DashboardPageRoutes from "@/features/layout/dashboard-layout";
// import ChatRoutes from "@/features/chat/routes";
// import { ClientDashboardPage } from "@/features/clientdashboard";
// import DashboardSidebar from "@/features/layout/dashboard/newdahboard-layout";
// import FAQs from "@/features/settings/faqs";
// import TaskDetails from "@/features/clientdashboard/task-detail-page/components/taskdetails";
// import ProjectDetails from "@/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails";
// import Annotatorroutespage from "@/features/annotator";
// import { AdminDashboardPage } from "@/features/admindashboard";
// import SettingFile from "@/features/settings/setting-file/index";
// import AdminFAQs from "@/features/admindashboard/components/adminfaqs";
// import AddOn from "@/features/clientdashboard/add-on/component/addon";
// import AdminTaskDetails from "@/features/admindashboard/detailsadmin/AdminTaskDetails";
// // import Attendacelog from "@/features/clientdashboard/attendance.log";
// // import AdminAttendance from "@/features/admindashboard/detailsadmin/annonatoradmin/attendance.log";
// import AdminCoodinator from "@/features/admindashboard/detailsadmin/coodinatoradmin/components/attendance.log";
// import AdminClients from "@/features/admindashboard/detailsadmin/clientadmin/components/attendance.log";
// import AdminOnboard from "@/features/admindashboard/onboard/attendance.log";
// // import ProjectManagement from "@/features/annotator/dashboard/projectmangement";
// import AttendanceAnnotator from "@/features/annotator/dashboard/attendence";
// // import SettingFile from "@/features/admindashboard/components/settingadmin";
// import NotificationFaqs from "@/features/admindashboard/notification";
// import MatchMakingmain from "@/features/admindashboard/matchmaking";
// import UserAllList from "@/features/admindashboard/userall";
// import SettingFileadmin from "@/features/admindashboard/components/settingadmin";
// import PackageAdmin from "@/features/admindashboard/packageadmin";
// import AdminsList from "@/features/admindashboard/admin-list";

// export const ProtectedRoutes = () => {
//   return (
//     <Routes>
//       {/* <Route element={<DashboardPageRoutes />}> */}
//       <Route element={<DashboardSidebar />}>
//         {/* client dashboards */}
//         <Route path="/" element={<ClientDashboardPage />} />
//         <Route path="/chat" element={<ChatRoutes />} />
//         <Route path="/task-details" element={<TaskDetails />} />
//         <Route path="/faq" element={<FAQs />} />
//         <Route path="/settings" element={<SettingFile />} />
//         {/* <Route path="attendance" element={<ClientDashboardPage />} /> */}
//         <Route path="project-details" element={<ProjectDetails />} />
//         <Route path="addon" element={<AddOn />} />
//         <Route path="/attendance" element={<AttendanceAnnotator />} />
//         {/*dashboard annotator routes start here  */}
//         <Route path="/annotator/*" element={<Annotatorroutespage />} />
//         {/* <Route path="/project-management" element={<ProjectManagement />} /> */}
//         {/* admin dashboard  admindetails*/}
//         <Route path="/admin" element={<AdminDashboardPage />} />
//         <Route path="/adminsettings" element={<SettingFileadmin />} />
//         <Route path="/faqsadmin" element={<AdminFAQs />} />
//         <Route path="/admindetails" element={<AdminTaskDetails />} />
//         <Route path="/admincoodinator" element={<AdminCoodinator />} />
//         <Route path="/adminclients" element={<AdminClients />} />
//         <Route path="/onboard" element={<AdminOnboard />} />
//         <Route path="/notifications" element={<NotificationFaqs />} />
//         <Route path="/match-making" element={<MatchMakingmain />} />
//         <Route path="/total-users" element={<UserAllList />} />
//         <Route path="/package-admin" element={<PackageAdmin />} />
//         <Route path="/admins-list" element={<AdminsList />} />
//         {/* <Route path="/faqs" element={<FAQs />} /> */}
//       </Route>
//     </Routes>
//   );
// };

import { Route, Routes } from "react-router-dom";
import { ClientDashboardPage } from "@/features/clientdashboard";
import DashboardSidebar from "@/features/layout/dashboard/newdahboard-layout";
// import Annotatorroutespage from "@/features/annotator";
import { AdminDashboardPage } from "@/features/admindashboard";

export const ProtectedRoutes = () => {
  return (
    <Routes>
      <Route element={<DashboardSidebar />}>
        {/* Client Dashboard */}
        <Route path="/" element={<ClientDashboardPage />} />

      

        {/* Admin Dashboard */}
        <Route path="/admin" element={<AdminDashboardPage />} />

        {/* Project Coordinator Dashboard */}
        {/* <Route
          path="/coordinator"
          element={<ProjectCoordinatorDashboardPage />}
        /> */}
      </Route>
    </Routes>
  );
};
