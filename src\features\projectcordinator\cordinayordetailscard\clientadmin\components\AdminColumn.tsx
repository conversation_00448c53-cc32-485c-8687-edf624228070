"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { AttendanceType } from "./attendancetype";
import { useNavigate } from "react-router-dom"; // ✅ React Router import

export const useAdminColumns = (): ColumnDef<AttendanceType>[] => {
  const navigate = useNavigate(); // ✅ Initialize navigation hook

  return [
    {
      accessorKey: "projectname",
      header: () => (
        <div
          className="w-[200px] font-medium cursor-pointer"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Project Name
        </div>
      ),
      cell: ({ row }) => <div>{row.getValue("projectname")}</div>,
    },
    {
      accessorKey: "starton",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Started on
        </Button>
      ),
      cell: ({ row }) => <div className="pl-4">{row.getValue("starton")}</div>,
    },
    {
      accessorKey: "duration",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Duration
        </Button>
      ),
      cell: ({ row }) => <div className="pl-4">{row.getValue("duration")}</div>,
    },
    {
      accessorKey: "priority",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Priority
        </Button>
      ),
      cell: ({ row }) => (
        <div>
          <span className="bg-[#2525AB] px-4 py-2 rounded-3xl text-white">
            {row.getValue("priority")}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "postedby",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Posted by
        </Button>
      ),
      cell: ({ row }) => <div className="pl-4">{row.getValue("postedby")}</div>,
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4">
          <span className="border border-[#E96B1C] px-4 py-2 rounded-3xl text-[#E96B1C]">
            {row.getValue("status")}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: () => (
        <div className="pl-4">
          <Button
            variant={"gradient"}
            className="px-7 rounded-xl"
            onClick={() => navigate("/dashboard/project-details")} // ✅ Navigate to route
          >
            View Details
          </Button>
        </div>
      ),
    },
  ];
};
