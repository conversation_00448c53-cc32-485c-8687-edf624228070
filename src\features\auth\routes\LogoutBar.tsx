"use client";
import { useState } from "react";
import { LogOut } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import ConfirmationModal from "@/features/auth/routes/common/ConfirmationButton";
import { RootState } from "@/store";
import { logout } from "@/store/slices/authSlice";
import { Logout } from "../api/client-api";

const LogoutBar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const user = useSelector((state: RootState) => state.auth.user);
  if (!user) return null;

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleLogout = async () => {
    try {
      // ✅ Step 1: Call logout API (cookie clear)
      await Logout();

      // ✅ Step 2: Clear Redux user state
      dispatch(logout());

      // ✅ Step 3: Close modal and redirect
      setIsModalOpen(false);
      navigate("/auth/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Optional: Toast error message yahan laga sakte ho
    }
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Support tab: Only for CLIENT or COWORKER */}
      {/* {(user.role === "CLIENT" || user.role === "COWORKER") && (
        <a
          href=""
          target="_blank"
          rel="noopener noreferrer"
        >
          <div className="flex flex-row gap-2 justify-start text-[#757575] font-medium text-[16px] ml-4 items-center">
            <CircleHelp />
            <p>Support</p>
          </div>
        </a>
      )} */}

      {/* Logout tab: Available for ALL users */}
      <div
        onClick={() => setIsModalOpen(true)}
        className="p-2 px-3 py-3 flex items-center shadow-[0px_4px_5.1px_-1px_#00000024] bg-[#F6F6F6] rounded-md cursor-pointer transition-all duration-300 hover:bg-red-50 dark:hover:bg-red-900"
      >
        <div className="flex items-center w-full justify-between">
          <span className="font-poppins text-md text-[#616060] pl-4 font-semibold">
            Logout
          </span>
          <LogOut className="text-[#FF577F]" />
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <ConfirmationModal
        open={isModalOpen}
        onClose={handleCloseModal}
        title="Are you sure you want to logout?"
        onConfirm={handleLogout}
      />
    </div>
  );
};

export default LogoutBar;
