import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { deleteOnboard } from "../../api/onboarding.api";

interface DeleteModalProps {
  userId: string;
}

const DeleteModal = ({ userId }: DeleteModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (error) {
    return <div>Error: {error}</div>;
  }
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Delete Account
        </Button>
      </DialogTrigger>
      <DialogContent className="py-5 flex flex-col gap-6 justify-center items-center">
        <DialogHeader>
          <DialogTitle className="text-xl">
            Do you want to delete this account?
          </DialogTitle>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              className="border-gradient bg-white hover:bg-[#faf5f5] px-14 py-6 text-black"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant="gradient"
            className="px-[4rem] py-6"
            disabled={isLoading}
            onClick={async () => {
              try {
                setIsLoading(true);
                setError(null);

                await deleteOnboard(userId);

                toast.success("User deleted successfully");

                // Close dialog
                const closeButton = document.querySelector(
                  "[data-dialog-close]"
                ) as HTMLElement;
                if (closeButton) closeButton.click();

                // Refresh page
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              } catch (err: any) {
                const errorMessage =
                  err.response?.data?.message || "Failed to delete admin";
                console.error("Error deleting admin:", err);
                setError(errorMessage);
                toast.error(errorMessage);
              } finally {
                setIsLoading(false);
              }
            }}
          >
            {isLoading ? "Processing..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteModal;
