export type AttendanceStatus = "ACTIVE" | "INACTIVE" | "ON_BREAK";
export type ArrivalStatus = "ON_TIME" | "LATE" | "HOURS_LATE";

export type AttendanceRecord = {
  id: string;
  userId: string;
  date: string;
  timeIn: string;
  timeOut: string | null;
  status: AttendanceStatus;
  arrivalStatus: ArrivalStatus;
  breakMinutes: number;
  workingMinutes: number;
  totalLeave: number;
  availableLeave: number;
  consumedLeave: number;
  totalBreak: number;
  availableBreak: number;
  consumedBreak: number;
  createdAt: string;
  updatedAt: string;
};
