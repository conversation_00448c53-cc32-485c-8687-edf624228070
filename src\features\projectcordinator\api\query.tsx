import { useInfiniteQuery } from "@tanstack/react-query";
import { getClientsData } from "./api";
import { PaginatedResponse } from "@/types/generics";
import { Assignment } from "../types/client.types";

export const useCoordinatorClientList = (
    filter: "developer" | "client" | "both"
  ) => {
    return useInfiniteQuery<PaginatedResponse<Assignment>, Error>({
      queryKey: ["coordinator-clients", filter],
      queryFn: getClientsData,
      getNextPageParam: (lastPage) =>
        lastPage.hasNextPage ? lastPage.nextPage : undefined,
      initialPageParam: 1,
    });
  };
  
