import { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useSuspendAdminMutation } from "../adminlist_api/adminList_mutations";

type FormValues = {
  suspensionPeriod: "24h" | "7d" | "30d" | "always";
};

const SuspendModalAdminlist = ({
  userId,
  onSuccessRefresh,
}: {
  userId: string;
  onSuccessRefresh: () => void;
}) => {
  const { mutate: suspendAdmin, isLoading } = useSuspendAdminMutation();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [showToast, setShowToast] = useState(false);
  const { handleSubmit, watch, setValue } = useForm<FormValues>({
    defaultValues: {
      suspensionPeriod: "24h",
    },
  });

  {
    showToast && (
      <div>
        <p>Admin suspended successfully!</p>
      </div>
    );
  }
  const onSubmit = (data: FormValues) => {
    console.log("Selected suspension period:", data.suspensionPeriod);
    console.log("Suspending admin with ID:", userId);

    // Call the suspendAdmin API with the selected duration
    suspendAdmin(
      {
        id: userId,
        duration: data.suspensionPeriod,
      },
      {
        onSuccess: () => {
          setShowToast(true);
          onSuccessRefresh();
          closeRef.current?.click();
          setTimeout(() => setShowToast(false), 3000);
        },
      }
    );
  };

  const selectedValue = watch("suspensionPeriod");

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Suspend Account
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col gap-y-3"
        >
          <DialogHeader className="flex flex-col gap-y-20 items-center">
            <DialogTitle className="text-xl text-center font-semibold">
              Do you want to suspend this account?
            </DialogTitle>
          </DialogHeader>

          <div className="flex justify-center my-4">
            <RadioGroup
              value={selectedValue}
              onValueChange={(value) =>
                setValue(
                  "suspensionPeriod",
                  value as FormValues["suspensionPeriod"]
                )
              }
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="24h" id="24h" />
                <Label htmlFor="24h">24 hours</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="7d" id="7d" />
                <Label htmlFor="7d">7 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="30d" id="30d" />
                <Label htmlFor="30d">30 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="always" id="always" />
                <Label htmlFor="always">Always</Label>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter className="flex justify-center gap-3">
            <DialogClose asChild>
              <Button
                variant="ghost"
                className="border-gradient text-black px-14 py-2"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              variant="gradient"
              className="text-white px-14 py-2"
              disabled={isLoading}
            >
              {isLoading ? "Processing..." : "Suspend"}
            </Button>
          </DialogFooter>
          {/* Hidden DialogClose for programmatic closing */}
          <DialogClose asChild>
            <button ref={closeRef} className="hidden" />
          </DialogClose>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SuspendModalAdminlist;
