// components/ReCAPTCHAComponent.tsx
import React from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";

const ReCAPTCHAComponent: React.FC = () => {
  const handleCaptcha = (value: string | null) => {
    console.log("Captcha value:", value);
  };

  return (
    <div className="mt-4">
      <ReCAPTCHA
        sitekey="6Lcu7hMrAAAAAIdmou9qJV5s3vCRic1qvZPdgA1x"
        onChange={handleCaptcha}
      />
    </div>
  );
};

export default ReCAPTCHAComponent;
