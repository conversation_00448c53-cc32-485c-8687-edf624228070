import React, { useEffect } from 'react';

interface CustomToastProps {
  title: string;
  message: string;
  type?: 'success' | 'error' | 'info';
  onClose: () => void;
  duration?: number; // in milliseconds
}

const CustomToast: React.FC<CustomToastProps> = ({
  title,
  message,
  type = 'success',
  onClose,
  duration = 4000,
}) => {
  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [onClose, duration]);

  const bgColor = {
    success: 'bg-green-100 text-green-100',
    error: 'bg-red-100 text-red-800',
    info: 'bg-green-100 text-blue-800',
  };

  return (
    <div className={`relative rounded-md p-4 shadow-md ${bgColor[type]} flex items-start gap-2 w-80`}>
      <span className="mt-1 w-6 h-6 flex items-center justify-center rounded-full bg-[#006644] text-white text-sm">
        ✓
      </span>
      <div className="flex-1">
        <p className="font-semibold text-[#172B4D] text-[16px]">{title}</p>
        <p className="font-thin text-[#172B4D] text-[14px]">{message}</p>
      </div>
      <button
        onClick={onClose}
        className="absolute top-2 right-2 text-sm text-gray-500 hover:text-black"
      >
        ✕
      </button>
    </div>
  );
};

export default CustomToast;
