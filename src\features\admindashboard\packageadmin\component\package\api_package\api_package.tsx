// api/packageApi.ts
import { customAxios } from "@/utils/axio-interceptor";
import { PackageData } from "./packagetype";

export const createPackageApi = async (data: PackageData) => {
  const response = await customAxios.post("v1/packages/create-package", data);
  return response.data;
};



// api/packageApi.ts
export const getAllPackages = async () => {
    const response = await customAxios.get("/v1/packages/get");
    return response.data;
  };


  //update

  export const updatePackageApi = async (id: string, updatedData: any) => {
    const response = await customAxios.patch(`v1/packages/update-package/${id}`, updatedData);
    return response.data;
  };
  

  //delete
  export const deletePackageApi = async (id: string) => {
    const response = await customAxios.delete(`v1/packages/delete-package/${id}`);
    return response.data;
  };