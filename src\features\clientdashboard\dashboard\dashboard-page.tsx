// /pages/DashboardPage.tsx
import React, { useEffect, useState } from "react";
import { FiUsers } from "react-icons/fi";
import { GiSandsOfTime } from "react-icons/gi";
import { BsBagDash } from "react-icons/bs";
import { MdOutlineAddToPhotos } from "react-icons/md";
import KanbanBoard from "../components/kanbandashboard";
import ProjectList from "../components/project";
import AnnotatorList from "../components/annotators";
import { getClientDashboardData } from "../api/api";
import { Link } from "react-router-dom";

type DashboardData = {
  activeAnnotators: number;
  activeProjects: number;
  expiringOn: string | null;
  addOnPackages: number;
};

const Dashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    activeAnnotators: 0,
    activeProjects: 0,
    expiringOn: null,
    addOnPackages: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response = await getClientDashboardData();
        console.log("Dashboard API response:", response); // Log the full response

        // Check if response has data directly or nested in a data property
        if (response && response.data) {
          setDashboardData(response.data);
        } else if (response) {
          // If data is not nested, use the response directly
          setDashboardData(response);
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format expiry date for display
  const formatExpiryDate = (dateString: string | null) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  return (
    <div className="w-full flex flex-col gap-4 lg:px-6 xl:px-8 2xl:px-10">
      <div className="w-full">
        {/* Top section - 2x2 grid for 1024px, 4 columns for larger screens */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-4 justify-center gap-4 lg:gap-6 xl:gap-8 2xl:gap-10 mt-2">
          {/* Card 1 - Active Annotators */}
          <Link to="/dashboard/task-details/annotators">
            <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
              <span className="flex items-center gap-3">
                <FiUsers className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
                <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                  Active annotators
                </p>
              </span>
              <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
                {loading ? "Loading..." : dashboardData.activeAnnotators}
              </p>
            </div>
          </Link>

          {/* Card 2 - Active Projects */}
          <Link to="/dashboard/task-details/projects">
            <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
              <span className="flex items-center gap-3">
                <BsBagDash className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
                <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                  Active Projects
                </p>
              </span>
              <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
                {loading ? "Loading..." : dashboardData.activeProjects}
              </p>
            </div>
          </Link>

          {/* Card 3 - Expiring On */}
          <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
            <span className="flex items-center gap-3">
              <GiSandsOfTime className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
              <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                Expiring On
              </p>
            </span>
            <p className="text-[#545454] font-semibold lg:text-sm xl:text-base 2xl:text-lg">
              {loading
                ? "Loading..."
                : formatExpiryDate(dashboardData.expiringOn)}
            </p>
          </div>

          {/* Card 4 - Add Ons */}
          <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
            <span className="flex items-center gap-3">
              <MdOutlineAddToPhotos className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
              <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                Total Subscription
              </p>
            </span>
            <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
              {loading ? "Loading..." : dashboardData.addOnPackages}
            </p>
          </div>
        </div>

        {/* Bottom section - Responsive layout using Tailwind */}
        <div className="mt-5">
          {/* For 1024px-1439px: Keep the existing layout (full width Kanban, lists at bottom) */}
          <div className="lg:block xl:hidden">
            <div className="flex flex-col gap-6 justify-center items-center">
              {/* Kanban Board - Full Width */}
              <div className="w-full">
                <KanbanBoard />
              </div>

              {/* Sidebar Lists - At Bottom */}
              <div className="w-full flex flex-row gap-4 mt-4 ">
                <div className="w-1/2">
                  <AnnotatorList />
                </div>
                <div className="w-1/2">
                  <ProjectList />
                </div>
              </div>
            </div>
          </div>

          {/* For 1440px and above: Side-by-side layout with screen-height kanban */}
          <div className="hidden xl:block">
            <div className="flex flex-row justify-between gap-12">
              {/* Kanban Board with screen height */}
              <div className="w-3/4">
                <div className="xl:h-[calc(100vh-250px)] 2xl:h-[calc(100vh-280px)]">
                  <KanbanBoard />
                </div>
              </div>

              {/* Sidebar Lists - Fixed heights */}
              <div className="w-1/4 flex flex-col gap-6">
                <div>
                  <AnnotatorList />
                </div>
                <div>
                  <ProjectList />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
