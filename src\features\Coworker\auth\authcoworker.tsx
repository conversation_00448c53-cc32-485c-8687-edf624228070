import React, { useState, useEffect } from "react";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";
import Logo from "@/assets/darklogo.png";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  handleCoworkerSignup,
  validateSignupForm,
} from "../components/validate.signup";

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreePolicy: boolean;
}

export const AuthCoworker = () => {
  const location = useLocation();
  const navigate = useNavigate();
  // Get parameters from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const emailFromUrl = queryParams.get("email") || "";
  const tokenFromUrl = queryParams.get("token") || ""; // Added: Get token from URL
  // const clientOwnerId = queryParams.get("clientId") || "";
  // const permissionRole = queryParams.get("permission") || "VIEW";

  const [formData, setFormData] = useState<SignupFormData>({
    firstName: "",
    lastName: "",
    email: emailFromUrl,
    password: "",
    confirmPassword: "",
    agreePolicy: false,
  });

  // Update email if URL changes
  useEffect(() => {
    if (emailFromUrl) {
      setFormData((prev) => ({
        ...prev,
        email: emailFromUrl,
      }));
    }
  }, [emailFromUrl]);

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validate = (): boolean => {
    const newErrors = validateSignupForm(formData);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;

    setIsSubmitting(true);

    await handleCoworkerSignup({
      formData: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
      },
      tokenFromUrl,
      setErrors,
      setIsSubmitting,
      onSuccess: () => {
        // Show success message
        toast.success("Invite accepted successfully! Redirecting to login...");

        // Navigate after a short delay
        setTimeout(() => {
          navigate("/auth/login", { replace: true }); // Using navigate with replace
        }, 2000);
      },
    });
  };

  return (
    <div className="w-full flex items-center justify-center overflow-hidden ">
      <ToastContainer position="top-right" autoClose={3000} />
      <div className="w-full transition-transform duration-300 ">
        <div className="flex flex-row justify-between w-full h-screen">
          {/* Logo Container */}
          <div className="h-screen p-4 flex flex-col">
            <img
              src={Logo}
              alt="Logo"
              className="w-44"
            />
          </div>

          {/* Main Content Container */}
          <div className="flex flex-row w-full gap-8 lg:gap-8 xl:gap-16 2xl:gap-24">
            {/* Left Side - Form */}
            <div className="w-[40%] flex items-center justify-center signup-form">
              <div className="w-full mt-14 lg:mt-14 xl:mt-16 2xl:mt-20">
                <div className="mx-auto max-w-xl lg:max-w-xl xl:max-w-2xl 2xl:max-w-3xl min-h-[350px] lg:min-h-[350px] xl:min-h-[380px] 2xl:min-h-[400px] shadow-[0px_3px_48px_10px_#0000000F] px-6 py-3 lg:px-6 lg:py-3 xl:px-8 xl:py-5 2xl:px-10 2xl:py-6 rounded-2xl">
                  <h2 className="text-[24px] lg:text-[24px] xl:text-[28px] 2xl:text-[32px] font-bold font-inter text-[#282828] mb-2 mt-2 lg:mb-2 lg:mt-2 xl:mb-3 xl:mt-2 2xl:mb-4 2xl:mt-2">
                    Accept Coworker Invite
                  </h2>

                  {errors.apiError && (
                    <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
                      {errors.apiError}
                    </div>
                  )}

                  {/* Added warning if token is missing */}
                  {!tokenFromUrl && (
                    <div className="mb-4 p-3 bg-yellow-100 text-yellow-700 rounded-lg">
                      Warning: No invitation token found in URL. This invitation
                      may be invalid.
                    </div>
                  )}

                  <form
                    onSubmit={handleSubmit}
                    className="space-y-3 lg:space-y-3 xl:space-y-4 2xl:space-y-5 w-full"
                  >
                    {/* First and Last Name - No changes here */}
                    <div className="flex gap-4 lg:gap-4 xl:gap-5 2xl:gap-6">
                      <div className="w-1/2">
                        <label className="block text-[14px] lg:text-[14px] xl:text-[15px] 2xl:text-[16px] font-medium text-[#757575] mb-1 lg:mb-1 xl:mb-1.5 2xl:mb-2">
                          First Name*
                        </label>
                       <div className="border-gradient rounded-lg">
                         <input
                          type="text"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          className={`w-full p-2 lg:p-2 xl:p-2.5 2xl:p-3 bg-[#F9EFEF] rounded-lg focus:outline-none  ${
                            errors.firstName ? "border-red-500" : ""
                          }`}
                          placeholder="First Name"
                        />
                       </div>
                        {errors.firstName && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.firstName}
                          </p>
                        )}
                      </div>
                      <div className="w-1/2">
                        <label className="block text-[14px] lg:text-[14px] xl:text-[15px] 2xl:text-[16px] font-medium text-[#757575] mb-1 lg:mb-1 xl:mb-1.5 2xl:mb-2">
                          Last Name*
                        </label>
                        <div className="border-gradient rounded-lg">
                        <input
                          type="text"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          className={`w-full p-2 lg:p-2 xl:p-2.5 2xl:p-3  bg-[#F9EFEF] rounded-lg focus:outline-none  ${
                            errors.lastName ? "border-red-500" : ""
                          }`}
                          placeholder="Last Name"
                        />
                        </div>
                        {errors.lastName && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.lastName}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Email - No changes */}
                    <div>
                      <label className="block text-[14px] lg:text-[14px] xl:text-[15px] 2xl:text-[16px] font-medium text-[#757575] mb-1 lg:mb-1 xl:mb-1.5 2xl:mb-2">
                        Email*
                      </label>
                       <div className="border-gradient rounded-lg">
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full p-2 lg:p-2 xl:p-2.5 2xl:p-3  ${
                          !!emailFromUrl ? "bg-gray-100" : "bg-[#F9EFEF]"
                        } rounded-lg focus:outline-none ${
                          errors.email ? "border-red-500" : ""
                        }`}
                        placeholder="Enter your email"
                        readOnly={!!emailFromUrl}
                      />
                      </div>
                      {!!emailFromUrl && (
                        <div className="mt-1">
                          <p className="text-green-600 text-xs">
                            Email from invite link
                          </p>
                        </div>
                      )}
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.email}
                        </p>
                      )}
                    </div>

                    {/* Password & Confirm Password - No changes */}
                    <div className="flex gap-4 lg:gap-4 xl:gap-5 2xl:gap-6">
                      <div className="w-1/2">
                        <label className="block text-[14px] lg:text-[14px] xl:text-[15px] 2xl:text-[16px] font-medium text-[#757575] mb-1 lg:mb-1 xl:mb-1.5 2xl:mb-2">
                          Password*
                        </label>
                        <div className="relative">
                          <div className="border-gradient rounded-lg">
                          <input
                            type={showPassword ? "text" : "password"}
                            name="password"
                            value={formData.password}
                            onChange={handleChange}
                            className={`w-full p-2 lg:p-2 xl:p-2.5 2xl:p-3 pr-10  bg-[#F9EFEF] rounded-lg focus:outline-none ${
                              errors.password ? "border-red-500" : ""
                            }`}
                            placeholder="Enter your password"
                            autoComplete="new-password"
                          />
                          </div>
                          <div
                            className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <Eye size={18} />
                            ) : (
                              <EyeOff size={18} />
                            )}
                          </div>
                        </div>
                        {errors.password && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.password}
                          </p>
                        )}
                      </div>

                      <div className="w-1/2">
                        <label className="block text-[14px] lg:text-[14px] xl:text-[15px] 2xl:text-[16px] font-medium text-[#757575] mb-1 lg:mb-1 xl:mb-1.5 2xl:mb-2">
                          Confirm Password*
                        </label>
                        <div className="relative">
                           <div className="border-gradient rounded-lg">
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            name="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleChange}
                            className={`w-full p-2 lg:p-2 xl:p-2.5 2xl:p-3 pr-10  bg-[#F9EFEF] rounded-lg focus:outline-none ${
                              errors.confirmPassword ? "border-red-500" : ""
                            }`}
                            placeholder="Confirm your password"
                            autoComplete="new-password"
                          />
                          </div>
                          <div
                            className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
                            onClick={() =>
                              setShowConfirmPassword(!showConfirmPassword)
                            }
                          >
                            {showConfirmPassword ? (
                              <Eye size={18} />
                            ) : (
                              <EyeOff size={18} />
                            )}
                          </div>
                        </div>
                        {errors.confirmPassword && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.confirmPassword}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Terms and Conditions Checkbox - No changes */}
                    <div className="space-y-2 lg:space-y-2 xl:space-y-2.5 2xl:space-y-3">
                      <div className="flex items-start gap-2 lg:gap-2 xl:gap-2.5 2xl:gap-3">
                        <input
                          type="checkbox"
                          name="agreePolicy"
                          checked={formData.agreePolicy}
                          onChange={handleChange}
                          className="mt-1 w-4 lg:w-4 xl:w-4.5 2xl:w-5 h-4 lg:h-4 xl:h-4.5 2xl:h-5"
                        />
                        <p className="text-sm lg:text-sm xl:text-[15px] 2xl:text-base text-gray-600">
                          I agree to the{" "}
                          <Link
                            to="/terms"
                            className="text-red-500 hover:underline"
                          >
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link
                            to="/privacy"
                            className="text-red-500 hover:underline"
                          >
                            Privacy Policy
                          </Link>
                          .
                        </p>
                      </div>
                      {errors.agreePolicy && (
                        <p className="text-red-500 text-sm">
                          {errors.agreePolicy}
                        </p>
                      )}
                    </div>

                    {/* Submit Button - No changes */}
                    <div className="flex flex-row items-center">
                      <button
                        type="submit"
                        disabled={isSubmitting || !tokenFromUrl} // Disable if no token
                        className={`px-10 lg:px-10 xl:px-10 2xl:px-12 py-3 lg:py-3 xl:py-3.5 2xl:py-4 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all flex items-center justify-center ${
                          isSubmitting || !tokenFromUrl
                            ? "opacity-70 cursor-not-allowed"
                            : ""
                        }`}
                      >
                        {isSubmitting ? (
                          "Processing..."
                        ) : (
                          <>
                            <span className="text-sm lg:text-sm xl:text-base 2xl:text-lg">
                              Accept Invitation
                            </span>
                            <MoveRight className="ml-2 lg:ml-2 xl:ml-2 2xl:ml-2 w-4 lg:w-4 xl:w-4.5 2xl:w-5 h-4 lg:h-4 xl:h-4.5 2xl:h-5" />
                          </>
                        )}
                      </button>
                    </div>
                  </form>

                  {/* Login Link - No changes */}
                  {/* <p className="text-center mt-3 lg:mt-3 xl:mt-3.5 2xl:mt-4 text-sm lg:text-sm xl:text-[15px] 2xl:text-base text-gray-600">
                    Already have an account?{" "}
                    <Link
                      to="/auth/login"
                      className="text-red-500 font-semibold hover:text-blue-600"
                    >
                      Log in
                    </Link>
                  </p> */}
                </div>
              </div>
            </div>

            {/* Right Side - Background Component - No changes */}
            <div className="w-[55%] h-screen">
              <AuthCommonComponent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
