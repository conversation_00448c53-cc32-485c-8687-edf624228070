import React from "react";
import img1 from "@/assets/authbackground.png";
import { useResponsive } from "@/hooks/use-responsive";
import ResponsiveImage from "@/components/ui/responsive-image";

export const AuthCommonComponent: React.FC = () => {
  const { isLaptopMd, isLaptopLg } = useResponsive();

  // Adjust text sizes based on screen size
  const getTitleSize = () => {
    if (isLaptopLg) return "text-5xl"; // 4K/2560px
    if (isLaptopMd) return "text-4xl"; // 1440px
    return "text-3xl"; // 1024px (default)
  };

  const getSubtitleSize = () => {
    if (isLaptopLg) return "text-xl"; // 4K/2560px
    if (isLaptopMd) return "text-lg"; // 1440px
    return "text-base"; // 1024px (default)
  };

  return (
    <div className="relative min-h-screen w-full overflow-hidden">
      {/* Background image using ResponsiveImage for better control */}
      <div className="absolute inset-0 z-0">
        <ResponsiveImage
          src={img1}
          alt="Background"
          containerClassName={`w-full h-full ${isLaptopLg ? 'flex items-center justify-center' : ''}`}
        />
      </div>

      {/* Content overlay */}
      <div className="relative z-10 flex flex-col justify-center items-center min-h-screen w-full">
        <div className={`text-center px-4 ${isLaptopLg ? 'max-w-3xl' : isLaptopMd ? 'max-w-xl' : 'max-w-md'}`}>
          <h2 className={`${getTitleSize()} font-bold mb-6 leading-snug text-white`}>
            Unlock the Power of <br /> Annotation!
          </h2>
          <p className={`${getSubtitleSize()} text-white`}>
            Hire Expert Annotators on a Flexible Subscription Basis!
          </p>
        </div>
      </div>
    </div>
  );
};
