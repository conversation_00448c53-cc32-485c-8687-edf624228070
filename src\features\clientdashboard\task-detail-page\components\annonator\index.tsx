import { useState, useEffect } from "react";
import Shift<PERSON>hange from "./shiftchange";
import CustomToast from "@/_components/common/customtoast";

import AnnotatorList from "./AnnotatorList";
import { ClientAnnonatorAll } from "./clientannonator_api/clientannonator_api";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";

// Define the annotator type
interface AnnotatorProps {
  id?: string;
  name: string;
  email: string;
  shiftTiming: string;
  projects: string;
  joiningDate: string;
  subscription: string;
  image: string;
  status: string;
  packageId?: string;
  availableFrom?: string | null;
  availableTo?: string | null;
}

export default function ClientAnnotators() {
  // navigate is now handled by the AnnotatorCard component
  const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAnnotator, setSelectedAnnotator] =
    useState<AnnotatorProps | null>(null);
  const [showToast, setShowToast] = useState(false);
  // const { isLaptopMd, isLaptopLg } = useResponsive();
  const [annotators, setAnnotators] = useState<AnnotatorProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch annotators from API
  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        setLoading(true);
        console.log("Starting to fetch annotators...");

        // Use the ClientAnnonatorAll API to get annotator data
        const response = await ClientAnnonatorAll(user);
        console.log("Raw API response:", response);

        // Check if we have data in the expected format
        if (response && response.data && Array.isArray(response.data)) {
          const annotatorData = response.data;
          console.log("Found annotator data:", annotatorData);

          // Format the annotator data
          const formattedAnnotators = annotatorData.map((annotator: any) => {
            return {
              id: annotator.id,
              name: annotator.name || "Unknown",
              email: annotator.email || "No email",
              shiftTiming: annotator.availableFrom || "0",
              projects: annotator._count?.annotatorProjects?.toString() || "0",
              joiningDate: annotator.createdAt,
              subscription: "Standard", // Will be replaced by package name in the card
              packageId: annotator.packageId,
              image: annotator.profileImage || "", // Will be generated in the card component
              status: annotator.accountStatus || "active",
              availableFrom: annotator.availableFrom,
              availableTo: annotator.availableTo,
            };
          });

          console.log("Formatted annotators:", formattedAnnotators);

          if (formattedAnnotators.length > 0) {
            setAnnotators(formattedAnnotators);
          } else {
            console.log("No annotators found in the response");
            setAnnotators([]);
          }
        } else {
          console.log("No annotator data found in the response");

          // TEMPORARY: Use dummy data for testing UI when API returns empty array
          // Remove this in production
          const dummyAnnotators = [
            {
              id: "dummy1",
              name: "John Doe",
              email: "<EMAIL>",
              shiftTiming: "09:00",
              projects: "2",
              joiningDate: "2023-04-15T00:00:00.000Z",
              subscription: "Standard",
              packageId: "standard-package",
              image: "",
              status: "active",
              availableFrom: "09:00",
              availableTo: "17:00",
            },
            {
              id: "dummy2",
              name: "Jane Smith",
              email: "<EMAIL>",
              shiftTiming: "10:00",
              projects: "3",
              joiningDate: "2023-03-20T00:00:00.000Z",
              subscription: "Premium",
              packageId: "premium-package",
              image: "",
              status: "active",
              availableFrom: "10:00",
              availableTo: "18:00",
            },
          ];

          console.log("Using dummy data for testing:", dummyAnnotators);
          setAnnotators(dummyAnnotators);
        }
      } catch (error) {
        console.error("Failed to fetch annotators:", error);
        setError("Failed to load annotators. Please try again later.");
        setAnnotators([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnotators();
  }, []);

  // Helper function to format date
  // const formatDate = (dateString: string) => {
  //   try {
  //     const date = new Date(dateString);
  //     return `${date.getDate()} ${getMonthName(date.getMonth())} ${date.getFullYear()}`;
  //   } catch (e) {
  //     return "Invalid date";
  //   }
  // };

  // Helper function to get month name
  // const getMonthName = (monthIndex: number) => {
  //   const months = [
  //     "January", "February", "March", "April", "May", "June",
  //     "July", "August", "September", "October", "November", "December"
  //   ];
  //   return months[monthIndex];
  // };

  const openModal = (annotator: AnnotatorProps) => {
    setSelectedAnnotator(annotator);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedAnnotator(null);
  };

  const handleSuccess = () => {
    setShowToast(true);
  };

  // Handle error state
  if (error) {
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  // Handle loading state
  if (loading) {
    return <div className="p-4">Loading annotators...</div>;
  }

  return (
    <div className="relative">
      {annotators && annotators.length > 0 ? (
        <AnnotatorList
          annotators={annotators.map((annotator, index) => ({
            id: annotator.id || index.toString(),
            name: annotator.name,
            email: annotator.email,
            image: annotator.image,
            status: annotator.status,
            shiftTiming: annotator.shiftTiming,
            projects: annotator.projects,
            joiningDate: annotator.joiningDate,
            subscription: annotator.subscription,
            packageId: annotator.packageId,
            availableFrom: annotator.availableFrom,
            availableTo: annotator.availableTo,
          }))}
          openModal={openModal}
        />
      ) : (
        <div className="text-lg font-medium  mt-10 text-gray-500 flex items-center justify-center text-center ">
          <span>There are no annotators available at the moment.</span>
        </div>
      )}

      {/* Modal Open Hoga Yaha */}

      {/* ✅ Modal */}
      {isModalOpen && selectedAnnotator && (
        <ShiftChange
          onClose={closeModal}
          onSuccess={handleSuccess}
          annotatorId={selectedAnnotator.id} // Make sure to pass the annotator ID
        />
      )}

      {/* ✅ Toast */}
      {showToast && (
        <div className="fixed top-5 right-5 z-[100]">
          <CustomToast
            title="Shift Change Requested"
            message="Your shift change request was submitted successfully."
            type="success"
            duration={4000}
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
