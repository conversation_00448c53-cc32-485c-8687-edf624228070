import { customAxios } from "@/utils/axio-interceptor";
import { useQuery } from "@tanstack/react-query";
// import { usegetFeaturedataList } from "./api";

export const usegetFeaturedataList = ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ["Features", { page, limit }],
    queryFn: async () => {
      const response = await customAxios.get(
        "/v1/packages/get-package-features",
        {
          params: {
            page,
            limit,
          },
        }
      );

      return {
        data: response.data.data, // Updated to match API response structure
        totalCount: response.data.data.totalCount,
        totalPages: response.data.data.totalPages,
        currentPage: response.data.data.currentPage,
      };
    },
  });
};
