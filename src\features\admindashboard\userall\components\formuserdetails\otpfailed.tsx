// @ts-ignore
import React from "react";

interface OtpFailedProps {
  emailVerified: string | null;
  email?: string;
}

const OtpFailed: React.FC<OtpFailedProps> = ({ emailVerified, email }) => {
  // Format date if emailVerified exists
  const formatVerificationDate = () => {
    if (!emailVerified) return;

    const date = new Date(emailVerified);
    return `Email verified on ${date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })} at ${date.toLocaleTimeString("en-GB", {
      hour: "2-digit",
      minute: "2-digit",
    })}`;
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">User Activity</h2>
      <div className=" rounded-lg overflow-hidden border-gradient">
        <div className="bg-[#F9EFEF] p-3 text-sm font-medium">
          Email Verification Status
        </div>
        <div className="p-3 bg-[#FFF9FA]">
          <p>{formatVerificationDate()}</p>
          {email && (
            <p className="text-sm text-gray-500 mt-1">Email: {email}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default OtpFailed;
