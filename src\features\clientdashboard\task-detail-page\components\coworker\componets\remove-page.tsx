"use client";

import React from "react";

interface RemoveModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  showToast: (
    title: string,
    message: string,
    type?: "success" | "error" | "info"
  ) => void;
}

const RemoveModal: React.FC<RemoveModalProps> = ({
  open,
  onClose,
  onConfirm,
  showToast,
}) => {
  if (!open) return null;
  const handleConfirm = () => {
    onConfirm(); // Close modal + handle remove logic
    showToast("Removed", "Access has been successfully removed.", "success");
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Are you sure you want to remove access?
        </h2>

        <div className="mt-6 flex justify-center space-x-3">
          <button
            onClick={onClose} // ✅ Modal close karega
            className="px-10 py-2 border border-[#FF577F] text-gray-900 dark:text-white rounded-lg"
          >
            No
          </button>

          <button
            onClick={handleConfirm} // ✅ Remove Confirm karega
            className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]  text-white px-10 py-2 text-lg rounded-lg"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};

export default RemoveModal;
