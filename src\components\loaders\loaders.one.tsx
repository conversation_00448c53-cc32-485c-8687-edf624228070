// src/components/BrandedGlobalLoader.tsx
import React from "react";
import { motion, AnimatePresence } from "framer-motion";

type LoaderProps = {
  isLoading: boolean;
  color?: string; // Tailwind color class suffix, like "blue-500"
};

const BrandedGlobalLoader: React.FC<LoaderProps> = ({
  isLoading,
  color = "blue-500",
}) => {
  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-white/50 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className={`relative w-20 h-20`}
          >
            {/* Outer Spinner */}
            <div
              className={`absolute inset-0 rounded-full border-4 border-t-transparent animate-spin border-${color}`}
            />
            {/* Middle Ring */}
            <div
              className={`absolute inset-2 rounded-full border-4 border-b-transparent animate-spin-slower border-${color.replace(
                "500",
                "300"
              )}`}
            />
            {/* Inner Pulse */}
            <div
              className={`absolute inset-6 rounded-full bg-${color} animate-pulse`}
            />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BrandedGlobalLoader;
