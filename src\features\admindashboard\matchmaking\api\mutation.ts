// import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify"; // if you are using react-toastify
// // wherever you handle errors
// import { createUser, resetPassword, suspendUser } from "./matchmaking.api";
// import { errorMessage } from "@/utils/errorHandler";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { assignAnnotatorCoordinator } from "./matchmaking.api";
import { errorMessage } from "@/utils/errorHandler";

// interface CreateUserInput {
//   name: string;
//   email: string;
//   domain: string;
//   role: string;
//   packageId: string;
//   password?: string | null;
// }

// export function useAddUserMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: CreateUserInput) => createUser(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("User added successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

// export function useResetPasswordMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: { id: string; password: string | null }) =>
//       resetPassword(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("Password reset successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

// export function useSuspendUserMutation() {
//   const queryClient = useQueryClient();

//   return useMutation({
//     mutationFn: (content: { id: string; suspendedUntil: number | null }) =>
//       suspendUser(content),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("User suspended successfully");
//         await queryClient.invalidateQueries({ queryKey: ["packages"] }); // Update queryKey accordingly
//       }
//     },
//   });
// }

export function useUpdateAnnotatorCoordinatorMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: {
      clientId: string;
      developerId: string;
      coordinatorId: string;
      availableFrom: string | null;
      availableTo: string | null;
      packageId: string | null | undefined;
    }) => assignAnnotatorCoordinator(content),
    onSettled: async (_data, error) => {
      if (error) {
        errorMessage(error);
      } else {
        toast.success("Annotator and coordinator assigned successfully");
        // Invalidate both matchmaking and packages queries to refresh the data
        await queryClient.invalidateQueries({ queryKey: ["matchmaking"] });
        await queryClient.invalidateQueries({ queryKey: ["packages"] });
      }
    },
  });
}
