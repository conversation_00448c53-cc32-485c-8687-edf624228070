import { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
// import Delete from './components/delete';
import { ChangePassword } from '@/features/admindashboard/components/faq_setting_api/setting/setting_api';
import { BackButton } from '@/_components/common';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Button } from '@/components/ui/button';

export default function SettingFileadmin() {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  const toggleVisibility = (field: 'old' | 'new' | 'confirm') => {
    setShowPassword((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const validatePassword = (password: string) => {
    const validations: string[] = [];
    
    // Check for spaces
    if (/\s/.test(password)) {
      validations.push('Must not contain spaces');
    }
    
    if (!/[A-Z]/.test(password)) {
      validations.push('At least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      validations.push('At least one lowercase letter');
    }
    
    if (!/[0-9]/.test(password)) {
      validations.push('At least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      validations.push('At least one special character');
    }
    
    if (password.length < 8) {
      validations.push('Minimum 8 characters');
    }
    
    return validations;
  };

  const handleSave = async () => {
    const newErrors: Record<string, string> = {};

    // Validate old password
    if (!oldPassword.trim()) {
      newErrors.oldPassword = 'Old password is required';
    } else if (/\s/.test(oldPassword)) {
      newErrors.oldPassword = 'Old password cannot contain spaces';
    }

    // Validate new password
    if (!newPassword.trim()) {
      newErrors.newPassword = 'New password is required';
    } else {
      const passwordErrors = validatePassword(newPassword);
      if (passwordErrors.length > 0) {
        newErrors.newPassword = passwordErrors.join(', ');
      }
    }

    // Validate confirm password
    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = 'Confirm password is required';
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    // Prevent same old and new password
    if (oldPassword && newPassword && oldPassword === newPassword) {
      newErrors.newPassword = 'New password must be different from old password';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      const response = await ChangePassword({ 
        oldPassword, 
        newPassword, 
        confirmPassword 
      });

      toast.success(response.message || 'Password changed successfully!', {
        position: 'top-right',
        autoClose: 3000,
      });

      // Reset form
      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setErrors({});
    } catch (error: any) {
      let errorMessage = 'Something went wrong. Please try again.';
      
      // Handle different error formats
      if (error.response) {
        errorMessage = error.response.data?.message || 
                      error.response.data?.error || 
                      error.response.statusText;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, {
        position: 'top-right',
        autoClose: 3000,
      });
    }
  };

  const renderInput = (
    label: string,
    value: string,
    setValue: (v: string) => void,
    error: string | undefined,
    show: boolean,
    toggle: () => void,
    type: 'old' | 'new' | 'confirm'
  ) => {
    const labelClass = {
      old: 'gap-[5rem]',
      new: 'gap-[4.5rem]',
      confirm: 'gap-[3rem]',
    };

    return (
      <div className={`flex flex-row items-center justify-start ${labelClass[type]}`}>
        <label className="block font-medium">{label}</label>
        <div className="flex flex-col">
          <div className="relative w-[416.25px] border-gradient rounded-md">
            <input
              type={show ? 'text' : 'password'}
              value={value}
              onChange={(e) => {
                setValue(e.target.value);
                // Clear error when typing
                if (errors[`${type}Password`]) {
                  setErrors(prev => ({ ...prev, [`${type}Password`]: '' }));
                }
              }}
              className={`w-full p-2 bg-[#F9EFEF] rounded-md pr-10 focus:ring focus:outline-none ${
                error ? 'border-red-500 border' : ''
              }`}
              placeholder={`Enter ${label}`}
              autoComplete="new-password"
            />
            <span
              onClick={toggle}
              className="absolute right-2 top-2.5 cursor-pointer text-[#cea6b7]"
            >
              {show ? <Eye size={20} /> : <EyeOff size={20} />}
            </span>
          </div>
          {error && (
            <p className="text-red-500 text-sm mt-1 max-w-[416px]">
              {error.split(', ').map((err, i) => (
                <span key={i} className="block">• {err}</span>
              ))}
            </p>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="px-14 w-full font-sans">
      <div className='flex gap-3 mb-3'>
        <BackButton/>
        <h2 className="text-2xl font-bold">Settings</h2>
      </div>

      <div className="rounded-xl bg-[#F9EFEF] p-6 shadow-md border-gradient">
        <h3 className="text-lg font-semibold mb-4 text-[#5E5E5E]">Password & Security</h3>

        <div className="space-y-6">
          {renderInput(
            'Old Password:',
            oldPassword,
            setOldPassword,
            errors.oldPassword,
            showPassword.old,
            () => toggleVisibility('old'),
            'old'
          )}
          {renderInput(
            'New Password:',
            newPassword,
            setNewPassword,
            errors.newPassword,
            showPassword.new,
            () => toggleVisibility('new'),
            'new'
          )}
          {renderInput(
            'Confirm Password:',
            confirmPassword,
            setConfirmPassword,
            errors.confirmPassword,
            showPassword.confirm,
            () => toggleVisibility('confirm'),
            'confirm'
          )}

          <div className="flex flex-row items-end justify-end">
            <Button
            variant={"gradient"}
              onClick={handleSave}
              disabled={Object.keys(errors).some(key => errors[key] !== '')}
              className=" text-white font-semibold px-10 py-2 rounded-md hover:opacity-90 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Delete Account Section */}
      {/* <div className="flex flex-col gap-0 mt-4">
        <Delete />
      </div> */}
    </div>
  );
}