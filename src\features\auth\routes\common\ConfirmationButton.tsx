"use client";
import React, { useState } from "react";
import GradientButton from "@/_components/common/GrafientButton"; // Import reusable button

interface ConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  title: string;
  onConfirm: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  onClose,
  title,
  onConfirm,
}) => {
  const [checked, setChecked] = useState(false);

  if (!open) return null; // Hide modal when not open

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg w-96">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          {title}
        </h2>

        <div className="mt-4 flex items-center">
          <input
            type="checkbox"
            id="customCheckbox"
            checked={checked}
            onChange={() => setChecked(!checked)}
            className="w-6 h-6 rounded-full border-2 border-[#808080] cursor-pointer appearance-none 
              flex items-center justify-center relative checked:bg-white checked:border-pink-500 transition-all duration-300 
              checked:after:content-[''] checked:after:w-4 checked:after:h-4 
              checked:after:bg-pink-500 checked:after:rounded-full checked:after:absolute"
          />
          <label
            htmlFor="customCheckbox"
            className="ml-2 text-[#4B4B4B] text-sm"
          >
            Log out from other devices
          </label>
        </div>

        <div className="mt-6 flex justify-center space-x-3">
          <button
            onClick={onClose} // Closes the modal
            className="px-10 py-2 border border-[#FF577F] text-gray-900 dark:text-white rounded-lg"
          >
            No
          </button>

          {/* Gradient Button for Logout */}
          <GradientButton
            text="Logout"
            onClick={onConfirm}
            className="px-10 py-2 text-lg"
          />
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
