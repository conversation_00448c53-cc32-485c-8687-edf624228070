import { Component, ReactNode } from "react";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  errorCode: number;
}

export class ErrorBoundary extends Component<Props, State> {
  state: State = {
    hasError: false,
    errorCode: 500,
  };

  static getDerivedStateFromError(error: any) {
    let code = 500; // Default

    if (error && error.statusCode) {
      code = error.statusCode;
    }

    return { hasError: true, errorCode: code };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error("ErrorBoundary caught an error", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorPage errorCode={this.state.errorCode} />;
    }

    return this.props.children;
  }
}

// Lazy load the ErrorPage (optional for performance)
import { ErrorPage } from "./ErrorPage";
