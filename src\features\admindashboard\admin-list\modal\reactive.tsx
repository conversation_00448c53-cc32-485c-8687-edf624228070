import { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useActivateAdminMutation } from "../adminlist_api/adminList_mutations";
import { toast } from "react-toastify";

interface ReactiveModalAdminListProps {
  userId: string;
  onSuccessRefresh?: () => void; // Parent ko refresh karne ke liye callback
}

const ReactiveModalAdminList = ({ userId, onSuccessRefresh }: ReactiveModalAdminListProps) => {
  const { mutate: activateAdmin } = useActivateAdminMutation();
  const [loading, setLoading] = useState(false);
  const dialogCloseRef = useRef<HTMLButtonElement>(null);

  const handleReactivate = () => {
    setLoading(true);
    activateAdmin(userId, {
      onSuccess: () => {
        setLoading(false);
        

        // Modal close karo
        dialogCloseRef.current?.click();

        // Data refresh karo agar callback diya hai
        if (onSuccessRefresh) onSuccessRefresh();
      },
      onError: () => {
        setLoading(false);
        toast.error("Failed to reactivate admin account");
      },
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Re-activate Account
        </Button>
      </DialogTrigger>

      <DialogContent className="flex flex-col gap-8 ">
        <DialogHeader className="flex flex-row justify-center">
          <DialogTitle className="text-xl text-center text-[#282828]">
            Do you want to reactivate this account?
          </DialogTitle>
        </DialogHeader>

        <DialogFooter className="flex gap-x-3">
          <DialogClose asChild>
            <Button
              ref={dialogCloseRef}
              variant="ghost"
              className="border-gradient px-14 py-6"
              disabled={loading}
            >
              Cancel
            </Button>
          </DialogClose>

          <Button
            variant="gradient"
            className="px-14 py-6"
            onClick={handleReactivate}
            disabled={loading}
          >
            {loading ? "Processing..." : "Reactivate"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReactiveModalAdminList;
