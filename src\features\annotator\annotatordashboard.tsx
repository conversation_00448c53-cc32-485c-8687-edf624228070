import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
} from "@dnd-kit/core";
import { CheckCircle2, Circle, Clock } from "lucide-react";
import * as React from "react";

import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
// import { Task } from "@/types/adminkanbantype";
import { Columncontainertask } from "./annotatorcolumn";
import {
  deleteTask,
  getTasks,
  updateTaskStatus,
} from "./annonator_api/annonator_api";
import { DetailsTask } from "@/types/kanbantasktype";

export type Column = {
  id: string;
  title: string;
  tasks: DetailsTask[];
};

const emptyColumns: Column[] = [
  {
    id: "todo",
    title: "To Do",
    tasks: [],
  },
  {
    id: "in-progress",
    title: "Progress",
    tasks: [],
  },
  {
    id: "done",
    title: "Completed",
    tasks: [],
  },
];

const KANBAN_STORAGE_KEY = "kanbanColumns";

// Helper function to map API status to column ID
const getColumnIdFromStatus = (status: string): string => {
  switch (status) {
    case "PENDING":
      return "todo";
    case "IN_PROGRESS":
      return "in-progress";
    case "COMPLETED":
      return "done";
    default:
      return "todo";
  }
};

// Helper function to map column ID to API status
const getStatusFromColumnId = (columnId: string): string => {
  switch (columnId) {
    case "todo":
      return "PENDING";
    case "in-progress":
      return "IN_PROGRESS";
    case "done":
      return "COMPLETED";
    default:
      return "PENDING"; 
  }
};

export default function AnnotatorKanbanBoard() {
  const [columns, setColumns] = React.useState<Column[]>(emptyColumns);
  const [activeTask, setActiveTask] = React.useState<DetailsTask | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const savedData = localStorage.getItem(KANBAN_STORAGE_KEY);

        if (savedData) {
          setColumns(JSON.parse(savedData));
          // Fetch fresh data in background
          await fetchTasks(false);
        } else {
          await fetchTasks(true);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Fetch tasks from API
  const fetchTasks = async (saveToLocalStorage: boolean) => {
    try {
      const response = await getTasks();

      if (response?.status === 1 && response.data) {
        const apiTasks = response.data.map((task: any) => ({
          id: task.id,
          title: task.name,
          description: task.description,
          level:
            task.priority === "HIGH"
              ? "H"
              : task.priority === "MEDIUM"
              ? "M"
              : "L",
          dueDate: task.dueDate,
          status: task.status,
          color: task.color,
          // Additional fields from API
          assignedToId: task.assignedToId,
          createdById: task.createdById,
          startDate: task.startDate,
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
        }));

        // Organize tasks into columns based on status
        const newColumns = emptyColumns.map((column) => ({
          ...column,
          tasks: apiTasks.filter(
            (task: any) =>
              getColumnIdFromStatus(task.status || "PENDING") === column.id
          ),
        }));

        setColumns(newColumns);

        if (saveToLocalStorage) {
          localStorage.setItem(KANBAN_STORAGE_KEY, JSON.stringify(newColumns));
        }
      }
    } catch (error) {
      console.error("Error fetching tasks:", error);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = columns
      .flatMap((col) => col.tasks)
      .find((t) => t.id === active.id);
    if (task) setActiveTask(task);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeTask = active.data.current?.task as DetailsTask;
    const activeColumn = active.data.current?.column as Column;
    const overColumn = columns.find((col) => col.id === over.id);

    if (!activeTask || !activeColumn || !overColumn) return;

    // Allow ALL movements (forward + backward)
    if (activeColumn.id !== overColumn.id) {
      const newStatus = getStatusFromColumnId(overColumn.id);

      // Optimistic UI update
      const updatedColumns = columns.map((col) => {
        if (col.id === activeColumn.id) {
          return {
            ...col,
            tasks: col.tasks.filter((t) => t.id !== activeTask.id),
          };
        } else if (col.id === overColumn.id) {
          return {
            ...col,
            tasks: [...col.tasks, { ...activeTask, status: newStatus }],
          };
        }
        return col;
      });

      setColumns(updatedColumns);

      try {
        // Update backend
        await updateTaskStatus(activeTask.id, { status: newStatus });
        // Update localStorage
        localStorage.setItem(
          KANBAN_STORAGE_KEY,
          JSON.stringify(updatedColumns)
        );
      } catch (error) {
        console.error("Failed to update task status:", error);
        // Revert on error
        setColumns(columns);
      }
    }

    setActiveTask(null);
  };

  const handleDeleteTask = async (taskToDelete: DetailsTask) => {
    try {
      // Optimistic update
      const newColumns = columns.map((col) => ({
        ...col,
        tasks: col.tasks.filter((task) => task.id !== taskToDelete.id),
      }));
      setColumns(newColumns);

      // API call
      await deleteTask(taskToDelete.id);
    } catch (error) {
      console.error("Failed to delete task:", error);
      setColumns(columns); // Revert on error
    }
  };

  const handleAddTask = async (newTask: DetailsTask, columnId: string) => {
    try {
      console.log("New Task:", newTask); // Debug
      const formattedTask: DetailsTask = {
        ...newTask,
        level: newTask.priority
          ? newTask.priority === "HIGH"
            ? "H"
            : newTask.priority === "MEDIUM"
            ? "M"
            : "L"
          : newTask.level || "L",
        dueDate: newTask.dueDate || null,
        status: newTask.status || getStatusFromColumnId(columnId),
      };

      const updatedColumns = columns.map((column) =>
        column.id === columnId
          ? { ...column, tasks: [...column.tasks, formattedTask] }
          : column
      );

      setColumns(updatedColumns);

      localStorage.setItem(KANBAN_STORAGE_KEY, JSON.stringify(updatedColumns));

      await fetchTasks(true);
    } catch (error) {
      console.error("Failed to add task:", error);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-full w-full flex flex-col">
      <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <div
          className="grid grid-cols-1  md:grid-cols-3 gap-4 w-full
          lg-only:h-[calc(100vh-150px)]
          xl:h-[calc(100vh-80px)]
          2xl:h-[calc(100vh-80px)]"
        >
          {columns.map((column) => (
            <Columncontainertask
              key={column.id}
              column={column}
              tasks={column.tasks}
              onDeleteTask={handleDeleteTask}
              onAddTask={handleAddTask}
              fetchTasksProp={fetchTasks}
            />
          ))}
        </div>
        <DragOverlay>
          {activeTask ? (
            <Card className="lg-only:w-[220px] xl:w-[250px] 2xl:w-[280px]">
              <CardHeader className="lg-only:p-2 xl:p-3 2xl:p-4">
                <div className="flex items-center space-x-2">
                  {getColumnIdFromStatus(activeTask.status || "PENDING") ===
                  "todo" ? (
                    <Circle className="lg-only:h-3 lg-only:w-3 xl:h-4 xl:w-4 2xl:h-5 2xl:w-5 text-blue-500" />
                  ) : getColumnIdFromStatus(activeTask.status || "PENDING") ===
                    "in-progress" ? (
                    <Clock className="lg-only:h-3 lg-only:w-3 xl:h-4 xl:w-4 2xl:h-5 2xl:w-5 text-yellow-500" />
                  ) : (
                    <CheckCircle2 className="lg-only:h-3 lg-only:w-3 xl:h-4 xl:w-4 2xl:h-5 2xl:w-5 text-green-500" />
                  )}
                  <CardTitle className="lg-only:text-xs xl:text-sm 2xl:text-base font-medium">
                    {activeTask.title}
                  </CardTitle>
                </div>
              </CardHeader>
            </Card>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
