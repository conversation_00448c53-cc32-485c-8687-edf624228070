// types.ts
export interface Annotator {
  id: string;
  name: string;
  email: string;
  // Add all other properties from your API response
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}

export interface Package {
  id: string;
  name: string;
  // Add other package properties
}

export interface PackageResponse {
  data: Package[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
}