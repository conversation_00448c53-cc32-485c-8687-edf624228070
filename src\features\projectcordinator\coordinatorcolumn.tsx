import { useDroppable } from "@dnd-kit/core";
import { AnimatePresence } from "framer-motion";
import { Task } from "@/types/adminkanbantype";
import { DraggableProjectTask } from "./coordinatordraggable";
import ClientCreateProjectTask from "./coordinatortask";

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};

export function Columncontainertask({
  column,
  tasks,
  onDeleteTask,
  onAddTask,
  fetchTasksProp,
}: {
  column: Column;
  tasks: Task[];
  onDeleteTask: (task: Task) => void;
  onAddTask: (newTask: Task, columnId: string) => void;
  fetchTasksProp: (saveToLocalStorage: boolean) => Promise<void>;
}) {
  const { setNodeRef } = useDroppable({
    id: column.id,
  });
  const fetchTasks = () => fetchTasksProp(true);

  const getColumnColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-[#FBF4F4]";
      case "in-progress":
        return "bg-[#FBF4F4]";
      case "done":
        return "bg-[#FBF4F4]";
      default:
        return "bg-[#FBF4F4]";
    }
  };

  const getTitleIndicatorColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-[#2525AB]";
      case "in-progress":
        return "bg-[#E96B1C]";
      case "done":
        return "bg-[#5AB24A]";
      default:
        return "bg-gray-400";
    }
  };

  return (
    <div>
      <div className="flex justify-between px-1 mb-2 items-center">
        <h2 className="font-semibold text-[#545454] text-muted-foreground">
          {column.title}
        </h2>
        <div
          className={`w-6 h-6 flex justify-center items-center rounded-full ${getTitleIndicatorColor(
            column.id
          )}`}
        >
          <ClientCreateProjectTask
            onAddTask={(task) => onAddTask(task, column.id)}
            columnId={column.id}
          />
        </div>
      </div>
      <div
        ref={setNodeRef}
        className={`rounded-lg p-4 ${getColumnColor(column.id)}`}
        style={{ height: "443px", overflowY: "auto" }}
      >
        <AnimatePresence mode="popLayout">
          {tasks.map((task) => (
            <DraggableProjectTask
              key={task.id}
              task={task}
              column={column}
              onDelete={onDeleteTask}
              fetchTasks={fetchTasks}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
