import React, {
  useState,
  useEffect,
  useRef,
  useImperative<PERSON><PERSON>le,
  useContext,
  forwardRef,
  useCallback,
  useMemo,
} from "react";
import { Search } from "lucide-react";
import ChatInput from "./centerchat/chatinput";
import MessageList from "./centerchat/messagelist";
import {
  getMessagesBetweenUsers,
  getGroupMessages,
  uploadFile,
} from "../apis/api";
import { SocketContext } from "@/socket/socket";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useJoinDMRoom } from "../hooks/useJoinDMRoom";
import { useJoinGroupRoom } from "../hooks/useJoinGroupRoom";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { HiOutlineDotsVertical } from "react-icons/hi";
import img1 from "@/assets/icons/circlesfour.gif";
import VideoCallIntegration from "./centerchat/videocallinterigation";

// Types
interface Message {
  id: string; // Required for uniqueness
  text: string;
  time: string;
  self: boolean;
  fileUrl?: string | null;
  fileType?: string | null;
  senderId?: string;
  receiverId?: string;
  tempFileUrl?: string;
  sender?: {
    id: string;
    name: string;
  };
}

interface SelectedUser {
  id?: string;
  userId?: string;
  name: string;
  avatar: string;
  isGroup?: boolean;
}

interface CenterChatProps {
  selectedUser: SelectedUser | null;
  scrollToMessage: (text: string) => void;
  toggleRightChat: () => void;
  isRightChatOpen: boolean;
}

// Utility functions
const sortMessagesByCreatedAt = (messages: Message[]): Message[] => {
  return [...messages].sort((a, b) => {
    const timeA = new Date(a.time).getTime();
    const timeB = new Date(b.time).getTime();
    return timeA - timeB;
  });
};

const formatMessage = (
  msg: {
    id: string;
    text?: string;
    createdAt: string;
    senderId?: string;
    receiverId?: string;
    conversationId?: string;
    fileUrl?: string;
    fileType?: string;
    sender?: {
      id: string;
      name: string;
    };
  },
  userId: string
): Message => ({
  id: msg.id,
  text: msg?.text || "",
  time: new Date(msg.createdAt).toISOString(),
  self: msg?.senderId === userId,
  fileUrl: msg.fileUrl,
  fileType: msg.fileType,
  senderId: msg.senderId,
  receiverId: msg.receiverId,
  sender: msg.sender,
});

const isMessageDuplicate = (
  existingMessages: Message[],
  newMessage: {
    id: string;
    text?: string;
    senderId?: string;
    fileUrl?: string;
    fileType?: string;
    createdAt?: string | Date;
    isUploading?: boolean;
    tempFileUrl?: string;
  }
): boolean => {
  return existingMessages.some((msg) => {
    // Check if the message ID already exists
    if (msg.id === newMessage.id) {
      return true;
    }

    // Check for temporary messages
    if (msg.id?.startsWith("temp-")) {
      // For file messages: compare fileType and senderId
      if (
        msg.fileUrl &&
        msg.fileType &&
        newMessage.fileUrl &&
        newMessage.fileType
      ) {
        return (
          msg.fileType === newMessage.fileType &&
          msg.senderId === newMessage.senderId
        );
      }
      // For text messages: compare text and senderId
      return (
        msg.text === (newMessage.text || "") &&
        msg.senderId === newMessage.senderId
      );
    }
    return false;
  });
};

const CenterChat = forwardRef<{ scrollToBottom: () => void }, CenterChatProps>(
  ({ selectedUser, scrollToMessage, toggleRightChat }, ref) => {
    // State
    const [messages, setMessages] = useState<Message[]>([]);
    const [newMessage, setNewMessage] = useState<string>("");
    const [searchOpen, setSearchOpen] = useState(false);
    const [searchText, setSearchText] = useState("");
    const [paperclipOpen, setPaperclipOpen] = useState(false);
    const [conversationId, setConversationId] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [filePreviews, setFilePreviews] = useState<string[]>([]);
    const [renderKey, setRenderKey] = useState(0);
    const [uploadingMessageId, setUploadingMessageId] = useState<string | null>(
      null
    );

    console.log("uploadProgress centerchat", uploadProgress, setRenderKey);
    console.log("scrollToMessage centerchat", scrollToMessage);

    // Context and selectors
    const { socket } = useContext(SocketContext);
    const userId = useSelector((state: RootState) => state.auth.user?.id);

    // Refs
    const scrollRef = useRef<HTMLDivElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      return () => {
        // Clean up any remaining blob URLs when component unmounts
        messages.forEach((msg) => {
          if (msg.tempFileUrl) {
            URL.revokeObjectURL(msg.tempFileUrl);
          }
        });
      };
    }, [messages]);

    // Memoized values
    const filteredMessages = useMemo(
      () =>
        searchText
          ? messages.filter((msg) =>
              msg.text.toLowerCase().includes(searchText.toLowerCase())
            )
          : messages,
      [messages, searchText]
    );

    const formattedMessages = useMemo(
      () =>
        messages.map((msg) => ({
          id: msg.id,
          text: msg.text,
          time: new Date(msg.time).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          self: msg.self,
          fileUrl: msg.fileUrl || undefined,
          fileType: msg.fileType || undefined,
          sender: msg.sender || {
            id: msg.senderId || "unknown",
            name: "Unknown",
          },
        })),
      [messages]
    );

    const formattedFilteredMessages = useMemo(
      () =>
        filteredMessages.map((msg) => ({
          id: msg.id,
          text: msg.text,
          time: new Date(msg.time).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          self: msg.self,
          fileUrl: msg.fileUrl || undefined,
          fileType: msg.fileType || undefined,
          sender: msg.sender || {
            id: msg.senderId || "unknown",
            name: "Unknown",
          },
        })),
      [filteredMessages]
    );

    // Callback functions
    const scrollToBottom = useCallback(() => {
      scrollRef.current?.scrollIntoView({ behavior: "smooth" });
    }, []);

    const clearError = useCallback(() => setError(null), []);

    const handleFileSelect = useCallback((files: File[]) => {
      setFilePreviews((prev) => {
        prev.forEach((url) => URL.revokeObjectURL(url));
        return [];
      });
      const newPreviews = files.map((file) => URL.createObjectURL(file));
      setSelectedFiles(files);
      setFilePreviews(newPreviews);
    }, []);

    const handleRemoveFiles = useCallback(() => {
      filePreviews.forEach((url) => URL.revokeObjectURL(url));
      setSelectedFiles([]);
      setFilePreviews([]);
    }, [filePreviews]);

    const handleFileUpload = useCallback(
      async (file: File) => {
        if (!userId || !selectedUser?.id) {
          setError("Missing user data for file upload.");
          return;
        }

        // Create unique key for this file to prevent duplicates
        const fileKey = `${file.name}-${file.size}-${file.lastModified}`;
        console.log(fileKey);
        // Skip if this file was recently uploaded
        if (
          messages.some(
            (msg) =>
              msg.fileUrl?.includes(file.name.split(".")[0]) && // Check filename prefix
              msg.senderId === userId &&
              new Date().getTime() - new Date(msg.time).getTime() < 30000 // 30 second window
          )
        ) {
          console.log("Skipping duplicate file upload:", file.name);
          return;
        }

        const tempId = `temp-${Date.now()}`;
        const tempFileUrl = URL.createObjectURL(file);
        setUploadingMessageId(tempId);
        setUploadProgress(0);

        // Add temporary message
        setMessages((prev) => [
          ...prev,
          {
            id: tempId,
            text: "",
            time: new Date().toISOString(),
            self: true,
            fileUrl: tempFileUrl,
            fileType: file.type.startsWith("image/") ? "IMAGE" : "FILE",
            senderId: userId,
            receiverId: selectedUser.isGroup
              ? undefined
              : selectedUser.userId || selectedUser.id,
            isUploading: true,
            tempFileUrl, // Store for cleanup
          },
        ]);

        try {
          const uploadData = {
            senderId: userId,
            ...(selectedUser.isGroup && { groupId: selectedUser.id }),
            ...(!selectedUser.isGroup && {
              receiverId: selectedUser.userId || selectedUser.id,
              conversationId,
            }),
          };

          const result = await uploadFile(file, uploadData, (progress) => {
            setUploadProgress(progress);
          });

          if (result.status === 1) {
            // Update the message with final URL
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === tempId
                  ? {
                      ...msg,
                      id: result.data.message.id,
                      fileUrl: result.data.fileUrl,
                      fileType: result.data.message.fileType,
                      isUploading: false,
                      tempFileUrl: undefined, // Clear temp URL
                    }
                  : msg
              )
            );

            // Clean up the blob URL
            URL.revokeObjectURL(tempFileUrl);

            // Emit socket event
            const messageData = {
              id: result.data.message.id,
              text: "",
              fileUrl: result.data.fileUrl,
              fileType: result.data.message.fileType,
              senderId: userId,
            };

            if (selectedUser.isGroup) {
              socket?.emit("send_group_message", {
                ...messageData,
                groupId: selectedUser.id,
              });
            } else {
              socket?.emit("send_dm", {
                ...messageData,
                conversationId,
                receiverId: selectedUser.userId || selectedUser.id,
              });
            }
          }
        } catch (error) {
          console.error("File upload error:", error);
          setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
          setError("Failed to upload file. Please try again.");

          // Clean up blob URL on error
          URL.revokeObjectURL(tempFileUrl);
        } finally {
          setUploadProgress(0);
          setUploadingMessageId(null);
        }
      },
      [userId, selectedUser, conversationId, socket, messages]
    );

    const handleSend = useCallback(async () => {
      let hasSent = false;

      if (newMessage.trim()) {
        if (!userId) {
          setError("You are not logged in. Please log in to send messages.");
          return;
        }

        if (!selectedUser?.userId && !selectedUser?.id) {
          setError("No recipient selected.");
          return;
        }

        const tempId = `temp-${Date.now()}`;
        const tempMessage: Message = {
          id: tempId,
          text: newMessage,
          time: new Date().toISOString(),
          self: true,
          senderId: userId,
        };

        setMessages((prev) => {
          const uniqueMessages = new Map(prev.map((msg) => [msg.id, msg]));
          uniqueMessages.set(tempId, tempMessage);
          return sortMessagesByCreatedAt(Array.from(uniqueMessages.values()));
        });
        setNewMessage("");

        const messageData = {
          id: tempId,
          text: newMessage,
          senderId: userId,
          fileUrl: null,
          fileType: null,
          replyToId: null,
        };

        if (selectedUser.isGroup) {
          socket?.emit("send_group_message", {
            ...messageData,
            groupId: selectedUser.id || "",
          });
        } else {
          if (!conversationId) {
            setError("No conversation found. Please try again.");
            return;
          }
          socket?.emit("send_dm", {
            ...messageData,
            conversationId,
            receiverId: selectedUser.userId || selectedUser.id,
          });
        }
        hasSent = true;
      }

      if (selectedFiles.length > 0) {
        // Clear files immediately when starting upload
        const filesToUpload = [...selectedFiles]; // Create a copy
        handleRemoveFiles(); // Clear the UI immediately

        // Upload files using the copy
        for (const file of filesToUpload) {
          await handleFileUpload(file);
        }
        hasSent = true;
      }

      if (hasSent) {
        setTimeout(scrollToBottom, 100);
      }
    }, [
      newMessage,
      userId,
      selectedUser,
      conversationId,
      socket,
      selectedFiles,
      handleFileUpload,
      handleRemoveFiles,
      scrollToBottom,
    ]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          handleSend();
        }
      },
      [handleSend]
    );

    // Imperative handle
    useImperativeHandle(ref, () => ({
      scrollToBottom,
    }));

    // Effects
    useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(e.target as Node)
        ) {
          setPaperclipOpen(false);
        }
        if (
          searchRef.current &&
          !searchRef.current.contains(e.target as Node)
        ) {
          setSearchOpen(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
      const fetchMessages = async () => {
        if (!selectedUser) return;
        setIsLoading(true);
        setError(null);
        try {
          let messagesArray = [];
          if (selectedUser.isGroup) {
            const response = await getGroupMessages(selectedUser.id || "");
            messagesArray = response?.data || [];
          } else {
            const response = await getMessagesBetweenUsers(
              selectedUser.userId || selectedUser.id || ""
            );
            messagesArray = response?.data || [];
          }

          // Filter duplicates before setting state
          const uniqueMessages = messagesArray.reduce(
            (acc: Message[], msg: any) => {
              const isDuplicate = acc.some(
                (existing) =>
                  (msg.fileUrl && existing.fileUrl === msg.fileUrl) ||
                  (msg.id && existing.id === msg.id)
              );
              return isDuplicate
                ? acc
                : [...acc, formatMessage(msg, userId || "")];
            },
            []
          );

          setMessages(sortMessagesByCreatedAt(uniqueMessages));
          setTimeout(scrollToBottom, 100);
        } catch (error) {
          console.error("Error fetching messages:", error);
          setError("Failed to load messages. Please try again.");
        } finally {
          setIsLoading(false);
        }
      };
      if (selectedUser && !conversationId) fetchMessages();
    }, [selectedUser, userId, conversationId, scrollToBottom]);

    useEffect(() => {
      if (!selectedUser || !userId || !socket) return;

      setConversationId(null);
      setMessages([]); // Clear messages on switch
      const joinRoom = () => {
        if (selectedUser.isGroup) {
          const groupId = selectedUser.id || "";
          socket.emit("join_group", { userId, groupId });
        } else {
          socket.emit("join_dm", {
            userId,
            otherUserId: selectedUser.userId || selectedUser.id,
          });
        }
      };

      joinRoom();

      const handleJoinedDm = (data: {
        conversation?: {
          id: string;
          messages?: Array<{
            id: string;
            text?: string;
            createdAt: string;
            senderId?: string;
            fileUrl?: string;
            fileType?: string;
          }>;
        };
      }) => {
        if (data.conversation) {
          setConversationId(data.conversation.id);
          if (
            data.conversation.messages &&
            Array.isArray(data.conversation.messages)
          ) {
            const formattedMessages = data.conversation.messages.map((msg) =>
              formatMessage(msg, userId || "")
            );
            setMessages((prev) => {
              const uniqueMessages = new Map(prev.map((msg) => [msg.id, msg]));
              formattedMessages.forEach((msg) =>
                uniqueMessages.set(msg.id, msg)
              );
              return sortMessagesByCreatedAt(
                Array.from(uniqueMessages.values())
              );
            });
            setTimeout(scrollToBottom, 100);
          }
        }
      };

      const handleJoinedGroup = () => {};
      const handleError = (error: { message?: string }) => {
        setError(error.message || "An error occurred");
      };

      socket.on("joined_dm", handleJoinedDm);
      socket.on("joined_group", handleJoinedGroup);
      socket.on("error", handleError);

      return () => {
        socket.off("joined_dm", handleJoinedDm);
        socket.off("joined_group", handleJoinedGroup);
        socket.off("error", handleError);
      };
    }, [selectedUser, userId, socket, scrollToBottom]);

    useEffect(() => {
      if (!socket) return;

      const handleNewMessage = (message: {
        id: string;
        text?: string;
        createdAt?: string | Date;
        senderId?: string;
        receiverId?: string;
        fileUrl?: string;
        fileType?: string;
      }) => {
        console.log("Received new_message:", message);

        // Skip processing if this is our own message (we handle those separately)
        if (message.senderId === userId) {
          console.log("Skipping own message - handled by message_sent event");
          return;
        }

        const formattedMessage: Message = {
          id: message.id,
          text: message.text || "",
          time: new Date(message.createdAt || Date.now()).toISOString(),
          self: message.senderId === userId,
          fileUrl: message.fileUrl || null,
          fileType: message.fileType || null,
          senderId: message.senderId,
          receiverId: message.receiverId,
        };

        setMessages((prev) => {
          const newMessages = [...prev, formattedMessage];
          return sortMessagesByCreatedAt(newMessages);
        });

        setTimeout(scrollToBottom, 100);
      };

      const handleNewGroupMessage = (message: {
        id: string;
        text?: string;
        createdAt?: string | Date;
        senderId?: string;
        fileUrl?: string;
        fileType?: string;
        sender?: { id: string; name: string };
      }) => {
        console.log("Received new_group_message:", message);
        if (isMessageDuplicate(messages, message)) return;

        const formattedMessage: Message = {
          id: message.id,
          text: message.text || "",
          time: new Date(message.createdAt || Date.now()).toISOString(),
          self: message.senderId === userId,
          fileUrl: message.fileUrl || null,
          fileType: message.fileType || null,
          senderId: message.senderId,
          sender: message.sender || {
            id: message.senderId || "",
            name: "Unknown",
          },
        };

        setMessages((prev) => {
          const uniqueMessages = new Map(prev.map((msg) => [msg.id, msg]));
          uniqueMessages.set(formattedMessage.id, formattedMessage);
          return sortMessagesByCreatedAt(Array.from(uniqueMessages.values()));
        });
        setTimeout(scrollToBottom, 100);
      };

      // In the useEffect for socket listeners
      const handleMessageSent = (data: {
        messageId: string;
        conversationId?: string;
        groupId?: string;
        message?: {
          id: string;
          text?: string;
          createdAt: string;
          senderId?: string;
          receiverId?: string;
          fileUrl?: string;
          fileType?: string;
          sender?: {
            id: string;
            name: string;
          };
        };
      }) => {
        if (!data.message) return;

        // Only process if this is the message we just sent
        if (
          data.messageId === uploadingMessageId ||
          (data.message.senderId === userId &&
            data.message.fileUrl &&
            !messages.some((m) => m.id === data.messageId))
        ) {
          setMessages((prev) => {
            // Remove temp message if exists
            const filtered = prev.filter(
              (msg) => msg.id !== uploadingMessageId
            );
            // Add new message
            return sortMessagesByCreatedAt([
              ...filtered,
              formatMessage(data.message!, userId || ""), // We can use ! here since we already checked data.message exists
            ]);
          });
          setUploadingMessageId(null);
          setTimeout(scrollToBottom, 0);
        }
      };

      socket.on("new_message", handleNewMessage);
      socket.on("new_group_message", handleNewGroupMessage);
      socket.on("message_sent", handleMessageSent);

      return () => {
        socket.off("new_message", handleNewMessage);
        socket.off("new_group_message", handleNewGroupMessage);
        socket.off("message_sent", handleMessageSent);
      };
    }, [socket, userId, messages, scrollToBottom, uploadingMessageId]);

    useJoinGroupRoom({
      socket,
      userId: userId ?? "",
      groupId: selectedUser?.isGroup ? selectedUser?.id ?? "" : "",
    });

    useJoinDMRoom({
      socket,
      userId: userId ?? "",
      otherUserId: !selectedUser?.isGroup
        ? (selectedUser?.userId || selectedUser?.id) ?? ""
        : "",
    });

    useEffect(() => {
      return () => {
        filePreviews.forEach((url) => URL.revokeObjectURL(url));
      };
    }, [filePreviews]);

    if (!selectedUser) {
      return (
        <div className="w-full h-full bg-white flex flex-col items-center justify-center text-gray-700">
          <div className="flex flex-col items-center justify-center text-center p-6 max-w-xl">
            <img src={img1} alt="4circle" className="w-[10rem] h-[10rem]" />
            <h2 className="text-2xl font-semibold mb-2">
              Chat with your colleagues
            </h2>
            <p className="text-sm">
              Select a conversation from the sidebar to start chatting
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center rounded-xl justify-between p-4 shadow-[0px_1px_12.24px_0px_#D8E4F7]">
          <div className="flex items-center gap-2">
            <img
              src={selectedUser.avatar || getAvatarUrl(selectedUser.name)}
              className="w-10 h-10 rounded-full"
              alt="avatar"
            />
            <div>
              <p className="font-semibold">{selectedUser.name}</p>
              <p className="text-xs text-gray-400">
                {selectedUser.isGroup ? "Group Chat" : "Online"}
              </p>
            </div>
          </div>
          <div
            className="flex gap-4 text-gray-500 items-center"
            ref={dropdownRef}
          >
            <div className="relative" ref={searchRef}>
              <Search
                className="cursor-pointer"
                onClick={() => setSearchOpen((prev) => !prev)}
              />
              {searchOpen && (
                <div className="absolute -top-[8px] right-[1.7rem] flex items-center border p-2 rounded-md w-[11rem] text-black z-10 bg-white shadow">
                  <input
                    type="text"
                    placeholder="Search messages..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="flex-1 text-sm outline-none"
                  />
                </div>
              )}
            </div>
            <VideoCallIntegration
              userId={userId}
              selectedUser={selectedUser}
              conversationId={conversationId || undefined}
              socket={socket}
              setMessages={setMessages}
              scrollRef={scrollRef}
              setError={setError}
            />
            <HiOutlineDotsVertical
              onClick={toggleRightChat}
              className="text-xl cursor-pointer hover:text-[#FF577F] transition"
            />
          </div>
        </div>
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-4 my-2">
            <p>{error}</p>
            <button className="float-right font-bold" onClick={clearError}>
              ×
            </button>
          </div>
        )}
        {isLoading && (
          <div className="flex justify-center items-center p-4">
            <div className="loader">Loading...</div>
          </div>
        )}
        <MessageList
          messages={formattedMessages}
          filteredMessages={formattedFilteredMessages}
          searchText={searchText}
          scrollRef={scrollRef}
          key={renderKey}
          uploadProgress={uploadProgress}
          uploadingMessageId={uploadingMessageId}
        />
        <ChatInput
          newMessage={newMessage}
          setNewMessage={setNewMessage}
          handleSend={handleSend}
          handleKeyDown={handleKeyDown}
          paperclipOpen={paperclipOpen}
          setPaperclipOpen={setPaperclipOpen}
          disabled={!selectedUser}
          userId={userId || ""}
          selectedUser={selectedUser || {}}
          onFileSelect={handleFileSelect}
          onFileUpload={handleFileUpload}
          //onRemoveFiles={handleRemoveFiles}
          files={selectedFiles}
        />
      </div>
    );
  }
);

CenterChat.displayName = "CenterChat";

export default CenterChat;
