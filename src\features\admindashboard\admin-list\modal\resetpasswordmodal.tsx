import { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "lucide-react";
import { useResetAdminPasswordMutation } from "../adminlist_api/adminList_mutations";
import { toast } from "react-toastify"; // ✅ Toastify imported
import "react-toastify/dist/ReactToastify.css";

const ResetPasswordModal = ({
  userId,
  onSuccessRefresh,
}: {
  userId: string;
  onSuccessRefresh: () => void;
}) => {
  const [method, setMethod] = useState<"auto" | "manual">("auto");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const { mutate: resetPassword, status } = useResetAdminPasswordMutation();
  const closeRef = useRef<HTMLButtonElement>(null);

  const generatePassword = () => {
    const newPassword = Math.random().toString(36).slice(-10);
    setPassword(newPassword);
  };

  const handleResetPassword = () => {
    if (method === "manual" && !password.trim()) {
      alert("Please enter or generate a password.");
      return;
    }

    const finalPassword =
      method === "manual" ? password : Math.random().toString(36).slice(-10);

    resetPassword(
      {
        id: userId,
        newPassword: method === "manual" ? finalPassword : "",
      },
      {
        onSuccess: () => {
          toast.success(
            method === "auto"
              ? "Password reset link sent successfully"
              : "Password reset successfully"
          );
          closeRef.current?.click();
          onSuccessRefresh();
        },
        onError: () => {
          toast.error("Something went wrong. Please try again.");
        },
      }
    );
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Reset Password
        </Button>
      </DialogTrigger>

      <DialogContent>
        <div className="mt-6">
          <Label className="text-xl">Admin Reset-Password</Label>
          <RadioGroup
            defaultValue="auto"
            className="mt-2 flex flex-col gap-2"
            onValueChange={(value) => setMethod(value as "auto" | "manual")}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="auto"
                id="auto"
                className="border-[#FF577F]"
              />
              <Label htmlFor="auto" className="text-lg">
                Automatic send password link to mail
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value="manual"
                id="manual"
                className="border-[#FF577F]"
              />
              <Label htmlFor="manual" className="text-lg">
                Create password
              </Label>
            </div>
          </RadioGroup>

          {method === "manual" && (
            <div className="flex mt-3 items-center gap-2">
              <div className="w-[40%] relative border-gradient rounded-lg">
                <Input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                  className="w-full bg-[#F9EFEF] text-[#5E5E5E] py-6"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-2 top-1/2 -translate-y-1/2"
                >
                  {showPassword ? (
                    <Eye className="w-5 h-5 text-gray-500" />
                  ) : (
                    <EyeOff className="w-6 h-5 text-gray-500" />
                  )}
                </button>
              </div>

              <Button
                variant="gradient"
                onClick={generatePassword}
                className="px-6 py-6"
              >
                Generate Password
              </Button>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-center mt-6">
          <DialogClose asChild>
            <Button
              className="border-gradient bg-white hover:bg-[#faf5f5] text-black px-14 py-6"
              ref={closeRef}
            >
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant="gradient"
            className="px-10 py-6"
            onClick={handleResetPassword}
            disabled={status === "pending"}
          >
            {status === "pending" ? "Processing..." : "Reset Password"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordModal;
