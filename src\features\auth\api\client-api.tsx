import { customAxios } from "@/utils/axio-interceptor";
import axios from "axios";

export const ClientSignUp = async (
  name: string,
  email: string,
  password: string
) => {
  try {
    console.log("Sending signup request with data:", { name, email, password });
    console.log("Using API base URL:", import.meta.env.VITE_API_BASE_URL);

    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL}/v1/clients/register`,
      {
        name,
        email,
        password,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    console.log("Signup API response:", {
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      headers: response.headers,
    });

    return response.data;
  } catch (error: any) {
    console.error("Full error object:", JSON.stringify(error, null, 2));

    if (error.response) {
      console.error("Error response details:", {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });
    } else if (error.request) {
      console.error("No response received. Request was:", error.request);
    } else {
      console.error("Request setup error:", error.message);
    }

    // Throw a more informative error
    throw new Error(
      error.response?.data?.message ||
        error.message ||
        "Signup failed. Please try again."
    );
  }
};

//login

//login
export const ClientLogin = async (email: string, password: string) => {
  try {
    const response = await customAxios.post(
      `/v1/clients/login`,
      { email, password }
      // {
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   withCredentials: true,
      // }
    );

    return response.data; // This should trigger OTP
  } catch (error) {
    console.error("Login failed:", error);
    throw error;
  }
};

// ✅ Forgot Password (Send OTP)
export const ClientForgotPassword = async (email: string) => {
  try {
    const response = await customAxios.post(
      `/v1/clients/forgot-password`,
      { email },
      {
        headers: { "Content-Type": "application/json" },
        withCredentials: true,
      }
    );

    return response.data;
  } catch (error) {
    console.error("Forgot Password failed:", error);
    throw error;
  }
};

// ✅ Verify OTP
export const ClientVerifyOtp = async (email: string, otp: string) => {
  try {
    const response = await customAxios.post(`/v1/clients/login-otp`, {
      email,
      otp,
    });
    console.log("OTP verification response:", response);
    localStorage.setItem("token", response.data.token);

    return response.data;
  } catch (error) {
    console.error("OTP verification failed:", error);
    throw error;
  }
};

// Reset Password
export const ResetVerifyOtp = async (email: string, otp: string) => {
  try {
    const response = await customAxios.post("/v1/clients/verify-otp", {
      email,
      otp,
    });
    if (response.data && !response.data.token) {
      console.warn(
        "Token not found in response.data.token, full response:",
        response
      );
    }
    console.log("OTP verification response:", response);
    return response.data; // Should include token
  } catch (error) {
    console.error("OTP verification error:", error);
    throw error;
  }
};

// ✅ Correct OTP verification URL for Signup
export const ClientSignupOtp = async (email: string, otp: string) => {
  try {
    const response = await customAxios.post(`/v1/clients/register-otp`, {
      email,
      otp,
    });
    console.log("OTP verification response:", response);

    return response.data;
  } catch (error: any) {
    console.error("OTP verification failed:", error);
    throw new Error(
      error.response?.data?.message ||
        error.message ||
        "OTP verification failed."
    );
  }
};

export const ClientResendOtp = async (email: string) => {
  try {
    const response = await customAxios.post(`/v1/clients/verify-otp-resend`, {
      email,
    });

    console.log("Resend OTP response:", response);
    return response.data;
  } catch (error) {
    console.error("Resend OTP failed:", error);
    throw error;
  }
};

// ✅ Reset Password
export const ClientResetPassword = async (
  email: string,
  resetToken: string,
  newPassword: string
) => {
  try {
    const response = await customAxios.post("/v1/clients/reset-password", {
      email,
      resetToken,
      newPassword,
    });
    console.log("Password reset response:", response);
    return response.data;
  } catch (error) {
    console.error("Password reset error:", error);
    throw error;
  }
};

// ✅ Verify OTP
// export const ClientVerifyOtp = async (email: string, otp: string) => {
//   try {
//     const response = await customAxios.post(
//       `/v1/clients/register-otp`,
//       { email, otp },
//       {
//         headers: {
//           "Content-Type": "application/json",
//         },
//         withCredentials: true,
//       }
//     );

//     console.log("OTP verification response:", response.data);
//     return response.data; // Should contain "message": "Email verified successfully."
//   } catch (error: any) {
//     console.error("OTP verification failed:", error);
//     throw new Error(
//       error.response?.data?.message || error.message || "OTP verification failed."
//     );
//   }
// };

// CoWorker INVITE Accept
export const CoWorkerInviteAccept = async (
  email: string,
  password: string,
  name: string,
  token: string // Make token required
) => {
  try {
    const response = await customAxios.post(
      `/v1/coworker/accept-invite`,
      { email, password, name, token }, // Send as separate fields
      {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true,
      }
    );
    return response.data;
  } catch (error) {
    console.error("CoWorker Invite Accept failed:", error);
    throw error;
  }
};

export const Logout = async () => {
  const response = await customAxios.post("/v1/clients/logout");
  return response.data;
};