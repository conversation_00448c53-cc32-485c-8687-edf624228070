// import {
//     InputOTP,
//     InputOTPGroup,
//     InputOTPSlot,
//   } from "@/components/ui/input-otp"
  
//   import { Button } from "@/components/ui/button"
//   import {
//     FormControl,
//     FormDescription,
//     FormLabel,
//     FormMessage,
//     FormItem,
//   } from "@/components/ui/form"
  
//   import { MoveRight } from "lucide-react"
  
//   interface OtpInputProps {
//     label?: string
//     description?: string
//     value: string
//     onChange: (value: string) => void
//     error?: string
//     onVerify: () => void
//     isSubmitting?: boolean
//   }
  
//   const SignupOtpInput: React.FC<OtpInputProps> = ({
//     label = "Enter One Time Password",
//     description = "Enter the 4-digit code sent to your email.",
//     value,
//     onChange,
//     error,
//     onVerify,
//     isSubmitting = false,
//   }) => {
//     return (
//       <div className="space-y-6">
//         <FormItem>
//           <FormLabel className="text-[28px] text-[#282828]">{label}</FormLabel>
//           <br />
//           <FormDescription className="text-[14px] text-[#757575]">
//             {description}
//           </FormDescription>
//           <FormControl>
//             <InputOTP maxLength={4} value={value} onChange={onChange}>
//               <InputOTPGroup className="flex flex-row gap-2 outline-none">
//                 <InputOTPSlot index={0} className="border-gradient rounded-lg" />
//                 <InputOTPSlot index={1} className="border-gradient rounded-lg" />
//                 <InputOTPSlot index={2} className="border-gradient rounded-lg" />
//                 <InputOTPSlot index={3} className="border-gradient rounded-lg" />
//               </InputOTPGroup>
//             </InputOTP>
//           </FormControl>
//           {error && <FormMessage>{error}</FormMessage>}
//         </FormItem>
  
//         <Button
//           type="button"
//           onClick={onVerify}
//           className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-10 py-6 text-[16px]"
//           disabled={isSubmitting}
//         >
//           Verify
//           <MoveRight className="ml-1 w-4 h-4" />
//         </Button>
//       </div>
//     )
//   }
  
//   export default SignupOtpInput
  