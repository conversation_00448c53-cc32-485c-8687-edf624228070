import { customAxios } from "@/utils/axio-interceptor";

// Pending leave get
export const PendingLeaveApi = async () => {
  try {
    const response = await customAxios.get("/v1/leave/pending");
    return response.data;
  } catch (error) {
    console.error("Error fetching pending leaves:", error);
    throw error;
  }
};

// Approve leave post
export const AproovLeave = async (id: string) => {
  try {
    const response = await customAxios.post(`/v1/leave/approve/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error approving leave:", error);
    throw error;
  }
};

// Reject leave post
export const RejectLeave = async (id: string) => {
  try {
    const response = await customAxios.post(`/v1/leave/reject/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error rejecting leave:", error);
    throw error;
  }
};

// // Get all leave requests ye admin ke liye hai cordiator ke liye nahi 
// export const getAllLeaves = async () => {
//   try {
//     const response = await customAxios.get("/v1/leave/all-leave-requests");
//     return response.data;
//   } catch (error) {
//     console.error("Error fetching all leaves:", error);
//     throw error;
//   }
// };



// Get all leave requests
export const getcordinatorclientLeaves = async () => {
  try {
    const response = await customAxios.get("/v1/leave/leave-requests");
    return response.data;
  } catch (error) {
    console.error("Error fetching all leaves:", error);
    throw error;
  }
};

