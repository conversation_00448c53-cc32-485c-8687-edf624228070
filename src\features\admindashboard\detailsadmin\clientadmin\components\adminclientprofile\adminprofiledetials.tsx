// @ts-ignore
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { AdminClientProfileApi } from './adminclientprofile_api';

interface AdminProfileUserallDataProps {
  isEditing: boolean;
  onSaveSuccess: () => void;
  onCancel: () => void;
  clientId?: string;
}

interface FormData {
  name: string;
  companyName: string;
  phoneNumber: string;
  country: string;
  stateProvince: string;
  email: string;
  website: string;
  timezone: string;
  address: string;
  postalCode: string;
}

const ProfileUserallData = ({ 
//   isEditing = false, 
  clientId 
}: AdminProfileUserallDataProps) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    companyName: '',
    phoneNumber: '',
    country: '',
    stateProvince: '',
    email: '',
    website: '',
    timezone: '',
    address: '',
    postalCode: ''
  });

  const [loading, setLoading] = useState(true);

  const fetchProfileData = async () => {
    if (!clientId) {
      toast.error('Client ID is missing');
      return;
    }

    try {
      setLoading(true);
      const response = await AdminClientProfileApi.getClientProfileById(clientId);

      setFormData({
        name: response.data.user?.name ?? 'null',
        companyName: response.data.profile?.companyName ?? 'null',
        phoneNumber: response.data.profile?.phoneNumber ?? 'null',
        country: response.data.profile?.country ?? 'null',
        stateProvince: response.data.profile?.stateProvince ?? 'null',
        email: response.data.user?.email ?? 'null',
        website: response.data.profile?.website ?? 'null',
        timezone: response.data.user?.timezone ?? 'null',
        address: response.data.profile?.address ?? 'null',
        postalCode: response.data.profile?.postalCode ?? 'null'
      });
    } catch (error: any) {
      console.error('Profile fetch error:', error);
      toast.error(error.response?.data?.message || 'Failed to fetch profile data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (clientId) {
      fetchProfileData();
    }
  }, [clientId]);

  // चूंकि एडिट API नहीं है, इसलिए हम इन फंक्शन्स को कमेंट कर देते हैं
  /*
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof FormData) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleSave = async () => {
    try {
      const updateData = {
        companyName: formData.companyName,
        phoneNumber: formData.phoneNumber,
        website: formData.website,
        address: formData.address,
        postalCode: formData.postalCode,
        country: formData.country,
        stateProvince: formData.stateProvince
      };

      await AdminClientProfileApi.updateClientProfile(clientId!, updateData);
      await fetchProfileData();
      onSaveSuccess();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update profile');
    }
  };
  */

  // Common styling classes
  const viewModeStyle = "w-full p-2 rounded-lg bg-[#F9EFEF] text-[#5E5E5E] border-gradient";

  if (!clientId) {
    return <div>No client selected</div>;
  }

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-lg">
      {/* एडिट और सेव बटन्स को हटा दिया क्योंकि एडिट फंक्शनैलिटी अभी उपलब्ध नहीं है */}
      
      <div className="space-y-6">
        <div className='flex flex-row gap-4 w-full'>
          {/* Name */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Name</label>
            <div className={viewModeStyle}>{formData.name || 'null'}</div>
          </div>

          {/* Company Name */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Company Name</label>
            <div className={viewModeStyle}>{formData.companyName || 'null'}</div>
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* Phone Number */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Phone Number</label>
            <div className={viewModeStyle}>{formData.phoneNumber || 'null'}</div>
          </div>

          {/* Country */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Country</label>
            <div className={viewModeStyle}>{formData.country || 'null'}</div>
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* State */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">State</label>
            <div className={viewModeStyle}>{formData.stateProvince || 'null'}</div>
          </div>

          {/* Email */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Email</label>
            <div className={viewModeStyle}>{formData.email || 'null'}</div>
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* Website */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Website</label>
            <div className={viewModeStyle}>{formData.website || 'null'}</div>
          </div>

          {/* Time Zone */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Time Zone</label>
            <div className={viewModeStyle}>{formData.timezone || 'null'}</div>
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* Address */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Address</label>
            <div className={viewModeStyle}>{formData.address || 'null'}</div>
          </div>

          {/* Postal Code */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Postal Code</label>
            <div className={viewModeStyle}>{formData.postalCode || 'null'}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileUserallData;