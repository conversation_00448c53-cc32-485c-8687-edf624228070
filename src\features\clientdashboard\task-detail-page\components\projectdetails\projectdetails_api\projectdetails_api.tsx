import { customAxios } from "@/utils/axio-interceptor";

export const getProjectDetails = async (Id: string) => {
  try {
    console.log("API call: getProjectDetails with ID:", Id);
    const response = await customAxios.get(`/v1/projects/project-by-id/${Id}`);
    console.log("API response from getProjectDetails:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching project details:", error);
    throw error;
  }
};

// This API will return empty data since there are no coworkersa
export const getCoworkers = async () => {
  try {
    // Make the API call but ignore the response since we know there are no coworkers
    await customAxios.get("/v1/coworker/get-coworkers");
    return { data: { items: [] } }; // Return empty array to show "Not coworkers assign"
  } catch (error) {
    console.error("Error fetching coworkers:", error);
    throw error;
  }
};


// Updated API to handle FormData for project update
export const UpdateProjectDetailsApi = async (id: string, projectForm: FormData) => {
  try {
    const response = await customAxios.put(`/v1/projects/update-project/${id}`, projectForm, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    console.log("Update project response data:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error updating project details:", error);
    throw error;
  }
};