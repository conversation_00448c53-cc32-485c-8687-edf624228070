// matchmaking.types.ts
export interface ClientData {
  id: string;
  name: string;
  email: string;
  passwordHash: string;
  emailVerified: string;
  role: "CLIENT";
  domain: string | null;
  availableFrom: string;
  availableTo: string;
  timezone: string | null;
  industry: string | null;
  category: string | null;
  isDeleted: boolean;
  invalidLoginAttempts: number;
  accountStatus: "ACTIVE" | "SUSPENDED" | string;
  suspendedUntil: string | null;
  clientOwnerId: string | null;
  annotatorStatus: string | null;
  coworkerPermission: string | null;
  otpCode: string | null;
  otpExpiry: string | null;
  createdAt: string;
  updatedAt: string;
  packageId: string | null;
  packageName: string | null; // From API response
  startDate: string | null; // From API response
  endDate: string | null; // From API response
  subscriptionId: string | null; // Added to match API response
  Subscription: SubscriptionData | null; // Keep for other use cases
  assignmentsAsClient: AssignmentData[];
  clientPackageDetails: ClientPackageDetail[];
}

export interface SubscriptionData {
  id: string;
  startDate: string;
  endDate: string;
  package: {
    name: string;
    id: string;
  };
}

export interface AssignmentData {
  id: string;
  clientId: string;
  developerId: string;
  coordinatorId: string;
  createdAt: string;
}

export interface ClientPackageDetail {
  id: string;
  clientId: string;
  packageId: string;
  availableFrom: string;
  availableTo: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuestionnaireData {
  id: string;
  packageId: string;
  timezone: string | null;
  industry: string;
  category: string;
  availableFrom: string | null;
  availableTo: string | null;
  startOn: string;
  description: string | null;
}

export interface PackageData {
  id: string;
  name: string;
}