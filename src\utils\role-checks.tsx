import { RootState } from "@/store";
import { useAppSelector } from "@/store/hooks/reduxHooks";

export function useHasAccess(): boolean {
  const role = useAppSelector((state: RootState) => state.auth.user?.role);
  const permission = useAppSelector((state) => state.auth.user?.coworkerPermission);

  if (role === "COWORKER") {
    console.log(permission === "EDIT", "mangaread")
    return permission === "EDIT";
  }

  return true;
}
