import { useState, useEffect, useContext } from 'react';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { getAvatarUrl } from '@/store/dicebearname/getAvatarUrl';
import { SocketContext } from '@/socket/socket';
import { getGroupMember } from '../../apis/api';

interface Member {
  id: string;
  name: string;
  isOnline?: boolean;
}

interface GroupMemberResponse {
  message: string;
  data: { id: string; name: string; email: string }[];
  count: number;
}

interface GroupMembersNameProps {
  groupId: string;
  isGroup?: boolean; // Add this prop to determine if it's a group chat
}

const GroupMembersName = ({ groupId, isGroup = false }: GroupMembersNameProps) => {
  const [showAllMembers, setShowAllMembers] = useState(false);
  const { socket } = useContext(SocketContext);
  const [membersState, setMembersState] = useState<Member[]>([]);

  const { data: members, isLoading, error } = useQuery<Member[], Error>({
    queryKey: ['groupMembers', groupId],
    queryFn: async () => {
      if (!groupId) {
        throw new Error('Invalid group ID');
      }
      const response: GroupMemberResponse = await getGroupMember(groupId);
      console.log('Group members response:', response);
      return response.data.map((member) => ({
        id: member.id,
        name: member.name,
        isOnline: false,
      }));
    },
    enabled: !!groupId && isGroup, // Only fetch if it's a group chat
  });

  // Update members with real-time online status (optional)
  useEffect(() => {
    if (!socket || !members) return;

    setMembersState(members);

    const handleUserOnline = (userId: string) => {
      setMembersState((prev) =>
        prev.map((m) => (m.id === userId ? { ...m, isOnline: true } : m))
      );
    };

    const handleUserOffline = (userId: string) => {
      setMembersState((prev) =>
        prev.map((m) => (m.id === userId ? { ...m, isOnline: false } : m))
      );
    };

    socket.on('user_online', handleUserOnline);
    socket.on('user_offline', handleUserOffline);

    return () => {
      socket.off('user_online', handleUserOnline);
      socket.off('user_offline', handleUserOffline);
    };
  }, [socket, members]);

  // Don't render anything for one-to-one chats
  if (!isGroup) {
    return null;
  }

  if (!groupId) return <div>Invalid group ID</div>;
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading group members: {error.message}</div>;
  if (!membersState || membersState.length === 0) return <div>No group members found</div>;

  const membersToDisplay = showAllMembers ? membersState : membersState.slice(0, 3);

  return (
    <div className="mt-6">
      <div className="flex justify-between items-center mb-4">
        <p className="font-bold">Members</p>
        {/* <Button variant="ghost" size="sm" className="border-gradient">
          Add
        </Button> */}
      </div>
      <div className="space-y-3">
        {membersToDisplay.map((member) => (
          <div key={member.id} className="flex items-center gap-3">
            <div className="relative">
              <img
                src={getAvatarUrl(member.name)}
                className="w-10 h-10 rounded-full"
                alt={member.name}
              />
              {member.isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div>
              <p className="font-medium">{member.name}</p>
              <p className="text-xs text-gray-500">
                {member.isOnline ? 'Online' : 'Offline'}
              </p>
            </div>
          </div>
        ))}
      </div>
      {membersState.length > 3 && (
        <div className="flex items-center justify-start mt-3">
          <Button
            variant="gradient"
            size="sm"
            onClick={() => setShowAllMembers(!showAllMembers)}
          >
            {showAllMembers ? 'Show Less' : 'See All'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default GroupMembersName;