import { useState, useEffect } from "react";
import { ChevronDown, ChevronRight } from "lucide-react"; // Import the icons

// Assuming this is the function to fetch the FAQs
import { getAllFAQ } from "@/features/admindashboard/components/faq_setting_api/faq_api/faqsapi";
import { BackButton, NoData } from "@/_components/common";

export default function FAQs() {
  const [faqs, setFaqs] = useState<any[]>([]); // Array for storing FAQs
  const [activeIndex, setActiveIndex] = useState<number | null>(null); // To track which FAQ is active

  const toggleFAQ = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index); // Toggle the active FAQ
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getAllFAQ();
        console.log("Fetched data:", response);

        // Check if the data is an array and set it to state
        if (response && Array.isArray(response.data)) {
          setFaqs(response.data); // Now we're setting the array correctly
        } else {
          console.error("FAQs are not in the expected array format");
        }
      } catch (error) {
        console.error("Error fetching FAQs:", error);
      }
    };

    fetchData();
  }, []); // Empty dependency array means this effect runs once when the component mounts

  return (
    <div className="w-full mx-auto px-10">
      <div className="flex gap-3 mb-3">
        <BackButton/>
      <h2 className="text-[30px] font-bold ">Frequently Asked Questions</h2>
      </div>
      {faqs.length > 0 ? (
        faqs.map((faq, index) => (
          <div
            key={faq.id} // Using unique 'id' from the FAQ
            className={`rounded-[18px] mb-4 transition-all duration-200 ${
              activeIndex === index
                ? "border-[#f14e64] shadow-[0px_6px_16px_0px_#D5314830] p-3 border-2 bg-white"
                : "shadow-[0px_5px_16px_0px_#080F340F] border-[#cccccc] border  p-2"
            }`}
          >
            <button
              onClick={() => toggleFAQ(index)} // Toggle the FAQ on click
              className="w-full flex justify-between items-center px-6 py-4 text-left"
            >
              <span className="font-semibold text-gray-800 text-[18px]">
                {faq.question}
              </span>
              {activeIndex === index ? (
                <ChevronDown className="text-[#ffffff] w-7 h-7 rounded-full bg-[#D53148] transition-transform duration-200" />
              ) : (
                <ChevronRight className="bg-[#D53148] text-[#ffffff] w-7 h-7  rounded-full transition-transform duration-200" />
              )}
            </button>
            {activeIndex === index && (
              <div className="px-6 pb-4 text-gray-600 text-[18px]">{faq.answer}</div>
            )}
          </div>
        ))
      ) : (
        <NoData message="No FAQs available" />
      )}
    </div>
  );
}
