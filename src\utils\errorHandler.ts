// utils/errorHandler.ts
import { AxiosError } from "axios";
import { toast } from "react-toastify";

export function errorMessage(error: unknown) {
  let message = "Something went wrong. Please try again.";

  if (isAxiosError(error)) {
    const errorData = error.response?.data;

    if (errorData?.message) {
      message = errorData.message;
    } else if (typeof errorData === "string") {
      message = errorData;
    }
  }

  toast.error(message);
}

function isAxiosError(error: unknown): error is AxiosError<any> {
  return (error as AxiosError)?.isAxiosError === true;
}
