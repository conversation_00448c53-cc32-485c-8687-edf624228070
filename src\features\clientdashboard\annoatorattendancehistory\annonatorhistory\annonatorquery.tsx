
import { useQuery } from "@tanstack/react-query";
import { getAnnonatorHistory } from "./annonatorhistory_api";

export const useGetAnnonatorHistory = (id: string, fromDate?: string, toDate?: string) => {
  return useQuery({
    queryKey: ["annonatorhistory", id, fromDate, toDate],
    queryFn: () => getAnnonatorHistory(id, fromDate, toDate),
    enabled: !!id, // Only run the query if we have an ID
  });
};
