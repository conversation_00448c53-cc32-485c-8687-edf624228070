
import { useState, useEffect } from "react";

// Hook to get responsive project card styles for admin dashboard
export const useProjectAdminStyles = () => {
  // const { isLaptopMd } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  
  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Get grid styles based on screen size
  const getGridStyles = () => {
    // 1024px
    if (windowWidth <= 1024) {
      return "grid grid-cols-3 gap-x-5 gap-y-6 py-2";
    } 
    // 1440px and 2560px (4K)
    else {
      return "grid grid-cols-4 gap-x-7 gap-y-6 py-2";
    }
  };

  return {
    gridStyles: getGridStyles()
  };
};
