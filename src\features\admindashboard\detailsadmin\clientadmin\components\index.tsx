// import img1 from "@/assets/clients/Ellipse.png";
// import { useState } from "react";
// import CustomToast from "@/_components/common/customtoast";
// import { useNavigate } from "react-router-dom";

// interface AnnotatorProps {
//   name: string;
//   email: string;
//   annotators: string;
//   projects: string;
//   joiningDate: string;
//   coworker: string;
//   expiringon: string;
//   image: string;
// }

// // Fake Data for Testing
// const annotators: AnnotatorProps[] = [
//   {
//     name: "<PERSON>",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Am<PERSON> <PERSON>",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "<PERSON>",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Am<PERSON>",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "<PERSON>",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit <PERSON>",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "Emily Davis",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit Singh",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "Kevin Copper",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit Singh",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "atharva Brown",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit Singh",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "aryan Johnson",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit Singh",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
//   {
//     name: "kunal",
//     email: "<EMAIL>",
//     annotators: "12",                     
//     projects: "01",
//     coworker: "Amit Singh",
//     joiningDate: "23 March 2025",                                    
//     expiringon: "23 March 2025",                                      
//     image: img1,
//   },
// ];

// export default function Clientsadmin() {
//   const navigate = useNavigate();
//   // const [isModalOpen, setIsModalOpen] = useState(false);
//   // const [selectedAnnotator, setSelectedAnnotator] =
//     useState<AnnotatorProps | null>(null);
//   const [showToast, setShowToast] = useState(false);

//   // const openModal = (annotator: AnnotatorProps) => {
//   //   setSelectedAnnotator(annotator);
//   //   setIsModalOpen(true);
//   // };

//   // const closeModal = () => {
//   //   setIsModalOpen(false);
//   //   setSelectedAnnotator(null);
//   // };

//   // const handleSuccess = () => {
//   //   setShowToast(true);
//   // };

//   return (
//     <div className="relative">
//       <div className="grid grid-cols-4 gap-4 ">
//         {annotators.map((annotator, index) => (
//           <div
//             key={index}
//             className="border border-[#FF577F] p-3 rounded-lg shadow-md bg-white "
//           >
//             <div className="flex flex-row items-center gap-3">
//               <img
//                 src={annotator.image}
//                 alt={annotator.name}
//                 className="w-12 h-12 rounded-full"
//               />
//               <div className="flex flex-col">
//                 <h3 className="text-md font-semibold">{annotator.name}</h3>
//                 {/* <p className="text-xs text-gray-500">{annotator.email}</p> */}
//               </div>
//             </div>
//             <div className="mt-2 flex flex-col px-4 gap-y-3 text-[12px]">
//               <p className="flex gap-x-5">
//                 <strong className="text-[#5B5B5B]">Annotators:</strong>{annotator.annotators}
//               </p>
//               <p className="flex gap-x-10">
//                 <strong className="text-[#5B5B5B]">Projects:</strong> {annotator.projects}
//               </p>
//               <p className="flex gap-x-6">
//                 <strong className="text-[#5B5B5B]">Coworkers:</strong> {annotator.coworker}
//               </p>
//               <p className="flex gap-x-12">
//                 <strong className="text-[#5B5B5B]">Joined:</strong> {annotator.joiningDate}
//               </p>
//               <p className="flex gap-x-6">
//                 <strong className="text-[#5B5B5B]">Expiring on:</strong> {annotator.expiringon}
//               </p>
//             </div>
//             <div className="flex flex-row justify-center gap-x-3 mt-2">
//               <button
//                 onClick={() => navigate("/dashboard/admincoodinator")}
//                 className="bg-gradient-to-r from-[#E91C24] via-[#FF577F]  to-[#45ADE2] px-[5.2rem] py-2  text-white text-[12px] rounded-lg"
//               >
//                 All Projects
//               </button>
             
//             </div>
//           </div>
//         ))}

//         {/* Modal Open Hoga Yaha */}
//       </div>
//       {/* ✅ Toast */}
//       {showToast && (
//         <div className="fixed top-5 right-5 z-[100]">
//           <CustomToast
//             title="Shift Change Requested"
//             message="Your shift change request was submitted successfully."
//             type="success"
//             duration={4000}
//             onClose={() => setShowToast(false)}
//           />
//         </div>
//       )}
//     </div>
//   );
// }
