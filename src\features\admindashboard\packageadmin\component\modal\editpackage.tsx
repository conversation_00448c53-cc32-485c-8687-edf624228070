
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import { ChevronDown } from "lucide-react";
import { updatePackageApi } from "../package/api_package/api_package";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";

const categoryOptions = [
  { label: "Active", value: true },
  { label: "Inactive", value: false },
];

interface EditPackageProps {
  data: {
    id: string;
    name: string;
    price: number;
    description?: string;
    isActive: boolean;
    billingType?: string;
  };
}

const EditPackage = ({ data }: EditPackageProps) => {
  const [packageName, setPackageName] = useState(data.name);
  const [price, setPrice] = useState(data.price.toString());
  const [description, setDescription] = useState(data.description || "");
  const [isActive, setIsActive] = useState<boolean>(data.isActive);
  const [billingType] = useState(data.billingType || "MONTHLY");
  const queryClient = useQueryClient();

  const handleSelect = (value: boolean) => {
    setIsActive(value);
    setDropdownOpen(false);
  };
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [clickedInsideDropdown, setClickedInsideDropdown] = useState(false);


  useEffect(() => {
    const handleClick = () => {
      if (!clickedInsideDropdown) {
        setDropdownOpen(false);
      }
      setClickedInsideDropdown(false); // reset after each click
    };

    document.addEventListener("click", handleClick);
    return () => document.removeEventListener("click", handleClick);
  }, [clickedInsideDropdown]);

  const [shouldCloseDialog, setShouldCloseDialog] = useState(false);

  const { mutate: updatePackageMutation, isPending } = useMutation({
    mutationFn: () =>
      updatePackageApi(data.id, {
        name: packageName,
        price: Number(price),
        description,
        isActive,
        billingType,
      }),
    onSuccess: () => {
      toast.success("Package updated successfully", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        theme: "light",
        style: {
          background: "#F0FFF4",
          border: "1px solid #C6F6D5",
          color: "#2F855A",
        },
      });

      queryClient.invalidateQueries({ queryKey: ["packageList"] });
      setShouldCloseDialog(true);
    },
    onError: (error) => {
      console.error("Update error:", error);
      toast.error("Failed to update package", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        theme: "light",
        style: {
          background: "#FFF5F5",
          border: "1px solid #FEB2B2",
          color: "#C53030",
        },
      });
    },
  });

  return (
    <div className="w-full flex flex-col lg-only:gap-3 xl-only:gap-5 2xl-only:gap-6 ">
      <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4">Edit Package</h2>
      <div className="flex flex-col justify-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4">
        <div className="lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Package Name*</Label>
          <div className="border-gradient rounded-lg">
            <Input
              value={packageName}
              onChange={(e) => setPackageName(e.target.value)}
              className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
            />
          </div>
        </div>

        <div className="w-full flex flex-row lg-only:gap-x-3 xl-only:gap-x-4 2xl-only:gap-x-5 justify-between">
          <div className="w-full">
            <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Price*</Label>
            <div className="border-gradient rounded-lg">
              <Input
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
                type="number"
              />
            </div>
          </div>

          <div className="w-full">
            <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Description</Label>
            <div className="border-gradient rounded-lg">
              <Input
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
              />
            </div>
          </div>
        </div>

        <div className="lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Status</Label>

          <div
            className="relative w-full lg-only:mt-1 xl-only:mt-1.5 2xl-only:mt-2 rounded-md"
            onClick={() => setClickedInsideDropdown(true)} // ✅ mark internal click
          >
            <button
              type="button"
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="w-full lg-only:py-2 lg-only:px-2 xl-only:py-1.5 xl-only:px-2.5 2xl-only:py-2 2xl-only:px-3 rounded-md border-gradient text-left flex items-center justify-between bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
            >
              {isActive !== undefined
                ? categoryOptions.find((opt) => opt.value === isActive)?.label
                : "Select Status"}
              <ChevronDown
                className={`ml-2 lg-only:h-3 lg-only:w-3 xl-only:h-3.5 xl-only:w-3.5 2xl-only:h-4 2xl-only:w-4 transition-transform ${dropdownOpen ? "rotate-180" : "rotate-0"
                  }`}
              />
            </button>

            {dropdownOpen && (
              <div className="absolute w-full bg-white border mt-1 rounded-md shadow-lg z-10">
                {categoryOptions.map((option) => (
                  <div
                    key={String(option.value)}
                    className="cursor-pointer lg-only:py-1 lg-only:px-2 xl-only:py-1.5 xl-only:px-2.5 2xl-only:py-2 2xl-only:px-3 hover:bg-gray-100 lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
                    onClick={() => {
                      handleSelect(option.value);
                      setDropdownOpen(false); // close dropdown after selecting
                    }}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

      </div>

      {shouldCloseDialog && (
        <DialogClose asChild>
          <button className="hidden" ref={(ref) => ref?.click()}></button>
        </DialogClose>
      )}

      <div className="lg-only:mt-3 xl-only:mt-4 2xl-only:mt-6 flex justify-end lg-only:gap-1 xl-only:gap-1.5 2xl-only:gap-2">
        <Button
          variant="gradient"
          className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
          onClick={() => {
            console.log("Updating package:", {
              id: data.id,
              name: packageName,
              price: Number(price),
              description,
              isActive,
              billingType,
            });
            updatePackageMutation();
          }}
          disabled={isPending}
        >
          {isPending ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 lg-only:h-3 lg-only:w-3 xl-only:h-4 xl-only:w-4 2xl-only:h-5 2xl-only:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </span>
          ) : (
            "Save Changes"
          )}
        </Button>
        <DialogClose>
          <Button
            variant="ghost"
            className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 border-gradient lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
            disabled={isPending}
          >
            Cancel
          </Button>
        </DialogClose>
      </div>
    </div>
  );
};

export default EditPackage;