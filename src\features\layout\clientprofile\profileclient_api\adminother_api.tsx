// api/admin-profile.ts
import { customAxios } from "@/utils/axio-interceptor"

interface UserProfile {
  id: string;
  name: string;
  lastname: string | null;
  email: string;
  role: string;
  domain: string | null;
  availableFrom: string | null;
  availableTo: string | null;
  timezone: string | null;
  industry: string | null;
  category: string | null;
  accountStatus: string;
  annotatorStatus: string | null;
  createdAt: string;
  updatedAt: string;
}

export const AdminAndOtherProfileapi = async (): Promise<UserProfile> => {
  const response = await customAxios.get('/v1/users/profile');
  return response.data.data; // Accessing the data property from the response
}