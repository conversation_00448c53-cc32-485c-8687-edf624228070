import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./components/AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { IoMdAddCircleOutline } from "react-icons/io";
import { useState } from "react";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import OnboardForm from "./components/OnboardForm";
import { useOnboardingList } from "./api/query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useQueryClient } from "@tanstack/react-query";
import { PaginationState, Updater } from "@tanstack/react-table";

const AdminOnboard = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const { data, isLoading } = useOnboardingList({
    page: pagination.pageIndex + 1,
    limit: pagination.pageSize,
  });

  const handleDialogChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      queryClient.invalidateQueries({ queryKey: ["onboarding"] });
    }
  };

  const handlePaginationChange = (updater: Updater<PaginationState>) => {
    setPagination((prev) => {
      const newPagination =
        typeof updater === "function" ? updater(prev) : updater;
      return newPagination;
    });
  };

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-row justify-between items-center">
        <div className="flex flex-wrap gap-3 items-center mx-2">
          <FaArrowLeft
            className="text-2xl text-[#FF577F] cursor-pointer"
            onClick={() => navigate(-1)}
          />
          <h1 className="text-[#282828] text-[24px] font-semibold">Onboards</h1>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={handleDialogChange}>
          <DialogTrigger asChild>
            <Button
              variant={"gradient"}
              className="px-8 flex items-center justify-center"
            >
              <span className="font-poppins">Onboard New</span>
              <IoMdAddCircleOutline className="!w-6 !h-6" />
            </Button>
          </DialogTrigger>
          <DialogContent className="2xl-only:max-w-4xl xl-only:max-w-2xl lg-only:max-w-lg">
            <OnboardForm
              onSuccess={() =>
                queryClient.invalidateQueries({ queryKey: ["onboarding"] })
              }
            />
          </DialogContent>
        </Dialog>
      </div>

      <div>
        <DataTable
          title="Forms"
          columns={useAdminColumns()}
          data={data?.data || []}
          loading={isLoading}
          pagination={pagination}
          onPaginationChange={handlePaginationChange}
          pageCount={data?.totalPages || 1}
        />
      </div>
    </div>
  );
};

export default AdminOnboard;
