import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate, useLocation } from "react-router-dom";
import { useClientProjectsList } from "../api/useClientProjectsQuery";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import  NoData  from "@/_components/common/nodata";

const AdminClients = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get client ID and name from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const clientId = queryParams.get('id');
  const clientName = queryParams.get('name') || "Client";

  // Log the URL parameters for debugging
  console.log("URL Search:", location.search);
  console.log("Client ID:", clientId);
  console.log("Client Name:", clientName);

  // Check if client ID is missing
  if (!clientId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>Missing client ID. Please select a client from the clients page.</p>
          <button
            onClick={() => navigate('/admin/clients')}
            className="mt-4 px-4 py-2 bg-[#FF577F] text-white rounded-md hover:bg-[#ff3c6a]"
          >
            Go to Clients
          </button>
        </div>
      </div>
    );
  }

  const columns = useAdminColumns(); // Call the hook at the component level

  const { data, isLoading, error } = useClientProjectsList(clientId);

  if (isLoading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>{(error as Error).message}</p>
        </div>
      </div>
    );
  }

  const projectsData = data?.data || [];

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />

        <h1 className="text-[#282828] text-[24px]">
          {clientName}'s Projects
        </h1>
      </div>

      {projectsData.length > 0 ? (
        <DataTable
          title="Projects"
          columns={columns}
          data={projectsData}
          loading={isLoading}
          disablePagination
        />
      ) : (
        <NoData />
      )}
    </div>
  );
};

export default AdminClients;
