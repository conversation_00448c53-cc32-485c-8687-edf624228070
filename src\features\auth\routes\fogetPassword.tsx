import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";
import { MoveRight } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import OtpInput from "./common/OtpInput";
import { FormField } from "@/components/ui/form";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ClientForgotPassword, ResetVerifyOtp } from "../api/client-api";
import { useResponsive } from "@/hooks/use-responsive";
import logo from "@/assets/darklogo.png";
import { BackButton } from "@/_components/common";

const FormSchema = z.object({
  pin: z.string().min(4, { message: "OTP must be 4 characters." }),
});

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [otpSent, setOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(30);
  const [canResend, setCanResend] = useState(false);
  // const [showToast, setShowToast] = useState(false);
  // const [toastMessage, setToastMessage] = useState("");
  const { isLaptopMd, isLaptopLg } = useResponsive();

  const navigate = useNavigate();

  const getStyles = () => {
    if (isLaptopLg) {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[17%] flex items-start justify-center pt-8",
        logoSize: "w-[155px] h-[30px]  mx-14",
        contentContainer: "flex flex-row gap-24 w-[88%] items-center",
        formContainer:
          "w-[45%] flex flex-col shadow-[0px_4px_48px_10px_#0000000F] rounded-xl p-10",
        titleContainer: "flex flex-row items-center gap-6",
        title: "text-[36px] font-bold font-inter text-[#282828]",
        formContent: "flex flex-col gap-12 mt-6 p-8",
        label: "block text-lg font-medium mb-3",
        input: "w-full p-4  bg-[#F9EFEF] rounded",
        errorText: "text-red-500 text-sm mt-2",
        button:
          "w-40 py-3 text-[17px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold flex items-center justify-center gap-2.5",
        buttonIcon: "w-[20px] h-[20px]",
        backgroundContainer: "w-[50%] h-screen",
      };
    } else if (isLaptopMd) {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[16%] flex items-start justify-center pt-6",
        logoSize: "w-[155px] h-[30px] mx-14",
        contentContainer: "flex flex-row gap-20 w-[93%] items-center",
        formContainer:
          "w-[45%] flex flex-col shadow-[0px_4px_48px_10px_#0000000F] rounded-xl p-9",
        titleContainer: "flex flex-row items-center ",
        backButton: "text-pink-500 hover:text-pink-600 text-lg font-medium",
        title: "text-[32px] font-bold font-inter text-[#282828]",
        formContent: "flex flex-col gap-11 mt-5 p-7",
        label: "block text-base font-medium mb-2.5",
        input: "w-full p-3.5 bg-[#F9EFEF] rounded  outline-none",
        errorText: "text-red-500 text-sm mt-1.5",
        button:
          "w-40 py-3 text-[17px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold flex items-center justify-center gap-2.5",
        buttonIcon: "w-[19px] h-[19px]",
        backgroundContainer: "w-[48%] h-screen",
      };
    } else {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[14%] flex items-start justify-center pt-4",
        logoSize: "w-[155px] h-[30px]  mx-14",
        contentContainer: "flex flex-row gap-20 w-[92%] items-center",
        formContainer:
          "w-[45%] flex flex-col shadow-[0px_4px_48px_10px_#0000000F] rounded-xl p-8",
        titleContainer: "flex flex-row items-center gap-4",
        backButton: "text-pink-500 hover:text-pink-600 text-lg font-medium",
        title: "text-[30px] font-bold font-inter text-[#282828]",
        formContent: "flex flex-col gap-10 mt-4 p-6",
        label: "block text-base font-medium mb-2",
        input: "w-full p-3 bg-[#F9EFEF] rounded",
        errorText: "text-red-500 text-xs mt-1",
        button:
          "w-40 py-3 text-[17px] bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold flex items-center justify-center gap-2.5",
        buttonIcon: "w-[18px] h-[18px]",
        backgroundContainer: "w-[45%] h-screen",
      };
    }
  };

  const styles = getStyles();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: { pin: "" },
  });

  const handleSendOtp = async () => {
    const trimmedEmail = email.trim();
    if (!trimmedEmail) return setError("Email is required");

    try {
      await ClientForgotPassword(trimmedEmail);
      setOtpSent(true);
      setCountdown(30);
      setCanResend(false);
      setError("");
      toast.success("OTP sent to your email.", {
        position: 'top-right',
        autoClose: 3000,
      });
    } catch (err: any) {
      console.error("Send OTP API Error:", err);
       setError(err.response?.data?.message || "Failed to send OTP. Try again.");
      toast.error(err.response?.data?.message || "Failed to send OTP. Try again.", {
        position: 'top-right',
        autoClose: 3000,
      });
    }
  };

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    try {
      const response = await ResetVerifyOtp(email, data.pin);
      console.log("API response", response);

      if (!response) {
        throw new Error("No response from server");
      }

      let token = null;
      if (response.token) {
        token = response.token;
      } else if (response.data && response.data.token) {
        token = response.data.token;
      } else if (typeof response === "string") {
        token = response;
      }
      if (!token) {
        throw new Error("Token not received from server");
      }

      // if (!response || !response.token) {
      //   throw new Error("Token not received from server");
      // }
      sessionStorage.setItem("resetToken", response.token);
      sessionStorage.setItem("resetEmail", email);
       toast.success('Password changed successfully!', {
        position: 'top-right',
        autoClose: 3000,
      });


      // Navigate to reset password with email and token
      setTimeout(() => {
        navigate("/auth/update-password", {
          replace: true,
          state: { resetToken: token, email: email },
        });
      }, 1000);
    } catch (err: any) {
      console.error("OTP verification error:", err);
      toast.error(err,{
        position: 'top-right',
        autoClose: 3000,
      });
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0 && otpSent) {
      timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
    } else {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown, otpSent]);

  return (
    <div className={styles.container}>
      <div className={styles.logoContainer}>
        <img src={logo} alt="Logo" className={styles.logoSize} />
      </div>

      <div className={styles.contentContainer}>
        <div className={styles.formContainer}>
          <div className={styles.titleContainer}>
            <button className={styles.backButton} onClick={() => navigate(-1)}>
              <BackButton />
            </button>
            <h2 className={styles.title}>Forgot Password?</h2>
          </div>

          <div className={styles.formContent}>
            <div>
              <label className={styles.label}>Enter email</label>
             <div className="border-gradient w-[60%] rounded-xl">
               <input
                type="email"
                className={`${styles.input} ${
                  error ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={otpSent}
              />
             </div>
              {error && <p className={styles.errorText}>{error}</p>}
            </div>

            <button
              type="button"
              onClick={handleSendOtp}
              disabled={!canResend && otpSent}
              className={`${styles.button} ${
                !canResend && otpSent ? "bg-gray-400 cursor-not-allowed" : ""
              }`}
            >
              {otpSent && !canResend ? `Resend in ${countdown}s` : "Send OTP"}
              <MoveRight className={styles.buttonIcon} />
            </button>

            {otpSent && (
              <FormProvider {...form}>
                <FormField
                  control={form.control}
                  name="pin"
                  render={({ field, fieldState }) => (
                    <OtpInput
                      value={field.value}
                      onChange={field.onChange}
                      error={fieldState.error?.message}
                      onVerify={form.handleSubmit(onSubmit)}
                      isSubmitting={form.formState.isSubmitting}
                    />
                  )}
                />
              </FormProvider>
            )}
          </div>
        </div>

        <div className={styles.backgroundContainer}>
          <AuthCommonComponent />
        </div>
      </div>

     
    </div>
  );
};

export default ForgotPassword;
