import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive project create form styles
export const useProjectCreateStyles = () => {
  const { isLaptopMd, isLaptopLg } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  console.log("windowWidth", windowWidth);
  
  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Get styles based on screen size
  const getStyles = () => {
    if (isLaptopLg) {
      // 4K/2560px
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50",
        scrollContainer: "w-full  flex items-center justify-center",
        formContainer: "bg-white px-6 py-4 rounded-lg shadow-lg w-[800px] h-[800px] overflow-y-scroll",
        heading: "text-[28px] font-semibold mb-6",
        formSpacing: "space-y-6",
        label: "block text-[12px] font-normal",
        input: "w-full p-4  bg-[#F9EFEF] text-[15px] rounded-lg outline-none",
        textarea: "w-full p-4  h-full bg-[#F9EFEF] text-[15px] rounded-lg outline-none",
        select: "w-full p-4  bg-[#F9EFEF] text-[14px] rounded-lg outline-none",
        flexContainer: "flex gap-6",
        dateInput: "w-full mt-2 p-3 bg-[#F9EFEF] text-sm rounded-md",
        uploadContainer: "flex justify-center items-center gap-3 p-3 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-6 h-6 text-gray-600",
        uploadText: "text-base text-gray-700",
        helpText: "text-[14px] text-[#424242] flex items-center gap-2",
        helpIcon: "w-5 h-5 text-gray-600",
        buttonContainer: "flex justify-end items-center gap-4 mt-6",
        
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-5 py-2.5 rounded-md mb-5 text-base"
      };
    } else if (isLaptopMd) {
      // 1440px
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50",
        scrollContainer: "w-full flex items-center justify-center",
        formContainer: "bg-white px-5 py-5 rounded-lg shadow-lg w-[650px] h-[620px] overflow-y-scroll",
        heading: "text-[24px] font-semibold mb-2",
        formSpacing: "space-y-5",
        label: "block text-[12px] font-normal",
        input: "w-full p-3.5  bg-[#F9EFEF] text-[14px] rounded-lg outline-none",
        textarea: "w-full p-3.5  h-full bg-[#F9EFEF] text-[14px] rounded-lg outline-none",
        select: "w-full p-3.5  bg-[#F9EFEF] text-[13px] rounded-lg outline-none",
        flexContainer: "flex gap-5",
        dateInput: "w-full  p-2.5 bg-[#F9EFEF] text-xs rounded-md",
        uploadContainer: "flex justify-center items-center gap-2.5 p-2.5 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-5.5 h-5.5 text-gray-600",
        uploadText: "text-sm text-gray-700",
        helpText: "text-[13px] text-[#424242] flex items-center gap-1.5",
        helpIcon: "w-4.5 h-4.5 text-gray-600",
        buttonContainer: "flex justify-end items-center gap-3.5 mt-5",
        cancelButton: "px-5 py-2.5 text-sm",
        submitButton: "px-5 py-2.5 text-sm",
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md mb-4 text-sm"
      };
    } else {
      // 1024px (default)
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50",
        scrollContainer: "w-full flex items-center justify-center",
        formContainer: "bg-white px-4 py-2 rounded-lg shadow-lg w-[550px] h-[600px] overflow-y-scroll",
        heading: "text-[20px] font-semibold mb-4",
        formSpacing: "space-y-3",
        label: "block text-[12px] font-normal",
        input: "w-full p-3 bg-[#F9EFEF] text-[13px] rounded-lg outline-none",
        textarea: "w-full p-3  h-full bg-[#F9EFEF] text-[13px] rounded-lg outline-none",
        select: "w-full p-3 bg-[#F9EFEF] text-[12px] rounded-lg outline-none",
        flexContainer: "flex gap-4",
        dateInput: "w-full  p-2 bg-[#F9EFEF] text-xs rounded-md",
        uploadContainer: "flex justify-center items-center gap-2 p-2 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-5 h-5 text-gray-600",
        uploadText: "text-sm text-gray-700",
        helpText: "text-[12px] text-[#424242] flex items-center gap-1",
        helpIcon: "w-4 h-4 text-gray-600",
        buttonContainer: "flex justify-end items-center gap-3",
        cancelButton: "px-4 py-2 text-sm",
        submitButton: "px-4 py-2 text-sm",
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-3 py-1.5 rounded-md mb-3 text-xs"
      };
    }
  };

  return getStyles();
};
