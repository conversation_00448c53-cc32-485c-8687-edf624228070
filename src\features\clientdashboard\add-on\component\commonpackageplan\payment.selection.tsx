import { <PERSON>alog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import {
  Control,
  FieldErrors,
  SubmitHandler,
  UseFormRegister,
} from "react-hook-form";
import { FormValues } from "./questioniare";

interface MyModalProps {
  register: UseFormRegister<FormValues>;
  control: Control<FormValues>;
  errors: FieldErrors<FormValues>;
  handleSubmit: any;
  setValue: any;
  onSubmit: SubmitHandler<FormValues>;
  formValid: boolean;
  isSubmitting: boolean;
}

function PaymentModal({
  handleSubmit,
  errors,
  setValue,
  onSubmit,
  formValid,
  isSubmitting,
}: MyModalProps) {
  const [open, setOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);

  const handlePaymentChange = (value: string) => {
    setSelectedPayment(value);
    // Always set "dodo" as the payment method regardless of UI selection
    setValue("paymentMethod", value); // Keep using dodo payment flow
    console.log("Payment method changed:", value, "- Using dodo payment flow");
  };

  const handleOpenModal = (e: React.MouseEvent) => {
    if (!formValid) {
      e.preventDefault();
      return;
    }
    setOpen(true);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="gradient"
          onClick={handleOpenModal}
          disabled={isSubmitting || !formValid}
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <svg
                className="animate-spin h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </div>
          ) : (
            "Pay Now"
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <div className="flex flex-col items-center space-y-6 py-4">
          <h2 className="text-xl font-semibold text-center">
            Select your Payment Method
          </h2>

          <div className="flex flex-col sm:flex-row gap-3 w-full justify-center">
            {/* Cards (Debit/Credit) Option */}
            <button
              onClick={() => handlePaymentChange("dodo")}
              className={`px-4 py-2 rounded-md border border-gray-300  hover:border-none min-w-[120px] transition-all ${
                selectedPayment === "dodo"
                  ? "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]  border-none text-white"
                  : "hover:bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] hover:text-white"
              }`}
            >
              Cards (Debit/Credit)
            </button>

            {/* PayPal Option */}
            <button
              onClick={() => handlePaymentChange("paypal")}
              className={`px-4 py-2 rounded-md border border-gray-300  hover:border-none min-w-[120px] transition-all ${
                selectedPayment === "paypal"
                  ? "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]  border-none text-white "
                  : "hover:bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] hover:text-white"
              }`}
            >
              Paypal
            </button>

            {/* Bank Transfer Option */}
            <button
              onClick={() => handlePaymentChange("bank")}
              className={`px-4 py-2 rounded-md border border-gray-300 hover:border-none min-w-[120px] transition-all ${
                selectedPayment === "bank"
                  ? "bg-gradient-to-r from-[#E91C24] via-[#FF577F] border-none to-[#45ADE2] text-white"
                  : "hover:bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] hover:text-white"
              }`}
            >
              Bank Transfer
            </button>
          </div>

          {errors.paymentMethod && (
            <p className="text-sm text-red-500">
              {errors.paymentMethod.message}
            </p>
          )}

          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={!selectedPayment}
            className={`w-full max-w-xs ${
              !selectedPayment
                ? "opacity-70 cursor-not-allowed"
                : "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]"
            }`}
          >
            Proceed with Payment
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default PaymentModal;