import { useEffect, useRef, useState, useContext } from "react";
import CenterChat from "../components/centerchat";
import LeftChat from "../components/leftchat";
import RightChat from "../components/rightchat";
import {
  fetchChats,
  fetchConversationMedia,
  fetchNotifications,
  markNotificationRead,
} from "../apis/api";
import { BackButton } from "@/_components/common";
import { IoChatbubbleEllipsesOutline } from "react-icons/io5";
import bell from "@/assets/icons/dashboard/bell.svg";
import NotificationDropdown from "../components/centerchat/notificationdropdown";
import { SocketContext } from "@/socket/socket";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface User {
  id: string;
  name: string;
  avatar: string;
  userId?: string;
  isGroup?: boolean;
  conversationId?: string;
  groupId?: string;
}

interface Media {
  id: string;
  fileUrl: string;
  fileType: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
  };
}

interface ChatNotification {
  id: string;
  type: string;
  message: string;
  metadata: {
    groupId?: string;
    conversationId?: string;
    messageId?: string;
    senderName?: string;
    groupName?: string;
    messageText?: string;
    isMention?: boolean;
  };
  isRead: boolean;
  createdAt: string;
}

const Chat = () => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [chatList, setChatList] = useState<User[]>([]);
  const [imageMedia, setImageMedia] = useState<Media[]>([]);
  const [pdfMedia, setPdfMedia] = useState<Media[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const bellRef = useRef<HTMLImageElement>(null);
  const centerChatRef = useRef<{ scrollToBottom: () => void }>(null);
  const [notificationsOpen, setNotificationsOpen] = useState<boolean>(false);
  const [notifications, setNotifications] = useState<ChatNotification[]>([]);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isRightChatOpen, setIsRightChatOpen] = useState(false);
  const { socket } = useContext(SocketContext);
  const userId = useSelector((state: RootState) => state.auth.user?.id);

  const toggleRightChat = () => {
    setIsRightChatOpen((prev) => !prev);
  };

  // Fetch chats
  useEffect(() => {
    const getChats = async () => {
      try {
        const response = await fetchChats();
        console.log("Fetch chats response:", response);
        const data = Array.isArray(response?.data) ? response.data : [];
        interface ChatParticipant {
          id: string;
          name: string;
        }
        interface ChatApiResponse {
          id: string;
          type: "group" | "dm";
          name?: string;
          participants?: ChatParticipant[];
        }
        const mappedChats: User[] = data.map((chat: ChatApiResponse) => ({
          id: chat.id,
          name:
            chat.type === "group"
              ? chat.name || "Unnamed Group"
              : chat.participants?.find((p) => p.id !== userId)?.name ||
                "Unknown",
          avatar: "",
          isGroup: chat.type === "group",
          groupId: chat.type === "group" ? chat.id : undefined,
          conversationId: chat.type === "dm" ? chat.id : undefined,
          userId:
            chat.type === "dm"
              ? chat.participants?.find((p) => p.id !== userId)?.id
              : undefined,
        }));
        console.log("Mapped chats:", mappedChats);
        if (mappedChats.length === 0) {
          console.error("No chats available");
        }
        setChatList(mappedChats);
      } catch (error) {
        console.error("Error fetching chats:", error);
        setChatList([]);
        console.error("Failed to load chats");
      }
    };
    getChats();
  }, [userId]);

  // Auto-select first chat
  useEffect(() => {
    if (!selectedUser && chatList.length > 0) {
      console.log("Auto-selecting first chat:", chatList[0]);
      setSelectedUser(chatList[0]);
    }
  }, [chatList, selectedUser]);

  useEffect(() => {
    if (selectedUser?.id) {
      const fetchMedia = async () => {
        try {
          const mediaId = selectedUser.isGroup
            ? selectedUser.groupId || selectedUser.id
            : selectedUser.conversationId || selectedUser.id;
          console.log(
            "Fetching media with ID:",
            mediaId,
            "Is group:",
            selectedUser.isGroup
          );
          const images = await fetchConversationMedia(mediaId, "IMAGE");
          const pdfs = await fetchConversationMedia(mediaId, "PDF");
          setImageMedia(images);
          setPdfMedia(pdfs);
        } catch (error) {
          console.error("Error fetching media:", error);
        }
      };
      fetchMedia();
    }
  }, [selectedUser]);

  // Fetch notifications
  useEffect(() => {
    if (!userId) return;

    const getNotifications = async () => {
      try {
        const response = await fetchNotifications(userId, {
          page,
          limit: 5,
          unreadOnly: true,
        });
        console.log("Fetched notifications response:", response);
        const newNotifications = response?.notifications || [];
        const newTotalPages = response?.totalPages || 1;
        setNotifications((prev) => {
          const existingIds = new Set(prev.map((n) => n.id));
          const uniqueNew = newNotifications.filter(
            (n: ChatNotification) => !existingIds.has(n.id)
          );
          return [...prev, ...uniqueNew];
        });
        setTotalPages(newTotalPages);
      } catch (error) {
        console.error("Error fetching notifications:", error)
      }
    };
    getNotifications();

    if (socket) {
      socket.on("connect", () => {
        console.log("Socket connected, fetching notifications");
        setPage(1);
        setNotifications([]);
        getNotifications();
      });
    }

    return () => {
      if (socket) {
        socket.off("connect");
      }
    };
  }, [userId, socket, page]);

  // Handle real-time notifications
  useEffect(() => {
    if (!socket) return;

    const handleNotification = (notification: {
      type: string;
      groupId?: string;
      conversationId?: string;
      messageId: string;
      content: string;
      senderName: string;
      groupName?: string;
      messageText: string;
      id?: string;
    }) => {
      console.log("Received new_notification:", notification);
      const newNotification: ChatNotification = {
        id:
          notification.id ||
          `${(notification.groupId || notification.conversationId)}-${
            notification.messageId
          }-${Date.now()}`,
        type: notification.type,
        message: notification.content,
        metadata: {
          groupId: notification.groupId,
          conversationId: notification.conversationId,
          messageId: notification.messageId,
          senderName: notification.senderName,
          groupName: notification.groupName,
          messageText: notification.messageText,
          isMention: notification.type === "MENTION",
        },
        isRead: false,
        createdAt: new Date().toISOString(),
      };
      setNotifications((prev) => {
        if (prev.some((n) => n.id === newNotification.id)) return prev;
        return [newNotification, ...prev];
      });
      centerChatRef.current?.scrollToBottom();
    };

    // socket.on("mention_notification", handleNotification);
    socket.on("new_notification", handleNotification);

    return () => {
      // socket.off("mention_notification", handleNotification);
      socket.off("new_notification", handleNotification);
    };
  }, [socket]);

  const loadMoreNotifications = () => {
    if (page < totalPages) {
      setPage((prev) => prev + 1);
    }
  };

  const scrollToMessage = (messageText: string) => {
    console.log("Attempting to scroll to message with text:", messageText);
    const sanitizedText = messageText
      .trim()
      .replace(/\s+/g, " ")
      .replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const el = document.querySelector(`[data-msg="${sanitizedText}"]`);
    if (el) {
      console.log("Found message element:", el);
      el.scrollIntoView({ behavior: "smooth", block: "center" });
      el.classList.add("bg-yellow-100");
      setTimeout(() => el.classList.remove("bg-yellow-100"), 2000);
    } else {
      console.warn(`Message not found: Text=${messageText}`);
    }
  };

  const handleNotificationClick = async (notification: ChatNotification) => {
    console.log("Notification clicked:", notification);
    if (!Array.isArray(chatList)) {
      console.error("chatList is not an array:", chatList);
      return;
    }

    // Prioritize group messages (all notifications are GROUP_MESSAGE)
    let targetChat: User | undefined;
    if (notification.metadata.groupId) {
      targetChat = chatList.find(
        (chat) => chat.isGroup && chat.groupId === notification.metadata.groupId
      );
    } else if (notification.metadata.conversationId) {
      targetChat = chatList.find(
        (chat) =>
          !chat.isGroup &&
          chat.conversationId === notification.metadata.conversationId
      );
    }

    if (!targetChat) {
      console.warn(
        `Chat not found: groupId=${notification.metadata.groupId}, conversationId=${notification.metadata.conversationId}`,
        "chatList:",
        chatList
      );
      return;
    }

    console.log("Switching to chat:", targetChat);
    setSelectedUser(targetChat);

    if (notification.metadata.messageText) {
      setTimeout(() => {
        scrollToMessage(notification.metadata.messageText!);
      }, 500);
    } else {
      console.warn("No messageText in notification:", notification);
    }

    if (!notification.isRead) {
      try {
        console.log("Marking notification as read:", notification.id);
        await markNotificationRead(notification.id);
        setNotifications((prev) =>
          prev.map((n) =>
            n.id === notification.id ? { ...n, isRead: true } : n
          )
        );
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationsOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        bellRef.current &&
        !bellRef.current.contains(event.target as Node)
      ) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [notificationsOpen]);

  return (
    <div className="border w-full lg:h-[calc(100vh-110px)]  xl:h-[calc(100-vh-100px)] 2xl:h-[100vh-90px] overflow-hidden border-[#FF577F] pt-1">
      <div className="flex flex-row w-full lg:h-[calc(100vh-115px)]  xl:h-[calc(100-vh-100px)] 2xl:h-[100vh-90px] gap-x-4">
        {/* Left Chat */}
        <div className="w-[23%] lg-only:w-[25%] xl-only:w-[20%] 2xl-only:w-[15%] flex-shrink-0 flex flex-col">
          <div className="flex flex-row justify-between p-1">
            <div className="flex flex-row gap-2 justify-start items-center mb-2">
              <div className="border-[#FF577F] border w-7 h-7  rounded-md text-[#FF577F] flex items-center justify-center">
                <BackButton />
              </div>
              <div className="flex flex-row gap-1 items-center">
                <IoChatbubbleEllipsesOutline className="text-xl" />
                <h1 className="text-[20px] text-[#292D32] font-bold">Chat</h1>
              </div>
            </div>
            <div className="relative flex items-center mb-2">
              <img
                src={bell}
                alt="bell"
                className="w-5 h-5 cursor-pointer"
                onClick={() => setNotificationsOpen((prev) => !prev)}
                ref={bellRef}
              />
              {notifications.filter((n) => !n.isRead).length > 0 && (
                <span className="absolute top-[-4px] right-[-6px] bg-red-600 text-white text-[10px] px-1.5 py-[1px] rounded-full">
                  {notifications.filter((n) => !n.isRead).length}
                </span>
              )}
              {notificationsOpen && (
                <div
                  className="absolute top-6 right-0 z-50 w-[250px]"
                  ref={dropdownRef}
                >
                  <NotificationDropdown
                    notifications={notifications}
                    open={notificationsOpen}
                    setOpen={setNotificationsOpen}
                    notificationClick={handleNotificationClick}
                    loadMore={
                      page < totalPages ? loadMoreNotifications : undefined
                    }
                    dropdownRef={dropdownRef}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="border-t-2 w-full">
            <LeftChat onSelectUser={setSelectedUser} />
          </div>
        </div>

        {/* Center Chat */}
        <div
          className={`bg-[#EDF0F5] border-[1.5px] border-[#d5d7db] shadow-[0px_1px_1.55px_0px_#00000024] transition-all duration-300 ${
            isRightChatOpen
              ? "w-[57%] lg-only:w-[50%] xl-only:w-[60%] 2xl-only:w-[65%]"
              : "w-[77%] lg-only:w-[75%] xl-only:w-[80%] 2xl-only:w-[85%]"
          }`}
        >
          <CenterChat
            ref={centerChatRef}
            selectedUser={selectedUser}
            scrollToMessage={scrollToMessage}
            isRightChatOpen={isRightChatOpen}
            toggleRightChat={toggleRightChat}
          />
        </div>

        {/* Right Chat - Responsive width */}
        {isRightChatOpen && (
          <div className="w-[20%] lg-only:w-[25%] xl-only:w-[20%] 2xl-only:w-[20%] transition-all duration-300">
            <RightChat
              selectedUser={selectedUser}
              imageMedia={imageMedia}
              pdfMedia={pdfMedia}
              scrollToMessage={scrollToMessage}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;
