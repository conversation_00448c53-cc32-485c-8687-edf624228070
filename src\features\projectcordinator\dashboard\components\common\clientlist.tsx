import React, { useEffect, useState } from "react";
import { getClientsData } from "@/features/projectcordinator/api/api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { Link, useLocation } from "react-router-dom";
import { getAnnonatorProjectDetails } from "@/features/annotator/annonator_api/annonator_api";

type Client = {
  id: string;
  name: string;
  projects: number;
  email?: string;
  accountStatus?: string;
};

const ClientList: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const location = useLocation();

  useEffect(() => {
    const fetchClients = async () => {
      try {
        // Check if we're on a project details page
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get('id');

        if (projectId) {
          // If we're on a project details page, get the client from the project details
          const projectResponse = await getAnnonatorProjectDetails(projectId);

          if (projectResponse && projectResponse.data && projectResponse.data.createdBy) {
            // Use the client data from the project details
            const clientData = projectResponse.data.createdBy;

            // Create a client object with the data from the project details
            const client = {
              id: clientData.id,
              name: clientData.name,
              email: clientData.email,
              accountStatus: clientData.accountStatus,
              projects: clientData.clientOwnerId ? 0 : 1 // If clientOwnerId is null, this is the owner
            };

            setClients([client]);
          } else {
            // Fallback to the regular client list if project details don't have client info
            await fetchAllClients();
          }
        } else {
          // If we're not on a project details page, get all clients
          await fetchAllClients();
        }
      } catch (error) {
        console.error("Error fetching clients:", error);
        // Fallback to the regular client list if there's an error
        await fetchAllClients();
      } finally {
        setLoading(false);
      }
    };

    const fetchAllClients = async () => {
      try {
        const response = await getClientsData({} as any);

        // Transform the API response to match our component's data structure
        const transformedClients = response.data.map((client: any) => ({
          id: client.id,
          name: client.client?.name || "Unknown Client",
          email: client.client?.email,
          accountStatus: client.client?.accountStatus,
          projects: client.client?._count?.projectsOwned || 0
        }));

        setClients(transformedClients);
      } catch (error) {
        console.error("Error fetching all clients:", error);
        setClients([]);
      }
    };

    fetchClients();
  }, [location.search]);

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:h-[150px] xl-only:h-[186px] 2xl-only:h-[200px] flex flex-col">
      <div className="flex justify-between items-center lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4">
        <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold font-poppins">Clients</h2>
       <Link to="/coordinator/projectdetails/clients">
        <button className="lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-semibold lg-only:px-2 lg-only:py-0.5 xl-only:px-3 xl-only:py-1 2xl-only:px-4 2xl-only:py-1.5 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all">
          View All
        </button>
       </Link>
      </div>

      <ul className="overflow-y-auto pr-1">
        {loading ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">Loading clients...</p>
        ) : clients.length === 0 ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">No clients found</p>
        ) : (
          clients.map((client, index) => (
            <li key={client.id || index} className="flex items-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4 last:mb-0">
              <img
                src={getAvatarUrl(client.name)}
                alt="client avatar"
                className="lg-only:w-5 lg-only:h-5 xl-only:w-6 xl-only:h-6 2xl-only:w-7 2xl-only:h-7 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-[#282828]">
                    {client.name.split(" ").slice(0, 2).join(" ")}
                  </p>
                  {client.accountStatus && (
                    <span className={`lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] lg-only:px-1 lg-only:py-0.5 xl-only:px-1.5 xl-only:py-0.5 2xl-only:px-2 2xl-only:py-0.5 rounded-full text-white ${
                      client.accountStatus === "ACTIVE" ? "bg-green-500" :
                      client.accountStatus === "INACTIVE" ? "bg-red-500" :
                      client.accountStatus === "SUSPENDED" ? "bg-red-500" :
                      "bg-blue-500"
                    }`}>
                      {client.accountStatus.toLowerCase()}
                    </span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <p className="lg-only:text-[8px] xl-only:text-[10px] 2xl-only:text-xs text-[#727272]">
                    {client.projects} {client.projects === 1 ? "project" : "projects"}
                  </p>
                 
                </div>
              </div>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default ClientList;
