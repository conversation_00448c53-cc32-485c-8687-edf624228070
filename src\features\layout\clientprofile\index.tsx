// @ts-ignore
import React, { useState } from 'react'
import ProfileUserallData from './component/profileuseralldata'
import BillingData from './component/billingdata'
import AdmiinAndOtherProfile from './component/admiinand_otherprofile'
import { BackButton } from '@/_components/common'
import { Button } from '@/components/ui/button'
import { useSelector } from 'react-redux'
import { toast } from 'react-toastify'

const ClientProfileRoute = () => {
  const [isEditing, setIsEditing] = useState(false)
  const { user } = useSelector((state: any) => state.auth)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleEditClick = () => {
    setIsEditing(true)
  }

  const handleSaveSuccess = () => {
    setIsEditing(false)
    setRefreshKey(prev => prev + 1)
    toast.success('Profile updated successfully')
  }

  const handleCancel = () => {
    setIsEditing(false)
    setRefresh<PERSON><PERSON>(prev => prev + 1)
    toast.info('Changes discarded')
  }

  if (user?.role !== 'CLIENT') {
    return <AdmiinAndOtherProfile />
  }

  return (
    <div className='w-full flex flex-col px-5'>
      <div className='flex flex-row justify-between mt-5'>
        <div className='flex justify-center items-center text-lg font-semibold'>
          <BackButton/>
          <span>Profile</span>
        </div>

        {!isEditing && (
          <Button 
            className='px-12 bg-[#D53148] hover:bg-[#b8293c]'
            onClick={handleEditClick}
          >
            Edit
          </Button>
        )}
      </div>
      
      <div>
        <ProfileUserallData 
          key={refreshKey}
          isEditing={isEditing} 
          onSaveSuccess={handleSaveSuccess}
          onCancel={handleCancel}
        />
      </div>
      
      <BillingData />
    </div>
  )
}

export default ClientProfileRoute