"use client";
import { useState, useEffect } from "react";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { GetClientCordinatorShiftNotification } from "./Annotatornotification_api/annonatornotification_api";
import { getcordinatorclientLeaves } from "./Annotatornotification_api/annonatornotification_api";
import AnnotaotrShiftNotification from "./annonatorshiftaprrovalrejectfile";
import AnnotatorLeaveNotification from "./annonatorleaveprrovalrejectfile";
import { toast } from "react-toastify";

interface ShiftRequest {
  type: "SHIFT";
  id: string;
  annotatorId: string;
  requestedById: string;
  newFrom: string;
  newTo: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  reason: string | null;
  approvedById: string | null;
  annotator: {
    id: string;
    name: string;
    email: string;
  };
  requestedBy: {
    id: string;
    name: string;
    email: string;
  };
  approvedBy: {
    id: string;
    name: string;
    email: string;
  } | null;
}

interface LeaveRequest {
  type: "LEAVE";
  id: string;
  annotatorId: string;
  startDate: string;
  endDate: string;
  reason: string;
  status: string;
  rejectionReason: string | null;
  approvedById: string | null;
  approvedAt: string | null;
  createdAt: string;
  updatedAt: string;
  annotator: {
    id: string;
    name: string;
    email: string;
  };
}

type Notification = ShiftRequest | LeaveRequest;

export default function AnnonatorNotification() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        // Fetch all shift requests
        const shiftData = await GetClientCordinatorShiftNotification();
        const shiftNotifications: ShiftRequest[] = (shiftData.data || []).map(
          (item: ShiftRequest) => ({
            ...item,
            type: "SHIFT",
          })
        );

        // Fetch all leave requests
        const leaveData = await getcordinatorclientLeaves();
        const leaveNotifications: LeaveRequest[] = (leaveData.data.data || []).map(
          (item: LeaveRequest) => ({
            ...item,
            type: "LEAVE",
          })
        );

        // Combine shift and leave notifications
        setNotifications([...shiftNotifications, ...leaveNotifications]);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        toast.error("Failed to fetch notifications");
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  return (
    <div className="w-full mx-auto px-6 py-4">
      <div className="flex gap-5 p-2 items-center justify-start mb-6">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h2 className="text-[30px] font-bold">Notifications</h2>
      </div>

      {loading ? (
        <div className="text-center py-10">Loading notifications...</div>
      ) : (
        <>
          {/* Pending Shift Notifications Section */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold mb-4">Shift Change Requests</h3>
            <AnnotaotrShiftNotification />
          </div>

          {/* Pending Leave Notifications Section */}
          <div className="mb-8">
            <h3 className="text-xl font-semibold mb-4">Leave Requests</h3>
            <AnnotatorLeaveNotification />
          </div>

          {notifications.length === 0 && (
            <div className="text-center text-[16px] font-semibold font-poppins py-10 text-gray-500">
              No notifications
            </div>
          )}
        </>
      )}
    </div>
  );
}