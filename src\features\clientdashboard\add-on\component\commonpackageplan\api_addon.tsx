// api_addon.tsx
import { customAxios } from "@/utils/axio-interceptor";

export interface QuestionnaireData {
  packageId: string;
  availableFrom: string;
  availableTo: string;
  timezone: string;
  industry: string;
  category: string;
  description?: string;
}

export const submitQuestionnaireData = async (data: QuestionnaireData) => {
  try {
    // Log the data being sent to the API
    console.log("Sending data to API:", data);

    // Make the PATCH request to the endpoint
    const response = await customAxios.patch("/v1/clients/details", data);

    // Log the response from the API
    console.log("API response:", response.data);

    return response.data;
  } catch (error: any) {
    console.error("Error submitting questionnaire data:", error);

    // Log more details about the error
    if (error.response) {
      console.error("Error response data:", error.response.data);
      console.error("Error response status:", error.response.status);
    }

    throw error;
  }
};
