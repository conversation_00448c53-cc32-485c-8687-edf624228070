import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormLabel,
  FormMessage,
  FormItem,
} from "@/components/ui/form";
import { MoveRight, Loader2 } from "lucide-react";
import { useEffect } from "react";
import { useResponsive } from "@/hooks/use-responsive";

interface OtpInputProps {
  label?: string;
  description?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  onVerify: () => Promise<void>;
  isSubmitting?: boolean;
  screenSize?: "small" | "medium" | "large";
}

const OtpInput: React.FC<OtpInputProps> = ({
  label = "Enter One Time Password",
  description = "Enter the 4-digit code sent to your email.",
  value,
  onChange,
  error,
  onVerify,
  isSubmitting = false,
  screenSize = "small",
}) => {
  const { isLaptopMd, isLaptopLg } = useResponsive();
  const handleVerify = async () => {
    try {
      await onVerify();
    } catch (error) {
      console.error("Verification failed:", error);
    }
  };

  // 👇 Listen for Enter key when OTP is complete
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Enter" && value.length === 4 && !isSubmitting) {
        handleVerify();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [value, isSubmitting]); // dependencies

  // Get responsive styles based on screen size
  const getStyles = () => {
    // Use either the prop or the hook for responsive styling
    const size =
      screenSize === "large" || isLaptopLg
        ? "large"
        : screenSize === "medium" || isLaptopMd
        ? "medium"
        : "small";

    if (size === "large") {
      // 4K/2560px
      return {
        container: " space-y-8",
        labelText: "text-[36px] text-[#282828]",
        descriptionText: "text-[18px] text-[#757575] mt-2",
        otpGroup: "flex flex-row gap-4 outline-none mt-4",
        otpSlot: "rounded-lg h-20 w-20 bg-[#F9EFEF]",
        button:
          "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-14 py-7 text-[20px] mt-6",
        buttonIcon: "ml-2 w-5 h-5",
        loaderIcon: "mr-3 h-5 w-5 animate-spin",
        errorMessage: "text-base mt-2",
      };
    } else if (size === "medium") {
      // 1440px
      return {
        container: " space-y-7",
        labelText: "text-[32px] text-[#282828]",
        descriptionText: "text-[16px] text-[#757575] mt-2",
        otpGroup: "flex flex-row gap-3 outline-none mt-3",
        otpSlot: "border-gradient rounded-lg h-16 w-16",
        button:
          "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-12 py-6 text-[18px] mt-5",
        buttonIcon: "ml-2 w-5 h-5",
        loaderIcon: "mr-2 h-5 w-5 animate-spin",
        errorMessage: "text-base mt-2",
      };
    } else {
      // 1024px (default)
      return {
        container: " space-y-6",
        labelText: "text-[28px] text-[#282828]",
        descriptionText: "text-[14px] text-[#757575]",
        otpGroup: "flex flex-row gap-2 outline-none",
        otpSlot: "border-gradient rounded-lg",
        button:
          "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-10 py-6 text-[16px]",
        buttonIcon: "ml-1 w-4 h-4",
        loaderIcon: "mr-2 h-4 w-4 animate-spin",
        errorMessage: "text-sm mt-1",
      };
    }
  };

  const styles = getStyles();

  return (
    <div className={styles.container}>
      <FormItem>
        <FormLabel className={styles.labelText}>{label}</FormLabel>
        <br />
        <FormDescription className={styles.descriptionText}>
          {description}
        </FormDescription>
        <FormControl>
          <InputOTP
            maxLength={4}
            value={value}
            onChange={onChange}
            data-testid="otp-input"
          >
            <InputOTPGroup className={styles.otpGroup}>
              <InputOTPSlot
                index={0}
                className={styles.otpSlot}
                data-testid="otp-slot-1"
              />
              <InputOTPSlot
                index={1}
                className={styles.otpSlot}
                data-testid="otp-slot-2"
              />
              <InputOTPSlot
                index={2}
                className={styles.otpSlot}
                data-testid="otp-slot-3"
              />
              <InputOTPSlot
                index={3}
                className={styles.otpSlot}
                data-testid="otp-slot-4"
              />
            </InputOTPGroup>
          </InputOTP>
        </FormControl>
        {error && (
          <FormMessage className={styles.errorMessage} data-testid="otp-error">
            {error}
          </FormMessage>
        )}
      </FormItem>

      <Button
        type="button"
        onClick={handleVerify}
        className={styles.button}
        disabled={isSubmitting}
        data-testid="verify-button"
      >
        {isSubmitting ? (
          <>
            <Loader2 className={styles.loaderIcon} />
            Verifying...
          </>
        ) : (
          <>
            Verify
            <MoveRight className={styles.buttonIcon} />
          </>
        )}
      </Button>
    </div>
  );
};

export default OtpInput;
