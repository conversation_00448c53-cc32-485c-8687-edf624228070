import { jsPDF } from "jspdf";
import logo from "@/assets/darklogo.png";

interface InvoiceData {
  provider: string;
  paymentId: string;
  amount: string;
  status: string;
  paymentMethod: string;
  date: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  price: string;
}

export const generateInvoicePdf = (invoiceData: InvoiceData) => {
  const doc = new jsPDF();

  // Header background
  doc.setFillColor(248, 249, 250);
  doc.rect(0, 0, 210, 50, 'F');

  // Add company logo (smaller size)
  try {
    doc.addImage(logo, 'PNG', 20, 15, 25, 10);
  } catch (error) {
    console.error("Error adding logo to PDF:", error);
  }

  // Company details (professional layout)
  doc.setFontSize(14);
  doc.setTextColor(30, 30, 30);
  doc.setFont("helvetica", "bold");
  doc.text("AGKRAFT TECHNOLOGIES", 140, 20);
  
  doc.setFontSize(9);
  doc.setFont("helvetica", "normal");
  doc.setTextColor(80, 80, 80);
  doc.text("Professional Annotation Services", 140, 26);
  doc.text("Email: <EMAIL>", 140, 31);
  doc.text("Phone: +****************", 140, 36);
  doc.text("www.agkraft.com", 140, 41);

  // Invoice title with accent line
  doc.setDrawColor(233, 28, 36);
  doc.setLineWidth(3);
  doc.line(20, 60, 190, 60);
  
  doc.setFontSize(28);
  doc.setTextColor(233, 28, 36);
  doc.setFont("helvetica", "bold");
  doc.text("INVOICE", 20, 75);

  // Invoice number and date (right aligned)
  doc.setFontSize(11);
  doc.setTextColor(60, 60, 60);
  doc.setFont("helvetica", "normal");
  doc.text(`Invoice #: ${invoiceData.paymentId}`, 190, 75, { align: "right" });
  doc.text(`Date: ${new Date(invoiceData.date).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })}`, 190, 82, { align: "right" });

  // Bill To section with background
  doc.setFillColor(250, 250, 250);
  doc.rect(20, 95, 85, 35, 'F');
  doc.setDrawColor(220, 220, 220);
  doc.rect(20, 95, 85, 35);

  doc.setFontSize(12);
  doc.setTextColor(233, 28, 36);
  doc.setFont("helvetica", "bold");
  doc.text("BILL TO:", 25, 105);
  
  doc.setFontSize(11);
  doc.setTextColor(40, 40, 40);
  doc.setFont("helvetica", "normal");
  doc.text(invoiceData.customerName, 25, 115);
  doc.text(invoiceData.customerEmail, 25, 122);

  // Payment details section
  doc.setFillColor(250, 250, 250);
  doc.rect(110, 95, 80, 35, 'F');
  doc.setDrawColor(220, 220, 220);
  doc.rect(110, 95, 80, 35);

  doc.setFontSize(12);
  doc.setTextColor(233, 28, 36);
  doc.setFont("helvetica", "bold");
  doc.text("PAYMENT DETAILS:", 115, 105);
  
  doc.setFontSize(10);
  doc.setTextColor(40, 40, 40);
  doc.setFont("helvetica", "normal");
  doc.text(`Method: ${invoiceData.paymentMethod}`, 115, 115);
  doc.text(`Status: ${invoiceData.status}`, 115, 122);
  // doc.text(`Provider: ${invoiceData.provider}`, 115, 129);

  // Services table
  const tableTop = 150;
  
  // Table header
  doc.setFillColor(233, 28, 36);
  doc.rect(20, tableTop, 170, 12, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(11);
  doc.setFont("helvetica", "bold");
  doc.text("DESCRIPTION", 25, tableTop + 8);
  doc.text("AMOUNT", 165, tableTop + 8, { align: "right" });

  // Table content
  doc.setFillColor(255, 255, 255);
  doc.rect(20, tableTop + 12, 170, 20, 'F');
  doc.setDrawColor(220, 220, 220);
  doc.rect(20, tableTop + 12, 170, 20);
  
  doc.setTextColor(40, 40, 40);
  doc.setFont("helvetica", "normal");
  doc.text(invoiceData.packageName, 25, tableTop + 22);
  doc.text(`$${invoiceData.price}`, 165, tableTop + 22, { align: "right" });

  // Subtotal and total section
  const totalSection = tableTop + 40;
  
  doc.setDrawColor(233, 28, 36);
  doc.setLineWidth(1);
  doc.line(130, totalSection, 190, totalSection);
  
  doc.setFontSize(12);
  doc.setTextColor(60, 60, 60);
  doc.text("Subtotal:", 130, totalSection + 10);
  doc.text(`$${invoiceData.price}`, 185, totalSection + 10, { align: "right" });
  
  doc.text("Tax:", 130, totalSection + 18);
  doc.text("$0.00", 185, totalSection + 18, { align: "right" });
  
  // Total with background
  doc.setFillColor(233, 28, 36);
  doc.rect(130, totalSection + 25, 60, 12, 'F');
  
  doc.setFontSize(14);
  doc.setTextColor(255, 255, 255);
  doc.setFont("helvetica", "bold");
  doc.text("TOTAL:", 135, totalSection + 33);
  doc.text(`$${invoiceData.amount}`, 185, totalSection + 33, { align: "right" });

  // Payment terms and notes
  doc.setFontSize(10);
  doc.setTextColor(80, 80, 80);
  doc.setFont("helvetica", "normal");
  doc.text("PAYMENT TERMS & CONDITIONS:", 20, totalSection + 55);
  doc.setFontSize(9);
  doc.text("• Payment is due within 30 days of invoice date", 20, totalSection + 62);
  doc.text("• Late payments may incur additional charges", 20, totalSection + 68);
  doc.text("• For any queries, please contact our billing department", 20, totalSection + 74);

  // Professional footer
  doc.setDrawColor(233, 28, 36);
  doc.setLineWidth(2);
  doc.line(20, 260, 190, 260);
  
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text("Thank you for choosing AGKRAFT Technologies for your annotation needs.", 105, 270, { align: "center" });
  doc.text(`Invoice generated on: ${new Date().toLocaleDateString('en-US')} | This is a computer-generated document.`, 105, 275, { align: "center" });

  return doc;
};
