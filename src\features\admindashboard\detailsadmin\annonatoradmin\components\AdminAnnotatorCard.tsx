import React from 'react';
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getAllPackages } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package";

// Interface matching the API response structure
interface AnnotatorProps {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  packageId: string;
  availableFrom: string | null;
  availableTo: string | null;
  _count: {
    annotatorProjects: number;
  };
  // Additional props for UI display
  image?: string;
  status?: string;
}

interface AdminAnnotatorCardProps {
  annotator: AnnotatorProps;
  openModal: (annotator: AnnotatorProps) => void;
}

const  AdminAnnotatorCard: React.FC<AdminAnnotatorCardProps> = ({
  annotator,
  openModal
}) => {
  const navigate = useNavigate();

  // Format date to dd/mm/yy
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear().toString().slice(-2)}`;
  };

  // Format shift timing
  const formatShiftTiming = () => {
    // If both values are null or empty strings, return "0"
    if ((!annotator.availableFrom || annotator.availableFrom === "") &&
      (!annotator.availableTo || annotator.availableTo === "")) {
      return "0";
    }

    // If availableFrom is a time string (not a date object)
    if (annotator.availableFrom && !annotator.availableFrom.includes('T')) {
      const fromTime = annotator.availableFrom || "0";
      const toTime = annotator.availableTo || "0";

      return `${fromTime} to ${toTime}`;
    }

    // If availableFrom is a date string
    if (annotator.availableFrom) {
      try {
        const fromTime = new Date(annotator.availableFrom);
        const fromHours = fromTime.getHours().toString().padStart(2, '0');
        const fromMinutes = fromTime.getMinutes().toString().padStart(2, '0');

        if (annotator.availableTo) {
          const toTime = new Date(annotator.availableTo);
          const toHours = toTime.getHours().toString().padStart(2, '0');
          const toMinutes = toTime.getMinutes().toString().padStart(2, '0');
          return `${fromHours}:${fromMinutes} to ${toHours}:${toMinutes}`;
        }

        return `${fromHours}:${fromMinutes}`;
      } catch (error) {
        // If there's an error parsing the date, return the raw values
        return `${annotator.availableFrom || "0"} to ${annotator.availableTo || "0"}`;
      }
    }

    return "0";
  };

  // Generate image URL using dicebear
  const getCustomAvatarUrl = (name: string): string => {
    if (!name) return "";

    // Get the first name (first word)
    const firstName = name.trim().split(" ")[0];

    // Take the first two letters of the first name
    const initials = firstName.length >= 2
      ? firstName.substring(0, 2).toUpperCase()
      : firstName.charAt(0).toUpperCase();

    return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}`;
  };

  const imageUrl = annotator.image || getCustomAvatarUrl(annotator.name);

  // Custom hook to get package names
  const usePackageNames = () => {
    const { data, isLoading } = useQuery({
      queryKey: ["packageList"],
      queryFn: getAllPackages,
    });

    return { data, isLoading };
  };

  // Get package data
  const { data: packageData, isLoading: isPackageLoading } = usePackageNames();

  // Convert package ID to a readable subscription name
  const getSubscriptionName = (packageId: string) => {
    if (isPackageLoading || !packageData || !packageData.data || !packageData.data.packages) {
      return packageId || "Loading...";
    }

    // Check if packageId looks like a UUID (has dashes and is long)
    const isUuid = packageId && packageId.includes('-') && packageId.length > 30;

    if (!isUuid) {
      // If it's not a UUID, it might already be a name, so return it as is
      return packageId || "No Package";
    }

    // Find the package with the matching ID
    const packageInfo = packageData.data.packages.find((pkg: any) => pkg.id === packageId);

    if (packageInfo) {
      return packageInfo.name;
    } else {
      // If we can't find the package, return the ID
      return packageId || "No Package";
    }
  };

  return (
    <div className="border border-[#FF577F] rounded-lg shadow-[0px_27px_48px_0px_#00000014] bg-white flex flex-col gap-y-2 h-full lg:p-3 xl:p-3 2xl:p-5">
      <div className="flex items-center lg:gap-2 xl:gap-3 2xl:gap-4">
        <img
          src={imageUrl}
          alt={annotator.name}
          className="rounded-full lg:w-10 lg:h-10 xl:w-12 xl:h-12 2xl:w-14 2xl:h-14"
        />
        <div className="flex flex-col w-full">
          <div className="flex justify-between items-center w-full">
            <h3 className="lg:text-sm xl:text-sm 2xl:text-base font-semibold">{annotator.name.split(" ").slice(0, 1).join(" ")}</h3>
            <button className="bg-[#5AB24A] hover:bg-[#7ece70] text-white rounded-2xl lg:px-2 lg:py-0.5 lg:text-[10px] xl:px-2.5 xl:py-0.5 xl:text-[11px] 2xl:px-3 2xl:py-1 2xl:text-xs">
              {annotator.status || "active"}
            </button>
          </div>
          <p className="text-gray-500 lg:text-xs xl:text-xs 2xl:text-sm">{annotator.email}</p>
        </div>
      </div>

      <div className="flex flex-col flex-grow gap-1 lg:mt-2 lg:px-2 lg:space-y-1.5 lg:text-[11px] xl:mt-3 xl:px-3 xl:space-y-2 xl:text-[12px] 2xl:mt-4 2xl:px-4 2xl:space-y-2.5 2xl:text-sm">
        <div className="flex justify-between">
          <span className="text-[#5B5B5B] font-medium lg:w-[90px] xl:w-[100px] 2xl:w-[120px] inline-block">Shift Timing:</span>
          <span>{formatShiftTiming()}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-[#5B5B5B] font-medium lg:w-[90px] xl:w-[100px] 2xl:w-[120px] inline-block">Projects:</span>
          <span>{annotator._count.annotatorProjects}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-[#5B5B5B] font-medium lg:w-[90px] xl:w-[100px] 2xl:w-[120px] inline-block">Joined:</span>
          <span>{formatDate(annotator.createdAt)}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-[#5B5B5B] font-medium lg:w-[90px] xl:w-[100px] 2xl:w-[120px] inline-block">Subscription:</span>
          <span>{getSubscriptionName(annotator.packageId)}</span>
        </div>
      </div>

      <div className="lg:flex lg:justify-between lg:gap-x-2 lg:mt-auto lg:pl-2 lg:pt-2 xl:pl-3 xl:pt-2 2xl:flex 2xl:justify-center 2xl:gap-x-4 2xl:pt-4 xl:w-full lg:w-full 2xl:w-full">

        {/* // In AdminAnnotatorCard.tsx, update the button click handler to pass annotator object without annotatorId */}
        <button
          onClick={() => openModal(annotator)} // Pass annotator object directly
          className="border w-1/2 border-[#FF577F] text-[#FF577F] rounded-lg whitespace-nowrap lg:px-3 lg:py-1.5 lg:text-[10px] xl:px-4 xl:py-2 xl:text-[12px] 2xl:px-5 2xl:py-2.5 2xl:text-xs 2xl:flex-1 2xl:text-center"
        >
          Change Shift
        </button>
        <button
          onClick={() => navigate(`/admin/attendance?id=${annotator.id}&name=${encodeURIComponent(annotator.name)}`)}
          className="bg-gradient-to-r w-1/2 from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] text-white rounded-lg whitespace-nowrap lg:px-3 lg:py-1.5 lg:text-[11px] xl:px-4 xl:py-2 xl:text-[12px] 2xl:px-5 2xl:py-2.5 2xl:text-xs 2xl:flex-1 2xl:text-center"
        >
          Attendance Log
        </button>
      </div>
    </div>
  );
};

export default AdminAnnotatorCard;
