import React, { useState } from "react";
import avatar from "@/assets/client1.png";
// import { useNavigate } from "react-router-dom";
import { X, ExternalLink } from "lucide-react";


type AnnotatorStatus = "View Project";

type Annotator = {
  name: string;
  tasksAssigned: number;
  status: AnnotatorStatus;
};

const annotators: Annotator[] = [
  { name: "Enam<PERSON> sah", tasksAssigned: 5, status: "View Project" },
  { name: "Enamuel sah", tasksAssigned: 5, status: "View Project" },
  { name: "En<PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
  { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
  { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
  { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
  { name: "En<PERSON>uel sah", tasksAssigned: 5, status: "View Project" },
  { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
];

// const statusColors: Record<AnnotatorStatus, string> = {
//   "View Project":
//     "bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white",
// };

const CoworkerDetails: React.FC = () => {
  const [showPopup, setShowPopup] = useState(false);

  const togglePopup = () => {
    setShowPopup((prev) => !prev);
  };
  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg p-4 w-[261px] h-[246px] flex flex-col relative">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-lg font-semibold font-poppins">Co-Workers</h2>
        {/* <button
          onClick={() => navigate("/dashboard/task-details/projects")}
          className="text-xs font-semibold px-3 py-1 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all"
        >
          View All
        </button> */}
      </div>
      <ul className="CustomScroll  overflow-y-auto pr-1">
        {annotators.map((annotator, index) => (
          <li key={index} className=" flex items-center gap-3 mb-3 last:mb-0">
            <img
              src={avatar}
              alt="avatar"
              className="w-6 h-6 rounded-full object-cover"
            />{" "}
            <div className="flex-1">
              <div className="flex gap-2">
                <p className="font-medium text-xs text-[#282828]">
                  {annotator.name}
                </p>
                <button
                  onClick={togglePopup}
                  className="flex items-center justify-center w-7 h-4 rounded-full bg-gray-200 hover:bg-gray-300"
                >
                  <div className="flex space-x-1">
                    <span className="w-1 h-1 bg-black rounded-full"></span>
                    <span className="w-1 h-1 bg-black rounded-full"></span>
                    <span className="w-1 h-1 bg-black rounded-full"></span>
                  </div>
                </button>
              </div>
              <p className="text-[10px] text-[#727272]">
                {annotator.tasksAssigned} task assigned
              </p>
            </div>
            {/* <button
              onClick={() => navigate("/dashboard/project-details")}
              className={`text-white text-xs px-2 py-1 rounded-md ${
                statusColors[annotator.status]
              }`}
            >
              {annotator.status}
            </button> */}
          </li>
        ))}
      </ul>

      {/* Popup */}
      {showPopup && (
        <div className="w-[261px] rounded-xl border border-pink-300 p-4 shadow-lg bg-white absolute top-10 left-0 z-10">
          {/* Top Icons */}
          <div className="absolute top-2 right-2 flex space-x-2">
            <ExternalLink className="w-4 h-4 cursor-pointer" />
            <X
              className="w-4 h-4 cursor-pointer"
              onClick={() => setShowPopup(false)}
            />
          </div>

          {/* Avatar and Name */}
          <div className="flex gap-2 items-center mt-4">
            <img
              src={avatar}
              alt="avatar"
              className="w-16 h-16 rounded-full object-cover"
            />
            <div className="flex flex-col">
              <h2 className="font-medium text-base mt-2">Kevin Kooper</h2>
              <p className="text-sm text-gray-500"><EMAIL></p>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-center gap-2 mt-4">
            <button className="w-full border border-pink-500 text-pink-500 rounded-lg px-4 py-2 text-xs font-semibold hover:bg-pink-50">
              Group Chat
            </button>
            <button className="w-full bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white rounded-lg px-4 py-2 text-sm font-medium hover:opacity-90">
              Chat
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CoworkerDetails;
