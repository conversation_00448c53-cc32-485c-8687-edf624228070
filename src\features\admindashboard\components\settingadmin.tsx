import { useState } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import Delete from './common/deletrsetting';
import { ChangePassword } from './faq_setting_api/setting/setting_api';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Button } from '@/components/ui/button';

export default function SettingFileadmin() {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  const toggleVisibility = (field: 'old' | 'new' | 'confirm') => {
    setShowPassword((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const validatePassword = (password: string) => {
    const validations: string[] = [];
    if (!/[A-Z]/.test(password)) validations.push('Must include at least one uppercase letter');
    if (!/[0-9]/.test(password)) validations.push('Must include at least one number');
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) validations.push('Must include at least one special character');
    if (password.length < 8) validations.push('Must be at least 8 characters long');
    return validations;
  };

  const handleSave = async () => {
    console.log('Save clicked');
    const newErrors: Record<string, string> = {};

    if (!oldPassword) newErrors.oldPassword = 'Old password is required';
    if (!newPassword) {
      newErrors.newPassword = 'New password is required';
    } else {
      const passwordErrors = validatePassword(newPassword);
      if (passwordErrors.length > 0) {
        newErrors.newPassword = passwordErrors.join(', ');
      }
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Confirm password is required';
    } else if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (Object.keys(newErrors).length > 0) {
      console.log('Validation errors:', newErrors);
      setErrors(newErrors);
      return;
    }

    try {
      console.log('Calling API with:', { oldPassword, newPassword, confirmPassword });
      await ChangePassword({ oldPassword, newPassword, confirmPassword });

      toast.success('Password changed successfully!', {
        position: 'top-right',
        autoClose: 3000,
      });

      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setErrors({});
    } catch (error: any) {
      const msg = error?.response?.data?.message || 'Something went wrong. Please try again.';
      console.error('API error:', msg);
      toast.error(msg, {
        position: 'top-right',
        autoClose: 3000,
      });
    }
  };

  const renderInput = (
    label: string,
    value: string,
    setValue: (v: string) => void,
    error: string | undefined,
    show: boolean,
    toggle: () => void,
    type: 'old' | 'new' | 'confirm'
  ) => {
    const labelClass = {
      old: 'gap-[5rem]',
      new: 'gap-[4.5rem]',
      confirm: 'gap-[3rem]',
    };

    return (
      <div className={`flex flex-row items-center justify-start ${labelClass[type]}`}>
        <label className="block font-medium">{label}</label>
        <div className="flex flex-col">
          <div className="relative w-[416.25px] border-gradient rounded-md">
            <input
              type={show ? 'text' : 'password'}
              value={value}
              onChange={(e) => setValue(e.target.value)}
              className="w-full p-2 bg-[#F9EFEF] rounded-md pr-10 focus:ring focus:outline-none"
              placeholder={`Enter ${label}`}
            />
            <span
              onClick={toggle}
              className="absolute right-2 top-2.5 cursor-pointer text-[#cea6b7]"
            >
              {show ? <Eye size={20} /> : <EyeOff size={20} />}
            </span>
          </div>
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>
      </div>
    );
  };

  return (
    <div className="px-14 w-full font-sans">
      <h2 className="text-2xl font-bold mb-6">Settings</h2>

      <div className="rounded-xl bg-[#F9EFEF] p-6 shadow-md border-gradient">
        <h3 className="text-lg font-semibold mb-4 text-[#5E5E5E]">Password & Security</h3>

        <div className="space-y-6">
          {renderInput(
            'Old Password:',
            oldPassword,
            setOldPassword,
            errors.oldPassword,
            showPassword.old,
            () => toggleVisibility('old'),
            'old'
          )}
          {renderInput(
            'New Password:',
            newPassword,
            setNewPassword,
            errors.newPassword,
            showPassword.new,
            () => toggleVisibility('new'),
            'new'
          )}
          {renderInput(
            'Confirm Password:',
            confirmPassword,
            setConfirmPassword,
            errors.confirmPassword,
            showPassword.confirm,
            () => toggleVisibility('confirm'),
            'confirm'
          )}

          <div className="flex flex-row items-end justify-end">
            <Button
            variant={"gradient"}
              onClick={handleSave}
              className=" text-white font-semibold px-10 py-2 rounded-md hover:opacity-90 transition"
            >
              Save
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-0 mt-4">
        <Delete />
      </div>
    </div>
  );
}