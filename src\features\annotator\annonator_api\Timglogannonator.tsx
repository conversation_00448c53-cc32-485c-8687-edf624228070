import { customAxios } from "@/utils/axio-interceptor";


//start break annoator
export const startBreakAnnonator = async () => {
  try {
    const response = await customAxios.post("/v1/attendance/start-break");
    return response.data;
  } catch (error) {
    console.error("Error starting break:", error);
    throw error;
  }
};





//end break annonator

export const endBreakAnnonator = async () => {
  try {
    const response = await customAxios.post("/v1/attendance/end-break");
    return response.data;
  } catch (error) {
    console.error("Error starting break:", error);
    throw error;
  }
};





//clock in annonator

export const clockInAnnonator = async () => {
  try {
    const response = await customAxios.post("/v1/attendance/clock-in");
    return response.data;
  } catch (error) {
    console.error("Error starting break:", error);
    throw error;
  }
};





//clockout annonator

export const clockOutAnnonator = async () => {
  try {
    const response = await customAxios.post("/v1/attendance/clock-out");
    return response.data;
  } catch (error) {
    console.error("Error starting break:", error);
    throw error;
  }
};

