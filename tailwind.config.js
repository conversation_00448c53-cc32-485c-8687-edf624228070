/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    extend: {
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          1: "hsl(var(--chart-1))",
          2: "hsl(var(--chart-2))",
          3: "hsl(var(--chart-3))",
          4: "hsl(var(--chart-4))",
          5: "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        progress: {
          "0%": {
            width: "0%"
          },
          "100%": {
            width: "100%"
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "progress": "progress 5s linear forwards",
      },
      fontFamily: {
        poppins: ["Poppins", "sans-serif"],
      },
    },
    screens: {
      // Default Tailwind breakpoints - these will be hidden in the UI
      'sm': '640px',  // Not used in the UI (hidden)
      'md': '768px',  // Not used in the UI (hidden)

      // Custom breakpoints for PC screens - these are the only supported screens
      'lg': '1024px',    // Laptop Small (1024px)
      'lg-mid': '1200px', // Mid-size between lg and xl
      'xl': '1440px',    // Laptop Medium (1440px)
      'xl-mid': '1800px', // Mid-size between xl and 2xl
      '2xl': '2560px',   // Laptop Large/4K (2560px)

      // Custom ranges for specific screen sizes only
      'lg-only': { 'min': '1024px', 'max': '1439px' },
      'xl-only': { 'min': '1440px', 'max': '2559px' },
      '2xl-only': { 'min': '2560px' },

      // Special breakpoint to hide content on mobile/tablet
      'desktop': { 'min': '1024px' }, // Only show on desktop (1024px and above)
    },
  },
  plugins: [
    require("tailwindcss-animate"),


    // tailwind.config.js के plugins में
    function ({ addUtilities }) {
      addUtilities({
        '.border-gradient': {
          position: 'relative',
          overflow: 'hidden',
        },
        '.border-gradient::before': {
          content: '""',
          position: 'absolute',
          inset: '0',
          padding: '1px',
          background:
            'linear-gradient(to right,#E91C24,#FF577F,#FF6ABF,#BF73E6,#7D90E9,#45ADE2)',
          borderRadius: 'inherit',
          '-webkit-mask':
            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          '-webkit-mask-composite': 'xor',   // ⭐ जोड़ें
          maskComposite: 'exclude',
          pointerEvents: 'none',
        },
      });
    },



    // ✅ Add this new plugin for no-spinner
    function ({ addUtilities }) {
      addUtilities({
        ".no-spinner": {
          "-moz-appearance": "textfield",
          "&::-webkit-inner-spin-button": {
            "-webkit-appearance": "none",
            margin: "0",
          },
          "&::-webkit-outer-spin-button": {
            "-webkit-appearance": "none",
            margin: "0",
          },
        },
      });
    },
  ]
};