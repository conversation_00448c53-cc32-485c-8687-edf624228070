import React, { useEffect, useState } from "react";
import { USER_ROLES } from "@/utils/constants";
import LogoutBar from "@/features/auth/routes/LogoutBar";
import SidebarSection from "./SidebarSection";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { customAxios } from "@/utils/axio-interceptor";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { CircleHelp } from "lucide-react";
import { Link } from "react-router-dom";

interface SidebarProps {
  sidebarData: {
    roles: USER_ROLES[];
    items: {
      section: string;
      items: {
        name: string;
        path?: string;
        icon: React.ReactNode;
        children?: {
          name: string;
          path: string;
        }[];
      }[];
    }[];
  }[];
  userRole: USER_ROLES;
  navbar: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({
  sidebarData,
  userRole,
  // navbar
}) => {
  const [openDropdowns, setOpenDropdowns] = useState<{
    [key: string]: boolean;
  }>({});
  const [hasAnnotators, setHasAnnotators] = useState(false);
  const [loading, setLoading] = useState(true);
  const authUser = useAppSelector((state) => state.auth.user);

  const user = useSelector((state: RootState) => state.auth.user);
  if (!user) return null;
  useEffect(() => {
    const checkAnnotatorCount = async () => {
      try {
        if (authUser?.role === "CLIENT") {
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          setHasAnnotators(response.data?.count > 0);
        } else {
          // Coworkers don't need annotators
          setHasAnnotators(true);
        }
      } catch (error) {
        console.error("Error checking annotator count:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAnnotatorCount();
  }, [authUser]);

  const toggleDropdown = (name: string) => {
    // Allow "Billing" dropdown for CLIENT even if hasAnnotators is false
    if (user.role === "CLIENT" && name === "Billing") {
      setOpenDropdowns((prev) => ({
        ...prev,
        [name]: !prev[name],
      }));
    } else if (hasAnnotators) {
      setOpenDropdowns((prev) => ({
        ...prev,
        [name]: !prev[name],
      }));
    }
  };

  const filteredSidebar = sidebarData.filter((section) =>
    section.roles.includes(userRole)
  );

  // Split Main and More
  const mainSections = filteredSidebar.filter((section) =>
    section.items.some((item) => item.section === "Main")
  );

  const moreSections = filteredSidebar.filter((section) =>
    section.items.some((item) => item.section === "More")
  );

  // Define styles using Tailwind classes
  const styles = {
    sectionContainer: "border-b-2 border-[#eeeeee] lg:p-1.5 xl:p-2 2xl:p-3",
    sectionTitle:
      "text-red-500 uppercase font-semibold mb-2 lg:text-[9px] xl:text-[10px] 2xl:text-[12px]",
    itemContainer: "text-[#757575] font-semibold space-y-2",
    link: "flex items-center gap-2 rounded-md cursor-pointer lg:px-1.5 lg:py-[8px] xl:px-2 xl:py-[10px] 2xl:px-3 2xl:py-[12px]",
    linkActive: "bg-[#f6f6f6] text-[#FF577F]",
    linkInactive: "hover:bg-[#f6f6f6] hover:text-[#FF577F]",
    iconTextContainer: "flex flex-row lg:gap-x-3 xl:gap-x-5 2xl:gap-x-6",
    icon: "lg:text-[18px] xl:text-[22px] 2xl:text-[24px]",
    text: "lg:text-[13px] xl:text-[15px] 2xl:text-[16px]",
    dropdownButton:
      "flex items-center gap-4 hover:text-[#FF577F] rounded-md cursor-pointer w-full text-left text-[#757575] hover:bg-gray-200 lg:p-1.5 xl:p-2 2xl:p-3",
    dropdownContainer:
      "relative ml-4 pl-4 lg:text-[12px] xl:text-[13px] 2xl:text-[14px]",
    dropdownLine:
      "h-[calc(100%-23px)] w-2 absolute -left-[5px] rounded border-l-4 border-[#F6F6F6]",
    dropdownItem:
      "relative block py-3 pl-6 text-gray-600 hover:rounded-md hover:bg-[#f6f6f6] hover:text-[#FF577F]",
    dropdownItemActive: "text-[#FF577F] font-semibold",
    dropdownItemLine:
      "absolute -left-[20px] top-3 w-5 h-3 border-b-4 border-l-2 rounded-l-2xl border-[#F6F6F6] rounded-tl-md",
    logoutContainer: "w-full mt-4",
  };

  if (loading) {
    return (
      <aside className="h-full bg-white border-r-2 overflow-hidden shadow-md transition-all w-[280px] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
      </aside>
    );
  }

  return (
    <aside className="h-full bg-white border-r-2 overflow-hidden shadow-md transition-all lg:w-[220px] xl:w-[250px] 2xl:w-[280px]">
      <div className="flex flex-col justify-between h-full lg:p-3 xl:p-5 2xl:p-6">
        {/* Main Section */}
        <div className="flex flex-col gap-6 overflow-y-auto CustomScroll lg:h-[42%] lg:pr-2 xl:h-[45%] xl:pr-2 2xl:h-[45%] 2xl:pr-3">
          {mainSections.map((section, sectionIndex) => (
            <SidebarSection
              key={sectionIndex}
              section={section}
              sectionType="Main"
              openDropdowns={openDropdowns}
              toggleDropdown={toggleDropdown}
              setOpenDropdowns={setOpenDropdowns}
              styles={styles}
              hasAnnotators={hasAnnotators}
            />
          ))}
        </div>

        {/* More Section */}
        <div className="flex flex-col gap-6 overflow-y-auto CustomScroll mt-5 lg:h-[32%] lg:pr-2 xl:h-[35%] xl:pr-2 2xl:h-[35%] 2xl:pr-3">
          {moreSections.map((section, sectionIndex) => (
            <SidebarSection
              key={sectionIndex}
              section={section}
              sectionType="More"
              openDropdowns={openDropdowns}
              toggleDropdown={toggleDropdown}
              setOpenDropdowns={setOpenDropdowns}
              styles={styles}
              hasAnnotators={hasAnnotators}
            />
          ))}
        </div>

        <div className="">
          {(user.role === "CLIENT" || user.role === "COWORKER") && (
            <Link to="/dashboard/support">
              <div className="flex flex-row gap-2 justify-start text-[#757575] font-medium text-[16px] ml-4 items-center">
                <CircleHelp />
                <p>Support</p>
              </div>
            </Link>
          )}
        </div>
        {/* Logout Button */}
        <div className="w-full mt-4">
          <LogoutBar />
        </div>
      </div>
    </aside>
  );
};

export default Sidebar; 