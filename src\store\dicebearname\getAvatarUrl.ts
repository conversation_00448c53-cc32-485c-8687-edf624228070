// utils/getAvatarUrl.ts

export const getAvatarUrl = (name: string): string => {
    if (!name) return "";

    // Split name into parts and get the first word
    const firstName = name.trim().split(" ")[0]; // Take only the first word

    // Take the first two letters of the first name
    const initials = firstName.substring(0, 2).toUpperCase(); // Make it uppercase

    return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}`;
  };
