import { useMutation, useQueryClient } from "@tanstack/react-query";
import { clockIn, clockOut, takeBreak, undoBreak } from "./annonator_api";
import { errorMessage } from "@/utils/errorHandler";
import { toast } from "react-toastify";

export function useClockINMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => clockIn(),
    onSettled: async (_data, error) => {
      if (error) {
        errorMessage(error);
      } else {
        toast.success("ClockedIN successfully");
        // Invalidate both matchmaking and packages queries to refresh the data
        await queryClient.invalidateQueries({ queryKey: ["attendencelog"] });
      }
    },
  });
}

export function useClockOUTMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => clockOut(),
    onSettled: async (_data, error) => {
      if (error) {
        errorMessage(error);
      } else {
        toast.success("ClockedOUT successfully");
        // Invalidate both matchmaking and packages queries to refresh the data
        await queryClient.invalidateQueries({ queryKey: ["attendencelog"] });
      }
    },
  });
}

export function useTakeBreakMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => takeBreak(),
    onSettled: async (_data, error) => {
      if (error) {
        errorMessage(error);
      } else {
        toast.success("Break started successfully");
        await queryClient.invalidateQueries({ queryKey: ["attendencelog"] });
      }
    },
  });
}

export function useUndoBreakMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => undoBreak(),
    onSettled: async (_data, error) => {
      if (error) {
        errorMessage(error);
      } else {
        toast.success("Break cancelled successfully");
        await queryClient.invalidateQueries({ queryKey: ["attendencelog"] });
      }
    },
  });
}
