"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom"; // ✅ React Router import
// Define the interface for the API response
interface ProjectType {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  coordinatorId: string;
  createdById: string;
  annotatorId: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  createdAt: string;
  updatedAt: string;
}

export const useCoordinatorColumns = (): ColumnDef<ProjectType>[] => {
  const navigate = useNavigate(); // ✅ Initialize navigation hook

  return [
    {
      accessorKey: "name", //proejct name
      header: () => (
        <div
          className="w-[200px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Project Name
        </div>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-[14px] font-medium text-center col-span-full">Annotator not assign any project</div>;
        }
        return <div className="text-[14px] font-medium">{row.getValue("name") as string}</div>;
      },
    },
    {
      accessorKey: "startDate",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Started on
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }

        // Format date to dd/mm/yy
        const dateString = row.getValue("startDate") as string;
        if (!dateString) return <div className="pl-4 text-[14px] font-medium">-</div>;

        try {
          const date = new Date(dateString);
          const day = String(date.getDate()).padStart(2, '0');
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const year = String(date.getFullYear()).slice(-2);
          return <div className="pl-4 text-[14px] font-medium">{`${day}/${month}/${year}`}</div>;
        } catch (error) {
          return <div className="pl-4 text-[14px] font-medium">{dateString}</div>;
        }
      },
    },
    {
      accessorKey: "dueDate",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Duration
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }

        // Calculate duration between start and due date
        const project = row.original;
        const startDate = project.startDate;
        const dueDate = project.dueDate;

        if (!startDate || !dueDate) return <div className="pl-4 text-[14px] font-medium">-</div>;

        try {
          const start = new Date(startDate);
          const end = new Date(dueDate);
          const diffTime = Math.abs(end.getTime() - start.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // Format as weeks if more than 7 days
          if (diffDays >= 7) {
            const weeks = Math.floor(diffDays / 7);
            return <div className="pl-4 text-[14px] font-medium">{`${weeks} ${weeks === 1 ? 'Week' : 'Weeks'}`}</div>;
          }

          return <div className="pl-4 text-[14px] font-medium">{`${diffDays} ${diffDays === 1 ? 'Day' : 'Days'}`}</div>;
        } catch (error) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }
      },
    },
    {
      accessorKey: "priority",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Priority
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }

        const priority = row.getValue("priority") as string;
        let bgColor = "bg-[#2525AB]";

        // Set different background colors based on priority
        if (priority === "HIGH") bgColor = "bg-[#E91C24]";
        if (priority === "MEDIUM") bgColor = "bg-[#E96B1C]";
        if (priority === "LOW") bgColor = "bg-[#2525AB]";

        return (
          <div>
            <span className={`${bgColor} px-4 py-2 rounded-3xl text-white`}>
              {priority}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "createdById", //ye cleint ki id hai name aa sakta
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Posted by
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }
        return <div className="pl-4 text-[14px] font-medium">{row.getValue("createdById") as string}</div>;
      },
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }
        return (
          <div className="pl-4">
            <span className="border border-[#E96B1C] px-4 py-2 rounded-3xl text-[#E96B1C]">
              {row.getValue("status") as string}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }

        const project = row.original;
        return (
          <div className="pl-4">
            <Button
              variant={"gradient"}
              className="px-7 rounded-xl"
              onClick={() => navigate(`/coordinator/coordinatorproject-details?id=${project.id}&name=${encodeURIComponent(project.name || "")}`)}
            >
              View Details
            </Button>
          </div>
        );
      },
    },
  ];
};
