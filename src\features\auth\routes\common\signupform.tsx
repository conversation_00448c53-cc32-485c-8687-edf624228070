import React, { useState } from "react";
import ReCAPTCHAComponent from "./ReCAPTCHA";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { ClientSignUp } from "@/features/auth/api/client-api";
import { useResponsive } from "@/hooks/use-responsive";
import { OTPModal } from "../signup-otp";

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreePolicy: boolean;
  receiveMarketing: boolean;
}

const SignupForm: React.FC = () => {
  const navigate = useNavigate();
  const { isLaptopMd, isLaptopLg } = useResponsive();

  const [formData, setFormData] = useState<SignupFormData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreePolicy: false,
    receiveMarketing: false,
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState("");

  // Get responsive styles based on screen size
  const getStyles = () => {
    if (isLaptopLg) {
      // 4K/2560px
      return {
        container: "w-full mt-20",
        formContainer:
          "mx-auto max-w-3xl min-h-[400px] shadow-[0px_3px_48px_10px_#0000000F] px-10 py-6 rounded-2xl",
        heading: "text-[32px] font-bold font-inter text-[#282828] mb-4 mt-2",
        formSpacing: "space-y-5 w-full",
        inputGroup: "flex gap-6",
        inputContainer: "w-1/2",
        label: "block text-[16px] font-medium text-[#757575] mb-2",
        input: "w-full p-3 bg-[#F9EFEF] rounded-lg ",
        checkboxContainer: "space-y-3",
        checkboxGroup: "flex items-start gap-3",
        checkbox: "mt-1 w-5 h-5",
        checkboxText: "text-base text-gray-600",
        button:
          "px-14 py-4 text-lg bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all flex items-center justify-center",
        buttonIcon: "ml-3 w-5 h-5",
        loginText: "text-center mt-4 text-base text-gray-600",
      };
    } else if (isLaptopMd) {
      // 1440px
      return {
        container: "w-full mt-16",
        formContainer:
          "mx-auto max-w-2xl min-h-[380px] shadow-[0px_3px_48px_10px_#0000000F] px-8 py-5 rounded-2xl",
        heading: "text-[28px] font-bold font-inter text-[#282828] mb-3 mt-2",
        formSpacing: "space-y-4 w-full",
        inputGroup: "flex gap-5",
        inputContainer: "w-1/2",
        label: "block text-[15px] font-medium text-[#757575] mb-1.5",
        input: "w-full p-2.5  bg-[#F9EFEF] rounded-lg focus:outline-none ",
        checkboxContainer: "space-y-2.5",
        checkboxGroup: "flex items-start gap-2.5",
        checkbox: "mt-1 w-4 h-4",
        checkboxText: "text-sm text-gray-600",
        button:
          "px-12 py-3.5 text-base bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all flex items-center justify-center",
        buttonIcon: "ml-2.5 w-4.5 h-4.5",
        loginText: "text-center mt-3.5 text-sm text-gray-600",
      };
    } else {
      // 1024px (default)
      return {
        container: "w-full mt-14",
        formContainer:
          "mx-auto max-w-xl min-h-[350px] shadow-[0px_3px_48px_10px_#0000000F] px-6 py-3 rounded-2xl",
        heading: "text-[24px] font-bold font-inter text-[#282828] mb-2 mt-2",
        formSpacing: "space-y-3 w-full",
        inputGroup: "flex gap-4",
        inputContainer: "w-1/2",
        label: "block text-[14px] font-medium text-[#757575] mb-1",
        input: "w-full p-2  bg-[#F9EFEF] rounded-lg ",
        checkboxContainer: "space-y-2",
        checkboxGroup: "flex items-start gap-2",
        checkbox: "mt-1 w-4 h-4",
        checkboxText: "text-sm text-gray-600",
        button:
          "px-10 py-3 text-base bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all flex items-center justify-center",
        buttonIcon: "ml-2 w-4 h-4",
        loginText: "text-center mt-3 text-sm text-gray-600",
      };
    }
  };

  const styles = getStyles();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // NEW: Function to validate individual fields
  const validateField = (name: string, value: any): string | null => {
    const businessEmailPattern =
      /^[a-zA-Z0-9._%+-]+@(?!gmail\.com$|yahoo\.com$|hotmail\.com$|outlook\.com$)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const passwordPattern =
      /^(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    switch (name) {
      case "firstName":
        if (!value.trim()) return "First name is required.";
        break;
      case "lastName":
        if (!value.trim()) return "Last name is required.";
        break;
      case "email":
        if (!value.trim()) return "Email is required.";
        else if (!businessEmailPattern.test(value))
          return "Only business emails are allowed.";
        break;
      case "password":
        if (!value) return "Password is required.";
        else if (!passwordPattern.test(value))
          return "Password must be at least 8 characters, include 1 uppercase, 1 number, and 1 special character.";
        break;
      case "confirmPassword":
        if (value !== formData.password) return "Passwords do not match.";
        break;
      case "agreePolicy":
        if (!value) return "You must agree to the privacy policy and terms.";
        break;
      default:
        return null;
    }
    return null;
  };

  // UPDATED: handleChange with immediate validation for checkboxes

  // NEW: handleBlur for field validation when leaving a field
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === "checkbox" ? checked : value;

    const error = validateField(name, fieldValue);

    setErrors((prev) => {
      if (error) {
        return { ...prev, [name]: error };
      } else {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      }
    });
  };

  const validate = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Validate all fields
    Object.keys(formData).forEach((key) => {
      const error = validateField(key, formData[key as keyof SignupFormData]);
      if (error) {
        newErrors[key] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    console.log("Form data:", formData);

    if (!validate()) {
      console.log("Validation failed", errors);
      setIsSubmitting(false);
      console.groupEnd();
      return;
    }

    try {
      const name = `${formData.firstName} ${formData.lastName}`;

      const response = await ClientSignUp(
        name,
        formData.email,
        formData.password
      );

      console.log("✅ Signup API response:", response);

      // Store email for OTP verification
      localStorage.setItem("emailForOTPVerification", formData.email);

      // Instead of navigating, show the OTP modal
      setVerificationEmail(formData.email);
      setShowOTPModal(true);
    } catch (error: any) {
      console.error("❌ Signup error details:", {
        message: error.message,
        stack: error.stack,
      });

      setErrors({
        ...errors,
        apiError: error.message,
      });

      // Check if navigation is available
      console.log("Navigate function exists:", !!navigate);
    } finally {
      setIsSubmitting(false);
      console.groupEnd();
    }
  };

  const handleOTPSuccess = () => {
    console.log("handleOTPSuccess called, navigating to questionaire");

    // Navigate to the correct path with debugging
    console.log("About to navigate to:", "/auth/profile/authprofile");
    navigate("/auth/profile/authprofile", {
      state: {
        email: verificationEmail,
        fromSignup: true,
      },
      replace: true,
    });
    console.log("Navigation called");
  };

  return (
    <div className={styles.container}>
      <div className={styles.formContainer}>
        <h2 className={styles.heading}>Sign Up</h2>

        {errors.apiError && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
            {errors.apiError}
          </div>
        )}

        <form onSubmit={handleSubmit} className={styles.formSpacing}>
          {/* First and Last Name */}
          <div className={styles.inputGroup}>
            <div className={styles.inputContainer}>
              <label className={styles.label}>First Name*</label>
              <div className="border-gradient rounded-lg">
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`${styles.input} ${
                    errors.firstName ? "border-red-500" : ""
                  }`}
                  placeholder="First Name"
                />
              </div>
              {errors.firstName && (
                <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
              )}
            </div>
            <div className={styles.inputContainer}>
              <label className={styles.label}>Last Name*</label>
              <div className="border-gradient rounded-lg">
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`${styles.input} ${
                    errors.lastName ? "border-red-500" : ""
                  }`}
                  placeholder="Last Name"
                />
              </div>
              {errors.lastName && (
                <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
              )}
            </div>
          </div>

          {/* Email */}
          <div>
            <label className={styles.label}>Email*</label>
            <div className="border-gradient rounded-lg">
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                onBlur={handleBlur}
                className={`${styles.input} ${
                  errors.email ? "border-red-500" : ""
                }`}
                placeholder="Enter your business email"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          {/* Password & Confirm Password */}
          <div className={styles.inputGroup}>
            <div className={styles.inputContainer}>
              <label className={styles.label}>Password*</label>
              <div className="relative">
                <div className="border-gradient rounded-lg">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`${
                      styles.input
                    } pr-10 hide-edge-password-toggle ${
                      errors.password ? "border-red-500" : ""
                    }`}
                    placeholder="Enter your password"
                    autoComplete="new-password"
                  />
                </div>
                <div
                  className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <Eye size={isLaptopLg ? 22 : 18} />
                  ) : (
                    <EyeOff size={isLaptopLg ? 22 : 18} />
                  )}
                </div>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            <div className={styles.inputContainer}>
              <label className={styles.label}>Confirm Password*</label>
              <div className="relative">
                <div className="border-gradient rounded-lg">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className={`${
                      styles.input
                    } pr-10 hide-edge-password-toggle ${
                      errors.confirmPassword ? "border-red-500" : ""
                    }`}
                    placeholder="Confirm your password"
                    autoComplete="new-password"
                  />
                </div>
                <div
                  className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <Eye size={isLaptopLg ? 22 : 18} />
                  ) : (
                    <EyeOff size={isLaptopLg ? 22 : 18} />
                  )}
                </div>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.confirmPassword}
                </p>
              )}
            </div>
          </div>

          {/* Checkboxes */}
          <div className={styles.checkboxContainer}>
            <div className={styles.checkboxGroup}>
              <input
                type="checkbox"
                name="agreePolicy"
                checked={formData.agreePolicy}
                onChange={handleChange}
                className={`${styles.checkbox} ${
                  errors.agreePolicy ? "border-red-500" : ""
                }`}
              />
              <span className={styles.checkboxText}>
                I agree with Macgence{" "}
                <a href="#" className="text-blue-500 underline">
                  Privacy Policy
                </a>{" "}
                and{" "}
                <a href="#" className="text-blue-500 underline">
                  Terms of Service
                </a>
              </span>
            </div>
            {errors.agreePolicy && (
              <p className="text-red-500 text-sm ml-6 -mt-2">
                {errors.agreePolicy}
              </p>
            )}

            <div className={styles.checkboxGroup}>
              <input
                type="checkbox"
                name="receiveMarketing"
                checked={formData.receiveMarketing}
                onChange={handleChange}
                className={`${styles.checkbox} ${
                  errors.receiveMarketing ? "border-red-500" : ""
                }`}
              />
              <span className={styles.checkboxText}>
                I agree to receive marketing communication from Macgence.
              </span>
            </div>
            {/* {errors.receiveMarketing && (
              <p className="text-red-500 text-sm ml-6 -mt-2">
                {errors.receiveMarketing}
              </p>
            )} */}
          </div>

          {/* ReCAPTCHA */}
          <ReCAPTCHAComponent />

          {/* Submit Button */}
          <div className="flex flex-row items-center">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`${styles.button} ${
                isSubmitting ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isSubmitting ? (
                "Processing..."
              ) : (
                <>
                  Sign Up <MoveRight className={styles.buttonIcon} />
                </>
              )}
            </button>
          </div>
        </form>

        {/* Login Link */}
        <p className={styles.loginText}>
          Already have an account?{" "}
          <Link
            to="/auth/login"
            className="text-red-500 font-semibold hover:text-blue-600"
          >
            Log in
          </Link>
        </p>
      </div>

      {/* OTP Modal */}
      {showOTPModal && (
        <>
          {console.log(
            "Rendering OTPModal with onSuccess:",
            typeof handleOTPSuccess
          )}
          <OTPModal
            email={verificationEmail}
            isOpen={showOTPModal}
            onClose={() => {
              console.log("Modal close triggered");
              setShowOTPModal(false);
              setIsSubmitting(false);
            }}
            onSuccess={handleOTPSuccess}
          />
        </>
      )}
    </div>
  );
};

export default SignupForm;
