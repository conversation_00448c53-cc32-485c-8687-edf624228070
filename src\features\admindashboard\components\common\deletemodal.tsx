// components/ConfirmDeleteModal.tsx
// @ts-expect-error
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface ConfirmDeleteModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title?: string;
}

export default function ConfirmDeleteModal({
    isOpen,
    onClose,
    onConfirm,
    title = "Do you want to delete this FAQ?",
}: ConfirmDeleteModalProps) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
            <div className="bg-white p-6 rounded-xl shadow-lg w-[90%] max-w-md flex flex-col items-center justify-center">
                <p className="text-2xl font-semibold mb-4 p-6">{title}</p>
                <div className="flex justify-end gap-4">
                    <Button  onClick={onClose}  className="px-10 py-6 text-lg border-gradient bg-white text-gray-600 hover:bg-[#faf5f5]">
                        Cancel
                    </Button>
                    <Button onClick={onConfirm} variant="gradient" className="px-10 py-6 text-lg">
                        Delete
                    </Button>
                </div>
            </div>
        </div>
    );
}
