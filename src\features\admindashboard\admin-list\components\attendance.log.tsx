import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { IoMdAddCircleOutline } from "react-icons/io";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import CreateAdminModal from "../modal/createadminmodal";
import { useAdminList } from "../adminlist_api/useAdminQuery";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useState } from "react";

const AdminsAllList = () => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const { data, isLoading, refetch } = useAdminList();
  const columns = useAdminColumns(refetch);

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-row justify-between gap-3 items-center mx-2">
        <div className="flex flex-wrap justify-center items-center gap-x-4">
          <FaArrowLeft
            className="text-2xl text-[#FF577F] cursor-pointer"
            onClick={() => navigate(-1)}
          />
          <h1 className="text-[#282828] text-[24px]">Admins List</h1>
        </div>
        <div>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button
                variant={"gradient"}
                className="px-8 flex items-center gap-1 justify-center"
                onClick={() => setOpen(true)}
              >
                <span className="font-poppins">Add New Admin</span>
                <IoMdAddCircleOutline className="!w-6 !h-6" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <CreateAdminModal refetchList={refetch} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <DataTable
        title="Admins"
        columns={columns}
        data={data?.data || []}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default AdminsAllList;
