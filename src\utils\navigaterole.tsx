// utils/roleRedirect.ts
import { NavigateFunction } from "react-router-dom";

export const redirectToDashboard = (
  role: string,
  navigate: NavigateFunction
) => {
  console.log("Redirecting to dashboard for role:", role);
  switch (role) {
    case "ADMIN":
      navigate("/admin/dashboard");
      break;
    case "CLIENT":
      navigate("/dashboard");
      break;
    case "ANNOTATOR":
      navigate("/annotator/list");
      break;
    case "COWORKER":
      navigate("/dashboard");
      break;
    case "PROJECT_COORDINATOR":
      navigate("/coordinator/dashboardlist");
      break;
    default:
      navigate("/login");
  }
};
