import { adminClientSuspendApi } from "../adminclientsuspenddelete_api/adminclientssuspenddelete_api";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";

interface SuspendeAdminClientProps {
  client: {
    id: string;
    name: string;
  };
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void; // Add this prop to handle successful suspension
}

type FormValues = {
  duration: "24h" | "7d" | "30d" | "always";
};

export default function SuspendeAdminClient({ 
  client, 
  open, 
  onOpenChange, 
  onSuccess 
}: SuspendeAdminClientProps) {
  const { handleSubmit, watch, setValue } = useForm<FormValues>({
    defaultValues: {
      duration: "24h",
    },
  });
  const [isLoading, setIsLoading] = useState(false);

  const onSubmit = async (data: FormValues) => {
    try {
      setIsLoading(true);
      await adminClientSuspendApi(client.id, data.duration);
      if (onOpenChange) onOpenChange(false);
      if (onSuccess) onSuccess(); // Call the success callback
    } catch (error) {
      console.error("Error suspending client:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const selectedValue = watch("duration");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col items-center justify-center gap-4">
          <DialogHeader>
            <DialogTitle>Do you want to suspend the account ?</DialogTitle>
          </DialogHeader>

          <div className="flex justify-center my-4">
            <RadioGroup
              value={selectedValue}
              onValueChange={(value) =>
                setValue("duration", value as FormValues["duration"])
              }
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="24h" id="24h" />
                <Label htmlFor="24h">24 hours</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="7d" id="7d" />
                <Label htmlFor="7d">7 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="30d" id="30d" />
                <Label htmlFor="30d">30 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="always" id="always" />
                <Label htmlFor="always">Always</Label>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => onOpenChange?.(false)} 
              className="border-gradient px-10"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              variant="gradient" 
              type="submit" 
              className="px-10"
              disabled={isLoading}
            >
              {isLoading ? "Processing..." : "Suspend"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}