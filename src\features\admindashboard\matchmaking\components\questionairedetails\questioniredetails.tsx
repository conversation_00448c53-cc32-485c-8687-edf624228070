import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { ClientData, QuestionnaireData, PackageData } from "@/types/matchmaking.types";
import { QuestionireShow_api, GetAllPackages_api } from "./questionire_api/questionire_api"; // Adjust the import path

interface QuestionireDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  client: ClientData;
  subscriptionId: string; // Pass subscriptionId instead of pre-fetched questionnaireData
}

const QuestionireDetails: React.FC<QuestionireDetailsProps> = ({
  isOpen,
  onClose,
  client,
  subscriptionId,
}) => {
  const [questionnaireData, setQuestionnaireData] = useState<QuestionnaireData | null>(null);
  const [packageName, setPackageName] = useState<string>("-");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!isOpen || !subscriptionId) return;
      setLoading(true);
      setError(null);

      try {
        // Fetch questionnaire details
        const response = await QuestionireShow_api(subscriptionId);
        const data: QuestionnaireData = response.getDetails;
        setQuestionnaireData(data);

        // Fetch all packages to map packageId to package name
        const packagesResponse = await GetAllPackages_api();
        const packages: PackageData[] = packagesResponse.data.packages;
        const matchedPackage = packages.find((pkg) => pkg.id === data.packageId);
        setPackageName(matchedPackage ? matchedPackage.name : "-");
      } catch (err) {
        setError("Failed to load questionnaire details. Please try again.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [isOpen, subscriptionId]);

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[805px]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-medium">
              {client.name}'s Questionnaire Details
            </DialogTitle>
          </DialogHeader>
          <div className="py-4 text-center">Loading...</div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error || !questionnaireData) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[805px]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-medium">
              {client.name}'s Questionnaire Details
            </DialogTitle>
          </DialogHeader>
          <div className="py-4 text-center text-red-500">
            {error || "No data available"}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[805px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-medium">
            {client.name}'s Questionnaire Details
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-8 py-4">
          <div className="flex flex-row gap-x-3 w-full justify-center items-center">
            <div className="grid grid-cols-2 w-1/2 p-2 items-center gap-2 border-gradient rounded-lg bg-[#F9EFEF]">
              <span className="font-medium">Package Category:</span>
              <span>{packageName}</span>
            </div>
            <div className="grid grid-cols-2 w-1/2 items-center p-2 rounded-lg gap-2 border-gradient bg-[#F9EFEF]">
              <span className="font-medium">Timezone:</span>
              <span>{questionnaireData.timezone}</span>
            </div>
          </div>
          <div className="flex flex-row gap-x-3 w-full justify-center items-center">
            <div className="grid grid-cols-2 items-center w-1/2 p-2 rounded-lg gap-2 border-gradient bg-[#F9EFEF]">
              <span className="font-medium">Industry:</span>
              <span>{questionnaireData.industry}</span>
            </div>
            <div className="grid grid-cols-2 items-center gap-2 w-1/2 p-2 rounded-lg border-gradient bg-[#F9EFEF]">
              <span className="font-medium">Annotator Category:</span>
              <span>{questionnaireData.category}</span>
            </div>
          </div>
          <div className="flex flex-row gap-x-3 w-full justify-center items-center">
            <div className="grid grid-cols-2 items-center gap-2 w-1/2 p-2 rounded-lg border-gradient bg-[#F9EFEF]">
              <span className="font-medium">Shift Timing:</span>
              <span>{`${questionnaireData.availableFrom} - ${questionnaireData.availableTo}`}</span>
            </div>
            <div className="grid grid-cols-2 items-center gap-2 w-1/2 p-2 rounded-lg border-gradient bg-[#F9EFEF]">
              <span className="font-medium">Start On:</span>
              <span>{new Date(questionnaireData.startOn).toLocaleDateString()}</span>
            </div>
          </div>
          <div className="grid grid-cols-1 flex-row items-center w-full justify-center gap-2 border-gradient bg-[#F9EFEF] p-2 rounded-lg">
            <span className="font-medium w-[20px]">Description:</span>
            <span>{questionnaireData.description || "-"}</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QuestionireDetails;