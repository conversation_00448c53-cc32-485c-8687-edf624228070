// import React, { useState } from "react";
// import avatar from "@/assets/client1.png";
// // import { useNavigate } from "react-router-dom";
// import { X, ExternalLink } from "lucide-react";

// type AnnotatorStatus = "View Project";

// type Annotator = {
//   name: string;
//   tasksAssigned: number;
//   status: AnnotatorStatus;
// };

// const annotators: Annotator[] = [
//   { name: "En<PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "Enamuel sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
//   { name: "<PERSON><PERSON><PERSON> sah", tasksAssigned: 5, status: "View Project" },
// ];

// // const statusColors: Record<AnnotatorStatus, string> = {
// //   "View Project":
// //     "bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white",
// // };

// const AdminClientDetails: React.FC = () => {
//   const [showPopup, setShowPopup] = useState(false);

//   const togglePopup = () => {
//     setShowPopup((prev) => !prev);
//   };
//   return (
//     <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg p-4 w-[261px] h-[246px] flex flex-col relative">
//       <div className="flex justify-between items-center mb-3">
//         <h2 className="text-lg font-semibold font-poppins">Clients</h2>
//         {/* <button
//           onClick={() => navigate("/dashboard/task-details/projects")}
//           className="text-xs font-semibold px-3 py-1 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all"
//         >
//           View All
//         </button> */}
//       </div>
//       <ul className="CustomScroll  overflow-y-auto pr-1">
//         {annotators.map((annotator, index) => (
//           <li key={index} className=" flex items-center gap-3 mb-3 last:mb-0">
//             <img
//               src={avatar}
//               alt="avatar"
//               className="w-6 h-6 rounded-full object-cover"
//             />{" "}
//             <div className="flex-1">
//               <div className="flex gap-2">
//                 <p className="font-medium text-xs text-[#282828]">
//                   {annotator.name}
//                 </p>
//                 <button
//                   onClick={togglePopup}
//                   className="flex items-center justify-center w-7 h-4 rounded-full bg-gray-200 hover:bg-gray-300"
//                 >
//                   <div className="flex space-x-1">
//                     <span className="w-1 h-1 bg-black rounded-full"></span>
//                     <span className="w-1 h-1 bg-black rounded-full"></span>
//                     <span className="w-1 h-1 bg-black rounded-full"></span>
//                   </div>
//                 </button>
//               </div>
//               <p className="text-[10px] text-[#727272]">
//                 {annotator.tasksAssigned} task assigned
//               </p>
//             </div>
//             {/* <button
//               onClick={() => navigate("/dashboard/project-details")}
//               className={`text-white text-xs px-2 py-1 rounded-md ${
//                 statusColors[annotator.status]
//               }`}
//             >
//               {annotator.status}
//             </button> */}
//           </li>
//         ))}
//       </ul>

//       {/* Popup */}
//       {showPopup && (
//         <div className="w-[261px] rounded-xl border border-pink-300 p-4 shadow-lg bg-white absolute top-10 left-0 z-10">
//           {/* Top Icons */}
//           <div className="absolute top-2 right-2 flex space-x-2">
//             <ExternalLink className="w-4 h-4 cursor-pointer" />
//             <X
//               className="w-4 h-4 cursor-pointer"
//               onClick={() => setShowPopup(false)}
//             />
//           </div>

//           {/* Avatar and Name */}
//           <div className="flex gap-2 items-center mt-4">
//             <img
//               src={avatar}
//               alt="avatar"
//               className="w-16 h-16 rounded-full object-cover"
//             />
//             <div className="flex flex-col">
//               <h2 className="font-medium text-base mt-2">Kevin Kooper</h2>
//               <p className="text-sm text-gray-500"><EMAIL></p>
//             </div>
//           </div>

//           {/* Buttons */}
//           <div className="flex justify-center gap-2 mt-4">
//             <button className="w-full border border-pink-500 text-pink-500 rounded-lg px-4 py-2 text-xs font-semibold hover:bg-pink-50">
//               Group Chat
//             </button>
//             <button className="w-full bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white rounded-lg px-4 py-2 text-sm font-medium hover:opacity-90">
//               Chat
//             </button>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default AdminClientDetails;

import React, { useState, useEffect } from "react";
import { X, ExternalLink } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { getProjectDetails } from "./admindetails_api/admindetails_api";

// Define types based on the exact Postman response structure
type ClientStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING" | string;

// Match the exact structure from the Postman response
type Client = {
  id: string;
  name: string;
  lastname: null | string;
  email: string;
  passwordHash: string;
  emailVerified: string;
  role: string;
  domain: string | null;
  availableFrom: string | null;
  availableTo: string | null;
  timezone: string | null;
  industry: string | null;
  category: string | null;
  isDeleted: boolean;
  invalidLoginAttempts: number;
  accountStatus: ClientStatus;
  suspendedUntil: string | null;
  clientOwnerId: string | null;
  annotatorStatus: string | null;
  coworkerPermission: string | null;
  otpCode: string | null;
  otpExpiry: string | null;
  createdAt: string;
  updatedAt: string;
  packageId: string | null;
  createdById: string | null;
};

// Map status values to colors
const statusColors: Record<string, string> = {
  ACTIVE: "bg-green-500",
  INACTIVE: "bg-red-500",
  SUSPENDED: "bg-red-500",
  PENDING: "bg-blue-500",
};

// Generate avatar URL using dicebear with first 2 letters of name
const getAvatarUrl = (name: string): string => {
  if (!name) return "";

  // Get the first name (first word)
  const firstName = name.trim().split(" ")[0];

  // Take the first two letters of the first name
  const initials =
    firstName.length >= 2
      ? firstName.substring(0, 2).toUpperCase()
      : firstName.charAt(0).toUpperCase();

  return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(
    initials
  )}`;
};

const AdminClientDetails: React.FC = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Fetch real data from the backend API
  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setLoading(true);

        // Get project ID from URL query parameters
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get("id");

        if (!projectId) {
          console.error("No project ID found in URL");
          setError("Project ID is missing");
          setLoading(false);
          return;
        }

        const response = await getProjectDetails(projectId);
        console.log("Project details response:", response);

        if (response && response.data && response.data.createdBy) {
          // Use the client data (createdBy) from the API response
          setClient(response.data.createdBy);
        } else {
          setClient(null);
        }
      } catch (err) {
        console.error("Error fetching project details:", err);
        setError("Failed to load client data");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [location.search]);

  const togglePopup = () => {
    setShowPopup((prev) => !prev);
  };

  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 w-full h-full lg-only:min-h-[120px] xl-only:min-h-[140px] 2xl-only:min-h-[160px] flex flex-col relative">
      <div className="flex justify-between items-center mb-2 lg:mb-3 xl:mb-4 2xl:mb-5">
        <h2 className="lg-only:text-[15px] xl-only:text-[15px] 2xl-only:text-lg font-semibold font-poppins">
          Clients
        </h2>
      </div>

      <ul className="CustomScroll overflow-y-auto pr-1">
        {loading ? (
          <li className="text-center lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">
            Loading...
          </li>
        ) : error ? (
          <li className="text-center lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-red-500">
            {error}
          </li>
        ) : client ? (
          <li className="flex items-center lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-3 lg-only:mb-1.5 xl-only:mb-2 2xl-only:mb-3 last:mb-0">
            <img
              src={getAvatarUrl(client.name)}
              alt="avatar"
              className="w-5 h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 2xl:w-10 2xl:h-10 rounded-full object-cover"
            />
            <div className="flex-1">
              <div className="flex lg-only:gap-1 xl-only:gap-1.5 2xl-only:gap-2 items-center">
                <p className="font-medium text-[12px] lg:text-[12px] xl:text-[13px] 2xl:text-lg text-[#282828]">
                  {client.name.split(" ").slice(0, 1).join(" ")}
                </p>
                <button
                  onClick={togglePopup}
                  className="flex items-center justify-center lg-only:w-5 lg-only:h-3 xl-only:w-6 px-5 xl-only:h-3.5 2xl-only:w-7 2xl-only:h-4 rounded-full bg-gray-200 hover:bg-gray-300"
                >
                  <div className="flex space-x-1">
                    <span className="w-1 h-1 lg:w-1 lg:h-1 xl:w-1 xl:h-1 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                    <span className="w-1 h-1 lg:w-1 lg:h-1 xl:w-1 xl:h-1 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                    <span className="w-1 h-1 lg:w-1 lg:h-1 xl:w-1 xl:h-1 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                  </div>
                </button>
              </div>
            </div>
            <span
              className={`text-white text-[10px] lg:text-xs xl:text-[12px] 2xl:text-base px-2 py-0.5 rounded-full capitalize ${
                statusColors[client.accountStatus] || "bg-gray-500"
              }`}
            >
              {client.accountStatus?.toLowerCase() || "unknown"}
            </span>
          </li>
        ) : (
          <li className="text-center lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">
            No client assigned
          </li>
        )}
      </ul>

      {/* Popup */}
      {showPopup && client && (
        <div className="lg-only:w-[280px] xl-only:w-[320px] 2xl-only:w-[360px] rounded-xl border border-pink-300 lg-only:p-2.5 xl-only:p-4 2xl-only:p-5 shadow-lg bg-white absolute lg-only:top-0 xl-only:top-0 2xl-only:top-0 lg-only:-translate-y-[calc(100%-40px)] xl-only:-translate-y-[calc(100%-50px)] 2xl-only:-translate-y-[calc(100%-20px)] lg-only:left-0 xl-only:-right-0 2xl-only:left-0 z-50">
          {/* Top Icons */}
          <div className="absolute lg-only:top-1.5 lg-only:right-1.5 xl-only:top-2 xl-only:right-2 2xl-only:top-2 2xl-only:right-2 flex space-x-2">
            {/* /coordinator/projectdetails/clients */}
            <Link to="/admin/admindetails/clients">
              <ExternalLink className="lg-only:w-3.5 lg-only:h-3.5 xl-only:w-5 xl-only:h-5 2xl-only:w-6 2xl-only:h-6 cursor-pointer" />
            </Link>

            <X
              className="lg-only:w-3.5 lg-only:h-3.5 xl-only:w-4 xl-only:h-4 2xl-only:w-5 2xl-only:h-5 cursor-pointer"
              onClick={() => setShowPopup(false)}
            />
          </div>

          {/* Avatar and Name */}
          <div className="flex lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-3 items-center lg-only:mt-3 xl-only:mt-4 2xl-only:mt-5">
            <img
              src={getAvatarUrl(client.name)}
              alt="avatar"
              className="lg-only:w-10 lg-only:h-10 xl-only:w-14 xl-only:h-14 2xl-only:w-16 2xl-only:h-16 rounded-full object-cover"
            />
            <div className="flex flex-col">
              {/* name client */}
              <h2 className="font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mt-0.5 xl-only:mt-1 2xl-only:mt-2">
                {client.name}
              </h2>
              {/* email client */}
              <p className="lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-gray-500">
                {client.email}
              </p>
            </div>
          </div>

          {/* Shift Timing */}
          <div className="flex justify-between lg-only:mt-2 xl-only:mt-3 2xl-only:mt-4 lg-only:px-1 xl-only:px-1.5 2xl-only:px-2 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm">
            <span className="text-gray-600">Shift Timing:</span>
            <span className="font-normal">
              {client.availableFrom && client.availableTo
                ? `${client.availableFrom} - ${client.availableTo}`
                : "Not specified"}
            </span>
          </div>

          {/* Industry */}
          <div className="flex justify-between lg-only:mt-1 xl-only:mt-1.5 2xl-only:mt-2 lg-only:px-1 xl-only:px-1.5 2xl-only:px-2 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm">
            <span className="text-gray-600">Industry:</span>
            <span className="font-normal">
              {client.industry || "Not specified"}
            </span>
          </div>

          {/* Buttons */}
          <div className="flex justify-center lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-3 lg-only:mt-2 xl-only:mt-3 2xl-only:mt-4">
            <button
              onClick={() => navigate("/admin/chat")}
              className="w-full border border-pink-500 text-pink-500 rounded-lg lg-only:px-1.5 lg-only:py-0.5 xl-only:px-3 xl-only:py-1.5 2xl-only:px-4 2xl-only:py-2 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-semibold hover:bg-pink-50"
            >
              Group Chat
            </button>
            <button
              onClick={() => navigate("/admin/chat")}
              className="w-full bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg lg-only:px-1.5 lg-only:py-0.5 xl-only:px-3 xl-only:py-1.5 2xl-only:px-4 2xl-only:py-2 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-medium hover:opacity-90"
            >
              Chat
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminClientDetails;
