import VideoCallDropdown from "./videocalldropdown";
import { useRef, useState } from "react";

interface VideoCallIntegrationProps {
    userId?: string;
    selectedUser?: {
        id?: string;
        userId?: string;
        isGroup?: boolean;
    };
    conversationId?: string;
    socket?: any;
    setMessages: React.Dispatch<React.SetStateAction<any[]>>;
    scrollRef: React.RefObject<HTMLDivElement>;
    setError: React.Dispatch<React.SetStateAction<string | null>>;
}

const VideoCallIntegration: React.FC<VideoCallIntegrationProps> = ({
    userId,
    selectedUser,
    conversationId,
    socket,
    setMessages,
    scrollRef,
    setError,
}) => {
    const [videoDropdownOpen, setVideoDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    type PlatformType = 'Google Meet' | 'Google Meet Scheduled' | 'Zoom' | 'Zoom Scheduled';
    const sendMessage = (message: string, platform: PlatformType) => {
        if (!userId || !selectedUser?.id) {
            setError("User or recipient not selected.");
            return;
        }

        const tempId = `temp-${Date.now()}`;
        const tempMessage = {
            id: tempId,
            text: `${platform} Link: ${message}`,
            time: new Date().toISOString(),
            self: true,
            senderId: userId,
            receiverId: selectedUser.isGroup ? undefined : selectedUser.userId || selectedUser.id,
        };

        // Add temporary message to state for immediate display
        setMessages((prev) => [...prev, tempMessage]);

        // Emit the message via socket
        if (selectedUser.isGroup) {
            const groupId = selectedUser.id || "";
            socket?.emit("send_group_message", {
                text: `${platform} Link: ${message}`,
                groupId,
                senderId: userId,
                fileUrl: null,
                fileType: null,
                replyToId: null,
            });
        } else {
            if (!conversationId) {
                setError("No conversation found. Please try again.");
                return;
            }
            socket?.emit("send_dm", {
                text: `${platform} Link: ${message}`,
                conversationId,
                senderId: userId,
                receiverId: selectedUser.userId || selectedUser.id,
                fileUrl: null,
                fileType: null,
                replyToId: null,
            });
        }

        // Scroll to the bottom
        setTimeout(() => scrollRef.current?.scrollIntoView({ behavior: "smooth" }), 100);
    };



 const handleGoogleMeet = async (type: 'create' | 'instant' | 'schedule') => {
    try {
        switch (type) {
            case 'instant':
                // Keep instant meeting exactly as it was
                const meetLink = "https://meet.google.com/new";
                sendMessage(`Instant Meeting: ${meetLink}`, 'Google Meet');
                window.open(meetLink, '_blank');
                break;

            case 'schedule':
                const startTime = new Date();
                const endTime = new Date(startTime.getTime() + 60 * 60 * 1000);

                // Format the time for display
                // const formattedStartTime = startTime.toLocaleString('en-US', {
                //     month: 'short',
                //     day: 'numeric',
                //     hour: '2-digit',
                //     minute: '2-digit',
                //     hour12: true
                // });
                // const formattedEndTime = endTime.toLocaleString('en-US', {
                //     hour: '2-digit',
                //     minute: '2-digit',
                //     hour12: true
                // });

                // Create ONLY the calendar link (no meeting link in chat)
                const calendarLink = `https://calendar.google.com/calendar/r/eventedit?text=Meeting&dates=${
                    startTime.toISOString().replace(/[-:]/g, '').split('.')[0]
                }/${
                    endTime.toISOString().replace(/[-:]/g, '').split('.')[0]
                }&details=Meeting&location=https://meet.google.com/new`;

                // Send ONLY the calendar information (no meeting link)
//                 const messageText = `Meeting Scheduled:
// 📅 When: ${formattedStartTime} - ${formattedEndTime}
// 📆 Calendar Link: ${calendarLink}`;

                // sendMessage(messageText, 'Google Meet');
                window.open(calendarLink, '_blank');
                break;
        }
    } catch (error) {
        console.error('Google Meet error:', error);
        setError("Failed to create Google Meet. Please try again.");
    }
};

const handleZoomMeeting = async (type: 'create' | 'instant' | 'schedule') => {
    try {
        switch (type) {
            case 'instant':
                // Keep instant meeting exactly as it was
                const zoomLink = "https://zoom.us/start/videomeeting";
                sendMessage(`Instant Zoom Meeting: ${zoomLink}`, 'Zoom');
                window.open(zoomLink, '_blank');
                break;

            case 'schedule':
                const startTime = new Date();
                const endTime = new Date(startTime.getTime() + 60 * 60 * 1000);
                console.log("zoom meeting endtime console", endTime)
                // const formattedStartTime = startTime.toLocaleString('en-US', {
                //     month: 'short',
                //     day: 'numeric',
                //     hour: '2-digit',
                //     minute: '2-digit',
                //     hour12: true
                // });
                // const formattedEndTime = endTime.toLocaleString('en-US', {
                //     hour: '2-digit',
                //     minute: '2-digit',
                //     hour12: true
                // });

                // Create ONLY the schedule link (no meeting link in chat)
                const zoomScheduleLink = 'https://zoom.us/meeting/schedule';

                // Send ONLY the scheduling information (no meeting link)
//                 const messageText = `Zoom Meeting Scheduled:
// 📅 When: ${formattedStartTime} - ${formattedEndTime}
// 📆 Schedule Link: ${zoomScheduleLink}`;

                // sendMessage(messageText, 'Zoom');
                window.open(zoomScheduleLink, '_blank');
                break;
        }
    } catch (error) {
        console.error('Zoom error:', error);
        setError("Failed to create Zoom meeting. Please try again.");
    }
};

    return (
        <div className="relative inline-block">
            <VideoCallDropdown
                dropdownRef={dropdownRef}
                videoDropdownOpen={videoDropdownOpen}
                setVideoDropdownOpen={setVideoDropdownOpen}
                handleGoogleMeet={handleGoogleMeet}
                handleZoomMeeting={handleZoomMeeting}
            />
        </div>
    );
};

export default VideoCallIntegration;