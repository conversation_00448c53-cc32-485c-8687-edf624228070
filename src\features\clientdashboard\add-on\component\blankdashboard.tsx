// import { IoMdCheckmarkCircleOutline } from "react-icons/io";
// import fourcirclegif from "@/assets/icons/circlesfour.gif";
// import { Button } from "@/components/ui/button";
// import { useNavigate } from "react-router-dom";
// import { useEffect } from "react";
// import { customAxios } from "@/utils/axio-interceptor";
// import { useDispatch } from "react-redux";
// import { logout } from "@/store/slices/authSlice";

// const BlankDashboard = () => {
//   const navigate = useNavigate();
//   const dispatch = useDispatch();

//   // Check annotator count when component mounts
//   useEffect(() => {
//     const checkAnnotatorCount = async () => {
//       try {
//         const response = await customAxios.get(
//           "/v1/dashboard/client-annotators"
//         );
//         if (response.data?.count > 0) {
//           navigate("/dashboard");
//         }
//       } catch (error) {
//         console.error("Error checking annotator count:", error);
//       }
//     };

//     checkAnnotatorCount();
//   }, [navigate]);

//   // Handle back button
//   useEffect(() => {
//     const handleBackButton = (event: PopStateEvent) => {
//       event.preventDefault();
//       dispatch(logout());
//       navigate("/auth/login");
//     };

//     window.addEventListener("popstate", handleBackButton);

//     return () => {
//       window.removeEventListener("popstate", handleBackButton);
//     };
//   }, [dispatch, navigate]);

//   // const handleContinue = () => {
//   //     // Check annotator count before allowing to continue
//   //     customAxios.get('/v1/dashboard/client-annotators')
//   //         .then(response => {
//   //             if (response.data?.count > 0) {
//   //                 navigate('/dashboard');
//   //             } else {
//   //                 // Stay on the same page if no annotators
//   //                 alert('Please add annotators before accessing the dashboard');
//   //             }
//   //         })
//   //         .catch(error => {
//   //             console.error('Error checking annotator count:', error);
//   //         });
//   // };

//   // const handleContactUs = () => {
//   //     navigate('/contact-us');
//   // };

//   return (
//     <div className="flex flex-col justify-center items-center">
//       {/* <div className="w-full border p-4">
//         <img src={imglogo} alt="imglogo" className="w-[10rem] h-[2rem]" />
//       </div> */}

//       <div className="flex flex-col gap-4 items-center justify-center">
//         <img
//           src={fourcirclegif}
//           alt="fourcirclegif"
//           className="w-[15rem] h-[15rem]"
//         />

//         <div className="flex flex-row justify-center items-center gap-[0.5rem] text-center">
//           <IoMdCheckmarkCircleOutline className="w-[35px] h-[35px] text-[#20BF55]" />
//           <h1 className="text-[40px] font-bold">Please check your Mail</h1>
//         </div>

//         <div className="flex flex-row justify-center gap-4">
//           <Button
//             variant={"ghost"}
//             className="border px-[18px] text-[18px] border-[#E91C24] text-[#E91C24]"
//             // onClick={handleContinue}
//           >
//             Continue to Website
//           </Button>
//           <Button
//             variant={"gradient"}
//             className="px-[40px] text-[18px]"
//             // onClick={handleContactUs}
//           >
//             Contact Us
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default BlankDashboard;

// import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import fourcirclegif from "@/assets/icons/circlesfour.gif";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation, Link} from "react-router-dom";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
// import { useDispatch } from "react-redux";
// import { logout } from "@/store/slices/authSlice";
import { Loader2 } from "lucide-react";
import { useAppSelector } from "@/store/hooks/reduxHooks";

const BlankDashboard = () => {
  const navigate = useNavigate();
//   const dispatch = useDispatch();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const userRole = useAppSelector((state) => state.auth.user?.role);
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);

  // Prevent direct URL access
  useEffect(() => {
    if (!location.pathname.endsWith("blank-page-after-payment-successful")) {
      navigate("/dashboard/blank-page-after-payment-successful", {
        replace: true,
      });
    }
  }, [location.pathname, navigate]);

  // Handle back button and URL changes
  useEffect(() => {
    const handleUrlChange = () => {
      if (
        !window.location.pathname.endsWith(
          "blank-page-after-payment-successful"
        )
      ) {
        window.history.pushState(
          null,
          "",
          "/dashboard/blank-page-after-payment-successful"
        );
      }
    };

    window.addEventListener("popstate", handleUrlChange);
    return () => window.removeEventListener("popstate", handleUrlChange);
  }, []);

  // Check annotator count
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/auth/login", { replace: true });
      return;
    }

    const checkAnnotatorCount = async () => {
      try {
        setIsLoading(true);
        if (userRole === "CLIENT") {
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          if (response.data?.count > 0) {
            navigate("/dashboard", { replace: true });
          }
        } else if (userRole === "COWORKER") {
          navigate("/dashboard", { replace: true });
        }
      } catch (error) {
        console.error("Error checking annotator count:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAnnotatorCount();
  }, [navigate, userRole, isAuthenticated]);

  // const handleContinue = useCallback(() => {
  //   navigate("/dashboard");
  // }, [navigate]);

 
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex flex-col gap-4 items-center justify-center flex-grow p-4">
        <img
          src={fourcirclegif}
          alt="Payment successful"
          className="w-[15rem] h-[15rem]"
        />

        <div className="flex flex-row justify-center items-center gap-[0.5rem] text-center">
         
       <div className="flex flex-col gap-6 mb-6 justify-center items-center w-[80%]">
           <h1 className="text-[28px] font-bold text-[#282828] leading-[100%]">An annotator will be allocated to you. </h1>
          <p className="text-[#757575] text-[20px] leading-[100%]">Please wait for at least few hours for us to allocate an annotator for you. </p>
       </div>

        </div>

        <div className="flex flex-row justify-center gap-4">
         
         
          <Link to='/dashboard/support'>
          <Button
            variant={"gradient"}
            className="px-[40px] py-7 text-[18px]"
            
          >
            Contact Support
          </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BlankDashboard;
