import TopSectionDashboard from "./components/topsectiondashboard";
import ClientList from "./components/common/clientlist";
import Cordinator_AnnotatorList from "./components/common/coordinator-annotator";
import Coordinator_Project from "./components/common/coordinator-project";
import CoordinatorK<PERSON>banBoard from "./coordinatorkanban";

const ProjectCordinator = () => {
  return (
    <div className="w-full flex flex-col gap-4 lg:px-6 xl:px-8 2xl:px-10 bg-white min-h-screen">
      {/* Top Summary Cards */}
      <div className="w-full">
        <TopSectionDashboard />
      </div>

      {/* Bottom section - Responsive layout using Tailwind */}
      <div className="mt-5">
        {/* For 1024px-1439px: Keep the existing layout (full width Kanban, lists at bottom) */}
        <div className="lg-only:block xl:hidden">
          <div className="flex flex-col gap-6 justify-center items-center">
            {/* Kanban Board - Full Width */}
            <div className="w-full">
              <CoordinatorKanbanBoard />
            </div>

            {/* Sidebar Lists - At Bottom */}
            <div className="w-full flex flex-row gap-4 mt-4">
              <div className="w-1/3">
                <Cordinator_AnnotatorList />
              </div>
              <div className="w-1/3">
                <ClientList />
              </div>
              <div className="w-1/3">
                <Coordinator_Project />
              </div>
            </div>
          </div>
        </div>

        {/* For 1440px and above: Side-by-side layout with screen-height kanban */}
        <div className="hidden xl:block">
          <div className="flex flex-row justify-between gap-12">
            {/* Kanban Board with screen height */}
            <div className="w-3/4">
              <CoordinatorKanbanBoard />
            </div>

            {/* Sidebar Lists - Fixed heights */}
            <div className="w-1/4 flex flex-col gap-6">
              <div>
                <Cordinator_AnnotatorList />
              </div>
              <div>
                <ClientList />
              </div>
              <div>
                <Coordinator_Project />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCordinator;
