// import BrandedGlobalLoader from "@/components/loaders/loaders.one";
// import { RootState } from "@/store";
// import { useAppDispatch } from "@/store/hooks/reduxHooks";
// import {
//   logout,
//   setIsAuthenticated,
//   setLoading,
//   setUser,
// } from "@/store/slices/authSlice";
// import { setUserProfile } from "@/store/slices/userSlice";
// import { customAxios } from "@/utils/axio-interceptor";
// import { useEffect } from "react";
// import { useSelector } from "react-redux";
// import { Navigate, Outlet } from "react-router-dom";

// // Helper function to verify token
// const verifyToken = async () => {
//   try {
//     const response = await customAxios.get("/v1/clients/me");
//     return response.data; // true or user data based on server response
//   } catch (err) {
//     return false;
//   }
// };

// const PrivateRoute = () => {
//   const { isAuthenticated, isLoading } = useSelector(
//     (state: RootState) => state.auth
//   );
//   const dispatch = useAppDispatch();

//   useEffect(() => {
//     const checkAuth = async () => {
//       try {
//         const result = await verifyToken();
//         if (result) {
//           dispatch(setUser(result));
//           dispatch(setIsAuthenticated(true));
//           dispatch(setUserProfile(result));
//         } else {
//           dispatch(logout());
//           dispatch(setIsAuthenticated(false));
//         }
//       } finally {
//         dispatch(setLoading(false));
//       }
//     };
//     dispatch(setLoading(true));
//     checkAuth();
//   }, [dispatch]);

//   console.log("isAuthenticated", isAuthenticated);
//   console.log("isLoading", isLoading);

//   // If loading or authentication state is still being determined, show nothing or a loader.
//   if (isLoading) {
//     return <BrandedGlobalLoader isLoading />; // Replace with a loader if needed.
//   }

//   // If not authenticated, redirect to login page
//   if (!isAuthenticated && !isLoading) {
//     return <Navigate to="/auth/login" replace />;
//   }

//   // If authenticated, render the child components (outlet).
//   return <Outlet />;
// };

// export default PrivateRoute;

import { useEffect } from "react";
import { useSelector } from "react-redux";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { RootState } from "@/store";
import { useAppDispatch } from "@/store/hooks/reduxHooks";
import {
  logout,
  setIsAuthenticated,
  setLoading,
  setUser,
} from "@/store/slices/authSlice";
import { setUserProfile } from "@/store/slices/userSlice";
import { customAxios } from "@/utils/axio-interceptor";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { USER_ROLES } from "@/utils/constants";

interface RoleProtectedRouteProps {
  allowedRoles: USER_ROLES[];
  skipAnnotatorCheck?: boolean;
}

const verifyToken = async () => {
  try {
    const response = await customAxios.get("/v1/clients/me");
    return response.data;
  } catch (err) {
    throw new Error("Token verification failed");
  }
};

const RoleProtectedRoute = ({
  allowedRoles,
  skipAnnotatorCheck = false,
}: RoleProtectedRouteProps) => {
  const { isAuthenticated, isLoading, user } = useSelector(
    (state: RootState) => state.auth
  );
  const dispatch = useAppDispatch();
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const result = await verifyToken();
        console.log(result, "mangaread")
        if (result) {
          dispatch(setUser(result));
          dispatch(setIsAuthenticated(true));
          dispatch(setUserProfile(result.user));
        } else {
          dispatch(logout());
          dispatch(setIsAuthenticated(false));
        }
      } catch (error) {
        dispatch(logout());
        dispatch(setIsAuthenticated(false));
        localStorage.removeItem("role");
        localStorage.removeItem("access_token");
      } finally {
        dispatch(setLoading(false));
      }
    };

    if (!user || !localStorage.getItem("access_token")) {
      dispatch(setLoading(true));
      checkAuth();
    }
  }, [dispatch, location.pathname]);

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Skip role check for payment success page
  if (
    skipAnnotatorCheck &&
    location.pathname.includes("blank-page-after-payment-successful")
  ) {
    return <Outlet />;
  }

  if (user?.role && !allowedRoles.includes(user.role as USER_ROLES)) {
    switch (user.role) {
      case USER_ROLES.CLIENT:
      case USER_ROLES.COWORKER:
        return <Navigate to="/dashboard" replace />;
      case USER_ROLES.ANNOTATOR:
        return <Navigate to="/annotator/list" replace />;
      case USER_ROLES.PROJECT_COORDINATOR:
        return <Navigate to="/coordinator/dashboardlist" replace />;
      case USER_ROLES.ADMIN:
        return <Navigate to="/admin/dashboard" replace />;
      default:
        return <Navigate to="/auth/login" replace />;
    }
  }

  return <Outlet />;
};

export default RoleProtectedRoute;
