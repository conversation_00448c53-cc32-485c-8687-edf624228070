@tailwind base;
@tailwind components;
@tailwind utilities;



::-webkit-scrollbar {
  width: 1px;
  height: 1px;
}

::-webkit-scrollbar-track {
  background: #ffffff;
}

::-webkit-scrollbar-thumb {
  background-color: #bdbcbc;
  border-radius: 1px;
}



/* Base theme variables */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

/* Custom Scrollbar Styling */
.CustomScroll::-webkit-scrollbar {
  width: 7px;
  height: 10px;
  background-color: rgba(224, 224, 224, 1);
}

.CustomScroll::-webkit-scrollbar-track {
  border-radius: 5px;
}

.CustomScroll::-webkit-scrollbar-thumb {
  background: rgba(63, 67, 80, 0.24);
  border-radius: 5px;
}

.CustomScroll:hover::-webkit-scrollbar-thumb {
  background: rgb(180, 180, 180);
}

/* Firefox scrollbar hide (optional, uncomment if needed) */
/*
.CustomScroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
*/


/* Responsive font sizes handled by Tailwind */
@layer utilities {
  .text-responsive {
    @apply lg:text-sm xl:text-base 2xl:text-lg;
  }
}

/* Responsive background image utility classes */
@layer utilities {
  .bg-responsive {
    @apply bg-no-repeat w-full h-full bg-center;
    @apply lg:bg-[length:100%_100%] xl:bg-[length:100%_100%] 2xl:bg-cover;
  }
}

/* Fix for background images in auth screens */
@layer utilities {
  .auth-background {
    @apply absolute inset-0 z-0 overflow-hidden;
  }

  .auth-background img {
    @apply w-full h-full object-center;
    @apply lg:object-fill xl:object-fill 2xl:object-cover;
  }
}

/* Responsive styles for signup page */
@layer utilities {
  .signup-container {
    @apply lg:px-4 xl:px-6 2xl:px-8;
  }

  .signup-form {
    @apply lg:max-w-[90%] xl:max-w-[85%] 2xl:max-w-[80%];
  }
}

/* Responsive styles for OTP page */
@layer utilities {
  .otp-input-slot {
    @apply lg:!h-14 lg:!w-14 lg:!text-xl;
    @apply xl:!h-16 xl:!w-16 xl:!text-2xl;
    @apply 2xl:!h-20 2xl:!w-20 2xl:!text-3xl;
  }

  .otp-page {
    @apply lg:px-4 xl:px-6 2xl:px-8;
  }
}

@layer utilities {
  /* Hide Edge's built-in password toggle */
  input[type="password"]::-ms-reveal,
  input[type="password"]::-ms-clear {
    display: none !important;
  }
}