import React, { useState, useEffect } from "react";
import { GetClientCordinatorShiftNotification, acceptShiftRequest } from "./notification_api/notification_api";
import { toast } from "react-toastify";

interface ShiftRequest {
  id: string;
  annotatorId: string;
  requestedById: string;
  newFrom: string;
  newTo: string;
  status: string;
  createdAt: string;
  annotator: {
    id: string;
    name: string;
    email: string;
  };
  requestedBy: {
    id: string;
    name: string;
    email: string;
  };
     approvedBy: {
    id: string;
    name: string;
    email: string;
  } | null;
}

const ShiftNotification: React.FC = () => {
  const [shiftRequests, setShiftRequests] = useState<ShiftRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<string[]>([]);

  useEffect(() => {
    const fetchShiftRequests = async () => {
      try {
        const response = await GetClientCordinatorShiftNotification();
        setShiftRequests(response.data || []);
      } catch (error) {
        console.error("Error fetching shift requests:", error);
        toast.error("Failed to fetch shift requests");
      } finally {
        setLoading(false);
      }
    };

    fetchShiftRequests();
  }, []);

  const handleApprove = async (id: string) => {
    try {
      setProcessingIds((prev) => [...prev, id]);
      const response = await acceptShiftRequest(id);

      if (response.status === 1) {
        toast.success("Shift change approved successfully");
        setShiftRequests((prev) =>
          prev.map((req) =>
            req.id === id ? { ...req, status: "APPROVED" } : req
          )
        );
      } else {
        toast.error("Failed to approve shift change");
      }
    } catch (error) {
      console.error("Error approving shift request:", error);
      toast.error("Error approving shift change");
    } finally {
      setProcessingIds((prev) => prev.filter((item) => item !== id));
    }
  };

  const handleDeny = async (id: string) => {
    setShiftRequests((prev) => prev.filter((req) => req.id !== id));
  };

 // In AdminAnnotatorCard.tsx, update the formatDate function to return hh:mm [weekday] dd/mm/yyyy
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-based
  const year = date.getFullYear();
  const weekday = date.toLocaleString("en-US", { weekday: "long" }); // Get full weekday name (e.g., Monday)
  return `${hours}:${minutes} ${weekday} ${day}/${month}/${year}`;
};

  const renderActionButtons = (request: ShiftRequest) => {
    if (request.status === "APPROVED") {
      return (
        <button
          className="px-4 py-2 text-sm bg-green-500 text-white font-semibold rounded-md cursor-default"
          disabled
        >
          Approved
        </button>
      );
    }

    return (
      <>
        <button
          onClick={() => handleDeny(request.id)}
          className="px-4 py-2 text-sm border rounded-md font-semibold text-gray-700 border-gray-300 hover:bg-gray-100"
          disabled={processingIds.includes(request.id)}
        >
          Deny
        </button>
        <button
          onClick={() => handleApprove(request.id)}
          className="px-4 py-2 text-sm bg-red-500 text-white font-semibold rounded-md hover:bg-red-600"
          disabled={processingIds.includes(request.id)}
        >
          {processingIds.includes(request.id) ? "Processing..." : "Confirm"}
        </button>
      </>
    );
  };

  return (
    <div className="space-y-4">
      {loading ? (
        <div className="text-center py-10">Loading shift requests...</div>
      ) : shiftRequests.length === 0 ? (
        <div className="text-center text-[16px] font-semibold font-poppins py-10 text-gray-500">
          No pending shift requests
        </div>
      ) : (
        shiftRequests.map((request) => (
          <div key={request.id} className="bg-white border rounded-lg shadow p-6">
            <div className="mb-2">
              <h3 className="text-lg font-semibold">
                {request.annotator.name} has requested shift change
              </h3>
              <p className="text-gray-600 text-sm">
                New shift time: {request.newFrom} to {request.newTo}
              </p>
                  {request.approvedBy && (
                <p className="text-gray-600 text-sm">
                  Approved by: {request.approvedBy.name}
                </p>
              )}
              
            </div>
            <div className="flex items-center mt-4">
              <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden mr-3">
                <img
                  src={`https://ui-avatars.com/api/?name=${request.annotator.name}&background=random`}
                  alt={request.annotator.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="font-semibold text-gray-800">
                  {request.annotator.name}
                </p>
                <p className="text-sm text-gray-500">{request.annotator.email}</p>
              </div>
              <div className="ml-auto space-x-2 flex items-center">
                <span className="text-xs text-gray-400 mr-4">
                  {formatDate(request.createdAt)}
                </span>
                {renderActionButtons(request)}
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ShiftNotification;