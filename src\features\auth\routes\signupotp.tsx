"use client";

import { z } from "zod";
import { useEffect, useState } from "react";
import { useForm, Form<PERSON>rovider, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormLabel,
  FormMessage,
  FormItem,
} from "@/components/ui/form";

import { MoveRight } from "lucide-react";
import CustomToast from "@/_components/common/customtoast";
import { AuthCommonComponent } from "./common/AuthCommon";
import { useLocation, useNavigate } from "react-router-dom";

// 🛡️ Zod schema
const FormSchema = z.object({
  otp: z
    .string()
    .min(4, { message: "Your one-time password must be 4 characters." }),
  email: z.string().email({
    message: "Invalid email address.",
  }),
});

type FormValues = z.infer<typeof FormSchema>;

export function SignupOTPForm() {
  const navigate = useNavigate();
  const location = useLocation();
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [countdown, setCountdown] = useState(30);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      otp: "",
      email: location.state?.email || "", // ✅ initialize email
    },
  });

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = form;

  const onSubmit = (data: FormValues) => {
    setToastMessage(JSON.stringify(data, null, 2));
    setShowToast(true);
    setTimeout(() => {
      navigate(`/dashboard`, {
        state: {
          email: data.email,
        },
      });
    }, 2000);
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  return (
    <div className="w-full h-full flex flex-row justify-center gap-x-20 items-center shadow-[0px_4px_48px_10px_#0000000F]">
      <FormProvider {...form}>
        <div className="w-[50%]">
          <Controller
            name="otp"
            control={control}
            render={({ field, fieldState }) => (
              <div className="space-y-6">
                <FormItem>
                  <FormLabel className="text-[28px] text-[#282828]">
                    Enter One Time Password...
                  </FormLabel>
                  <FormDescription className="text-[14px] text-[#757575]">
                    Enter the 4-digit code sent to your email.
                  </FormDescription>
                  <FormControl>
                    <InputOTP
                      maxLength={4}
                      value={field.value}
                      onChange={field.onChange}
                    >
                      <InputOTPGroup className="flex flex-row gap-2 outline-none">
                        {[0, 1, 2, 3].map((index) => (
                          <InputOTPSlot
                            key={index}
                            index={index}
                            className="border-gradient rounded-lg"
                          />
                        ))}
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage>{fieldState.error.message}</FormMessage>
                  )}
                </FormItem>

                <Button
                  type="button"
                  onClick={handleSubmit(onSubmit)}
                  className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-10 py-6 text-[16px]"
                  disabled={isSubmitting}
                >
                  Verify
                  <MoveRight className="ml-1 w-4 h-4" />
                </Button>

                <div className="text-sm text-gray-500 pt-2">
                  {countdown > 0 ? (
                    <>Resend OTP in {countdown}s</>
                  ) : (
                    <button
                      className="text-blue-500 underline"
                      onClick={() => setCountdown(30)}
                    >
                      Resend OTP
                    </button>
                  )}
                </div>
              </div>
            )}
          />
        </div>
      </FormProvider>

      <div className="w-[50%] flex justify-end items-end">
        <AuthCommonComponent />
      </div>

      {showToast && (
        <div className="fixed bottom-4 right-4 z-50">
          <CustomToast
            title="Submitted Values"
            message={toastMessage}
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
