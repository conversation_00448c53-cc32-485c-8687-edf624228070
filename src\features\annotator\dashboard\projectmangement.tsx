import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getAnnonatorProjects } from "../annonator_api/annonator_api";
import { BackButton } from "@/_components/common";
import calender from "@/assets/icons/clenderclient.svg";
import clock from "@/assets/icons/clockclient.svg";
import profile from "@/assets/icons/clientprofile.svg";
import checked from "@/assets/icons/checkedcleint.svg";

type Project = {
    id: string;
    name: string;
    description: string;
    startDate: string;
    dueDate: string;
    status: string;
    priority: string;
    createdBy: {
        name: string;
    };
    tasks?: any[];
};

const AnnotatorProjectManagement = () => {
    const navigate = useNavigate();
    const [showToast, setShowToast] = useState(false);
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Format date to DD MMM YYYY
    const formatDate = (dateString: string) => {
        try {
            const date = new Date(dateString);
            return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;
        } catch (e) {
            return "Invalid date";
        }
    };

    // Calculate duration in weeks
    const calculateDuration = (startDate: string, endDate: string) => {
        try {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end.getTime() - start.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const diffWeeks = Math.ceil(diffDays / 7);
            return `${diffWeeks} weeks`;
        } catch (e) {
            return "Unknown duration";
        }
    };

    // Get priority badge style
    const getPriorityBadge = (priority: string) => {
        switch (priority.toLowerCase()) {
            case 'low':
                return 'bg-blue-800 text-white';
            case 'medium':
                return 'bg-orange-500 text-white';
            case 'high':
                return 'bg-red-600 text-white';
            default:
                return 'bg-gray-600 text-white';
        }
    };

    // Handle success toast
    const handleSuccess = () => {
        setShowToast(true);
        setTimeout(() => {
            setShowToast(false);
        }, 3000);
    };

    useEffect(() => {
        const fetchProjects = async () => {
            try {
                setLoading(true);
                const response = await getAnnonatorProjects();

                if (response && response.status === 1 && Array.isArray(response.data)) {
                    setProjects(response.data);
                } else {
                    setProjects([]);
                }
            } catch (err) {
                console.error("Error fetching projects:", err);
                setError("Failed to load projects. Please try again later.");
            } finally {
                setLoading(false);
            }
        };

        fetchProjects();
    }, []);

    // Prepare the content based on loading and error states
    let content;

    if (loading) {
        content = <div className="lg-only:p-3 xl-only:p-4 2xl-only:p-5 text-center lg-only:text-sm xl-only:text-base 2xl-only:text-lg">Loading projects...</div>;
    } else if (error) {
        content = <div className="text-red-500 lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:text-sm xl-only:text-base 2xl-only:text-lg">{error}</div>;
    } else if (projects.length === 0) {
        content = (
            <div className="col-span-full lg-only:h-[250px] xl-only:h-[300px] 2xl-only:h-[350px] flex items-center justify-center">
                <div className="text-lg font-medium  mt-10 text-gray-500 flex items-center justify-center text-center "><span>
                    There are no annotators available at the moment.</span></div>
            </div>
        );
    } else {
        content = (
            <div className="grid grid-cols-1 md:grid-cols-2 lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5 gap-3 py-2 mx-auto max-w-[98%]">
                {projects.map((project) => (
                    <div key={project.id} className="bg-white lg-only:px-2 xl-only:px-2.5 2xl-only:px-3 lg-only:pt-2 xl-only:pt-2.5 2xl-only:pt-3 rounded-lg shadow-md overflow-hidden border border-gray-200">
                        {/* Project Header with Name and Priority Badge */}
                        <div className="lg-only:px-2 xl-only:px-2.5 2xl-only:px-3">
                            <div className="flex justify-between items-center">
                                <div className="flex items-center">
                                    <span className="text-gray-800 font-semibold lg-only:text-xs xl-only:text-sm 2xl-only:text-base">{project.name.split(" ").slice(0, 2).join(" ")}</span>
                                </div>
                                <span className={`ml-2 lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-0.5 2xl-only:px-2.5 2xl-only:py-1 lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-xs rounded-full ${getPriorityBadge(project.priority)}`}>
                                    {project.priority.toUpperCase()}
                                </span>
                            </div>
                        </div>

                        {/* Project Description */}
                        <div className="lg-only:p-2 xl-only:p-2.5 2xl-only:p-3">
                            <p className="lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-gray-700 line-clamp-2">
                                {project.description || "We are seeking a highly skilled and motivated individual with expertise in technical drawing, environmental engineering."}
                            </p>
                        </div>

                        {/* Project Details */}
                        <div className="lg-only:p-2 xl-only:p-2.5 2xl-only:p-3 lg-only:space-y-1.5 xl-only:space-y-2 2xl-only:space-y-2.5">
                            {/* Started */}
                            <div className="flex items-center lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-[12px] justify-between">
                                <div className="flex lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-2.5">
                                    <img src={calender} alt="calender" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                                    <span className="lg-only:w-16 xl-only:w-18 2xl-only:w-20 text-gray-600">Started:</span>
                                </div>
                                <span className="text-gray-800">{formatDate(project.startDate)}</span>
                            </div>

                            {/* Duration */}
                            <div className="flex items-center lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-[12px] justify-between">
                                <div className="flex lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-2.5">
                                    <img src={clock} alt="clock" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                                    <span className="lg-only:w-16 xl-only:w-18 2xl-only:w-20 text-gray-600">Duration:</span>
                                </div>
                                <span className="text-gray-800">{calculateDuration(project.startDate, project.dueDate)}</span>
                            </div>

                            {/* Posted By */}
                            <div className="flex items-center lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-[12px] justify-between">
                                <div className="flex lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-2.5">
                                    <img src={profile} alt="profile" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                                    <span className="lg-only:w-16 xl-only:w-18 2xl-only:w-20 text-gray-600">Posted By:</span>
                                </div>
                                <span className="text-gray-800">{project.createdBy?.name || "Kevin Cooper"}</span>
                            </div>

                            {/* Status */}
                            <div className="flex items-center lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-[12px] justify-between">
                                <div className="flex lg-only:gap-1.5 xl-only:gap-2 2xl-only:gap-2.5">
                                    <img src={checked} alt="checked" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                                    <span className="lg-only:w-16 xl-only:w-18 2xl-only:w-20 text-gray-600">Status:</span>
                                </div>
                                <span className="lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-0.5 2xl-only:px-2.5 2xl-only:py-1 lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-xs rounded-full border border-[#E96B1C] text-[#E96B1C]">
                                    {project.status || "Ongoing"}
                                </span>
                            </div>
                        </div>

                        {/* Project Details Button */}
                        <div className="lg-only:p-2 xl-only:p-2.5 2xl-only:p-3">
                            <button
                                onClick={() => {
                                    handleSuccess();
                                    navigate(`/annotator/annotatordescription?id=${project.id}`);
                                }}
                                className="w-full lg-only:py-1.5 xl-only:py-2 2xl-only:py-2.5 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-medium rounded-md hover:opacity-90 transition-opacity"
                            >
                                Project Details
                            </button>
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    return (
        <div className="lg-only:p-3 xl-only:p-4 2xl-only:p-5 mx-auto">
            <div className="flex lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3 lg-only:mb-2 xl-only:mb-2.5 2xl-only:mb-3 items-center">
                <BackButton />
                <h1 className="lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl font-bold text-gray-800">Projects</h1>
            </div>

            {showToast && (
                <div className="lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4 lg-only:p-1.5 xl-only:p-2 2xl-only:p-2.5 bg-green-500 text-white rounded lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                    Action successful!
                </div>
            )}

            {content}
        </div>
    );
};

export default AnnotatorProjectManagement;
