"use client";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Annotator } from "@/types/onboarding.types";
import { PermissionSelector } from "./permission.coworker";
import { useResendCoWorkerInviteMutation } from "../api/mutation";

// useAdminColumns hook definition
export const ClientcoworkerColumns = (): ColumnDef<Annotator>[] => {
  return [
    {
      accessorKey: "email",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Email
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4 text-[14px] font-medium">
          {row.getValue("email")}
        </div>
      ),
    },
    {
      accessorKey: "coworkername",
      header: () => (
        <div
          className="w-[200px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          CoWorker Name
        </div>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-medium">
          {row.original.name.length > 0 ? row.original.name : "-"}
        </div>
      ),
    },

    {
      accessorKey: "joinon",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Join on
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4 text-[14px] font-medium">
          {row.getValue("joinon")}
        </div>
      ),
    },
    {
      accessorKey: "permission",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Permission
        </Button>
      ),
      cell: ({ row }) => <PermissionSelector row={row} />,
    },

    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        const { mutate: resendInvite, isPending } = useResendCoWorkerInviteMutation();
        return (
          <div className="pl-4">
            <Button
              variant="gradient"
              className="px-7 rounded-xl"
              onClick={() => resendInvite(row.original.id)}
            >
              {isPending ? "Resending..." : "Resend Invite"}
            </Button>
          </div>
        );
      },
    },
  ];
};
