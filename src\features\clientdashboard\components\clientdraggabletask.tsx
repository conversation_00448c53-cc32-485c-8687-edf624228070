// import { Task } from "@/types/adminkanbantype";
import { useDraggable } from "@dnd-kit/core";
import { motion } from "framer-motion";
import { CheckCircle2, Circle, Clock, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ClientUpdateTask from "./clientupdatetask";
import { Column } from "./kanbandashboard";
import { DetailsTask } from "@/types/kanbantasktype";

interface DraggableTaskProps {
  task: DetailsTask;
  column: Column;
  onDelete: (task: DetailsTask) => void;
  fetchTasks: () => void;
}

export function ClientDraggableTask({
  task,
  column,
  onDelete,
  fetchTasks,
}: DraggableTaskProps) {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: task.id,
    data: { task, column },
  });

  // Get icon based on column
  const getTaskIcon = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return <Circle className="h-4 w-4 text-blue-500" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "done":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return null;
    }
  };

  // Get indicator color based on column
  // const getTitleIndicatorColor = (columnId: string) => {
  //   switch (columnId) {
  //     case "todo":
  //       return "bg-[#2525AB]";
  //     case "in-progress":
  //       return "bg-[#E96B1C]";
  //     case "done":
  //       return "bg-[#E91C24]";
  //     default:
  //       return "bg-gray-400";
  //   }
  // };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
    >
      {/* Task Card with colored border */}
      <Card
        ref={setNodeRef}
        {...attributes}
        {...listeners}
        className={`mb-3 shadow-sm transition-shadow hover:shadow-md relative cursor-move ${
          isDragging ? "opacity-50" : ""
        }`}
        style={{
          borderLeft: `4px solid ${task.color || "#60A5FA"}`,
          borderTop: `1px solid ${task.color || "#60A5FA"}20`,
          borderRight: `1px solid ${task.color || "#60A5FA"}20`,
          borderBottom: `1px solid ${task.color || "#60A5FA"}20`,
        }}
      >
        <CardHeader className="p-3">
          {/* Due Date */}
          <div className="flex justify-end">
            <h1 className="font-normal text-xs">
              Due Date:{" "}
              {task.dueDate
                ? new Date(task.dueDate).toLocaleDateString()
                : "N/A"}
            </h1>
          </div>

          {/* Task Title and Actions */}
          <div className="flex items-center justify-between overflow-hidden">
            <div className="flex items-center flex-grow">
              {getTaskIcon(column.id)}
              <CardTitle className="text-sm font-medium ml-1 line-clamp-4 w-28">
                {task.title?.trim().split(/\s+/).slice(0, 6).join(" ")}
                {task.title?.trim().split(/\s+/).length > 6 ? "..." : ""}
              </CardTitle>
            </div>
            <div
              className="flex ml-2 z-50"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onPointerDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {/* Edit Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => e.stopPropagation()}
                aria-label={`Edit task: ${task.title}`}
              >
                <ClientUpdateTask taskId={task.id} onTaskUpdated={fetchTasks} />
              </Button>
              {/* Delete Button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(task);
                }}
                aria-label={`Delete task: ${task.title}`}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Task Description */}
          <CardDescription className="text-xs line-clamp-4">
            {task.description}
          </CardDescription>

          {/* Priority Level Indicator */}
          <div className="flex justify-end mt-2">
            <div
              className="w-5 h-5 flex justify-center items-center rounded-full text-white"
              style={{ backgroundColor: task.color || "#60A5FA" }}
            >
              <p className="text-xs">{task.level}</p>
            </div>
          </div>
        </CardHeader>
      </Card>
    </motion.div>
  );
}
