import { useEffect, useState } from "react";
import { BsBagDash } from "react-icons/bs";
import { FiUser } from "react-icons/fi";
import { GiSandsOfTime } from "react-icons/gi";
import { getCoordinatorDashboardData } from "@/features/projectcordinator/api/api";
import { Link } from "react-router-dom";

type DashboardData = {
    clientCount: number;
    activeAnnotators: number;
    activeProjects: number;
};

const TopSectionDashboard = () => {
    const [dashboardData, setDashboardData] = useState<DashboardData>({
        clientCount: 0,
        activeAnnotators: 0,
        activeProjects: 0
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDashboardData = async () => {
            try {
                const response = await getCoordinatorDashboardData();
                if (response && response.data) {
                    setDashboardData(response.data);
                }
            } catch (error) {
                console.error("Error fetching dashboard data:", error);
            } finally {
                setLoading(false);
            }
        };

        fetchDashboardData();
    }, []);

    return (
        <div className="mt-2">
            <div className="grid lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-3 justify-center gap-4 lg:gap-6 xl:gap-8 2xl:gap-10">
                {/* Total Annotators response */}
                <Link to='/coordinator/projectdetails/annotators'>
                    <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
                        <span className="flex items-center gap-3">
                            <FiUser className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
                            <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                                Total Annotators
                            </p>
                        </span>
                        <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
                            {loading ? "Loading..." : dashboardData.activeAnnotators}
                        </p>
                    </div>
                </Link>

                {/* Total Projects response*/}
                <Link to='/coordinator/projectdetails/projects'>
                    <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
                        <span className="flex items-center gap-3">
                            <BsBagDash className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
                            <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                                Total Projects
                            </p>

                        </span>
                        <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
                            {loading ? "Loading..." : dashboardData.activeProjects}
                        </p>
                    </div>
                </Link>

                {/* Total Clients response */}
                        <Link to='/coordinator/projectdetails/clients'>
                <div className="flex flex-col rounded-md border-gradient gap-2 lg:w-full lg:h-[90px] xl:w-full xl:h-auto 2xl:w-full 2xl:h-auto px-3 py-3">
                    <span className="flex items-center gap-3">
                        <GiSandsOfTime className="text-[#D53148] lg:text-lg xl:text-xl 2xl:text-2xl" />
                            <p className="text-[#545454] font-medium lg:text-base xl:text-lg 2xl:text-xl">
                                Total Clients
                            </p>


                    </span>
                    <p className="text-[#545454] font-semibold lg:text-xl xl:text-2xl 2xl:text-3xl">
                        {loading ? "Loading..." : dashboardData.clientCount}
                    </p>
                </div>
                        </Link>
            </div>
        </div>
    );
};

export default TopSectionDashboard;
