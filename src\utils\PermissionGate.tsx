import React from "react";
import { useRole } from "@/context/roles.context";

interface PermissionGateProps {
  permission: keyof ReturnType<typeof useRole>["permissions"];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionGate = ({
  permission,
  children,
  fallback = null,
}: PermissionGateProps) => {
  const { permissions } = useRole();
  return permissions?.[permission] ? <>{children}</> : <>{fallback}</>;
};
