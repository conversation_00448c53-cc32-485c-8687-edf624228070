import { baseUrl } from "@/globalurl/baseurl";
import { customAxios } from "@/utils/axio-interceptor";

export const fetchChats = async () => {
    try {
      const response = await customAxios.get(`${baseUrl}/v1/messages/chats/sidebar`, {
        data: []
      });
      //console.log(response.data, "chattts")
      return response.data;
      
    } catch (error) {
      console.error('Error fetching chats:', error);
      throw error;
    }
  };

  export const getMessagesBetweenUsers = async (otherUserId: string) => {
    try {
      const response = await customAxios.get(`${baseUrl}/v1/messages/get-all-message/${otherUserId}`);
      console.log(response.data, "getMessagesBetweenUsers")
      return response.data;
    } catch (error) {
      console.error('Error fetching chats:', error);
      throw error;
    }
  };

  export const getGroupMessages = async (groupId: string) => {
    try {
      const response = await customAxios.get(`${baseUrl}/v1/messages/group-messages/${groupId}`);
      console.log(response.data, "getGroupMessages")
      return response.data;
    } catch (error) {
      console.error('Error fetching chats:', error);
      throw error;
    }
  };

    export const getGroupMember = async (groupId: string) => {
    try {
      const response = await customAxios.get(`${baseUrl}/v1/messages/members/${groupId}`);
      console.log(response.data, "getGroupMember")
      return response.data;
    } catch (error) {
      console.error('Error fetching chats:', error);
      throw error;
    }
  };

  export const uploadFile = async (
    file: File,
    data: {
      senderId: string;
      receiverId?: string;
      groupId?: string;
      replyToId?: string;
      forwardedFromId?: string;
    },
    onProgress?: (progress: number) => void
  ) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Add all the required fields from the data object
      Object.entries(data).forEach(([key, value]) => {
        if (value) formData.append(key, value);
      });
      
      // Use the correct endpoint path
      const response = await customAxios.post(`${baseUrl}/v1/chat/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total && onProgress) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(percentCompleted);
          }
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  };
export const fetchConversationMedia = async (conversationId: string, fileType?: string) => {
  try {
    const url = `${baseUrl}/v1/messages/media/${conversationId}${fileType ? `?fileType=${fileType}` : ""}`;
    console.log("Calling fetchConversationMedia:", url);
    const response = await customAxios.get(url);
    console.log("fetchConversationMedia response:", response.data);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching conversation media:", error);
    throw error;
  }
};
  

// get rightside geoup members dataq
export const GroupsidebarMemebrsName  =() =>{
  
}

export interface Notification {
  id: string;
  type: string;
  message: string;
  metadata: {
    groupId?: string;
    messageId?: string;
    senderName?: string;
    groupName?: string;
    messageText?: string;
    isMention?: boolean;
  };
  isRead: boolean;
  createdAt: string;
}

export interface PaginatedNotifications {
  notifications: Notification[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

export const fetchNotifications = async (
  userId: string,
  options: { page?: number; limit?: number; unreadOnly?: boolean; search?: string } = {}
): Promise<PaginatedNotifications> => {
  try {
    const { page = 1, limit = 10, unreadOnly = false, search = "" } = options;
    const response = await customAxios.get(`${baseUrl}/v1/notification/all-notifications`, {
      params: {
        userId,
        page,
        limit,
        unreadOnly,
        ...(search && { search }),
      },
    });
    console.log(response.data, "fetchNotifications");
    return response.data.data;
  } catch (error) {
    console.error("Error fetching notifications:", error);
    throw error;
  }
};

export const markNotificationRead = async (notificationId: string): Promise<void> => {
  try {
    const response = await customAxios.patch(`${baseUrl}/v1/notification/read-notification/${notificationId}`, {
      isRead: true,
    });
    console.log(response.data, "markNotificationRead");
  } catch (error) {
    console.error("Error marking notification as read:", error);
    throw error;
  }
};
