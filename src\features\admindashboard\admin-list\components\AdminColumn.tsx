"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { AttendanceType } from "./attendancetype";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { HiDotsHorizontal } from "react-icons/hi";
import ResetPasswordModal from "../modal/resetpasswordmodal";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import ReactiveModalAdminList from "../modal/reactive";
import SuspendModalAdminlist from "../modal/suspended";
import { deleteAdmin } from "../adminlist_api/adminList_mutations";
import { toast } from "react-toastify";

export const useAdminColumns = (
  refetch: () => void
): ColumnDef<AttendanceType>[] => {
  return [
    {
      accessorKey: "name",
      header: () => (
        <div className="w-[200px] text-[14px] font-medium">
          Admin Name
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[14px] font-normal">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: () => (
        <Button variant="ghost" className="text-base text-[14px] font-medium">
          Email
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-6 text-[14px] font-normal">
          {row.getValue("email")}
        </div>
      ),
    },
    {
      accessorKey: "accountStatus",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue("accountStatus");
        const isActive = status === "ACTIVE";
        return (
          <button
            className={`w-[6rem] lg:w-[7rem] xl:w-[6rem] py-1 lg:py-1.5 xl:py-1 rounded-full text-white text-[14px] font-normal ${
              isActive ? "bg-green-500" : "bg-red-500"
            }`}
          >
            {isActive ? "Active" : "Suspended"}
          </button>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Joined On
        </Button>
      ),
      cell: ({ row }) => {
        const rawDate = row.getValue("createdAt") as string;
        const formatted = new Date(rawDate).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "2-digit",
        });
        return <div className="pl-6 text-[14px] font-normal">{formatted}</div>;
      },
    },
    {
      accessorKey: "updatedAt",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Last Login
        </Button>
      ),
      cell: ({ row }) => {
        const rawDate = row.getValue("updatedAt") as string;
        const formatted = new Date(rawDate).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "2-digit",
        });
        return <div className="pl-6 text-[14px] font-normal">{formatted}</div>;
      },
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        const userId = row.original.id;
        const [open, setOpen] = useState(false);
        const [isDeleting, setIsDeleting] = useState(false);
        const status = row.getValue("accountStatus");
        const isActive = status === "ACTIVE";

        return (
          <div className="pl-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild className="outline-none">
                <Button
                  variant="ghost"
                  className="px-6 lg:px-7 xl:px-8 rounded-xl outline-none"
                >
                  <HiDotsHorizontal className="text-xl lg:text-2xl" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[12.3rem] flex flex-col py-3 gap-3 font-poppins rounded-2xl">
                <ResetPasswordModal
                  userId={userId}
                  onSuccessRefresh={refetch}
                />
                
                {/* Conditionally render Suspend or Reactive based on current status */}
                {isActive ? (
                  <SuspendModalAdminlist
                    userId={userId}
                    onSuccessRefresh={refetch}
                  />
                ) : (
                  <ReactiveModalAdminList
                    userId={userId}
                    onSuccessRefresh={refetch}
                  />
                )}

                <Dialog open={open} onOpenChange={setOpen}>
                  <DialogTrigger asChild>
                    <Button variant="ghost" className="justify-start">
                      Delete Account
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="lg:px-[1.5rem] flex flex-col items-center justify-center xl:px-[1rem] py-6 lg:py-4 xl:py-6">
                    <DialogHeader>
                      <DialogTitle className="text-xl">
                        Do you want to delete this Admin account?
                      </DialogTitle>
                    </DialogHeader>
                    <DialogFooter>
                      <DialogClose asChild>
                        <Button
                          type="button"
                          className="border-gradient bg-white hover:bg-[#faf5f5] text-black px-14 lg:px-14 xl:px-16 py-6 lg:py-6 xl:py-7 text-base lg:text-base xl:text-lg"
                        >
                          Cancel
                        </Button>
                      </DialogClose>
                      <Button
                        variant="gradient"
                        className="px-14 lg:px-14 xl:px-16 py-6 lg:py-6 xl:py-7 text-base lg:text-base xl:text-lg"
                        disabled={isDeleting}
                        onClick={async () => {
                          setIsDeleting(true);
                          try {
                            await deleteAdmin(userId);
                            toast.success("Admin deleted successfully");
                            refetch();
                            setOpen(false);
                          } catch (err) {
                            toast.error("Failed to delete admin");
                            console.error("Delete error:", err);
                          } finally {
                            setIsDeleting(false);
                          }
                        }}
                      >
                        {isDeleting ? "Processing..." : "Delete"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];
};