import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { getAnnonatorProjectDetails } from "../../annonator_api/annonator_api";
import { customAxios } from "@/utils/axio-interceptor";

// Define client type
type Client = {
  id: string;
  name: string;
  email?: string;
  accountStatus?: string;
  projects?: number;
};

// Map status values to colors
// const statusColors: Record<string, string> = {
//   ACTIVE: "bg-green-500",
//   INACTIVE: "bg-red-500",
//   SUSPENDED: "bg-red-500",
//   PENDING: "bg-blue-500",
// };

const ClientsList: React.FC = () => {
  // const navigate = useNavigate();
  const location = useLocation();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);

        // Check if we're on a project details page
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get('id');

        if (projectId) {
          // If we're on a project details page, get the client from the project details
          const projectResponse = await getAnnonatorProjectDetails(projectId);

          if (projectResponse && projectResponse.data && projectResponse.data.createdBy) {
            // Use the client data from the project details
            const clientData = projectResponse.data.createdBy;

            // Create a client object with the data from the project details
            const client = {
              id: clientData.id,
              name: clientData.name,
              email: clientData.email,
              accountStatus: clientData.accountStatus,
              projects: 1 // We don't know the exact count, so we'll just show 1
            };

            setClients([client]);
          } else {
            // Fallback to the regular client list if project details don't have client info
            await fetchAllClients();
          }
        } else {
          // If we're not on a project details page, get all clients
          await fetchAllClients();
        }
      } catch (error) {
        console.error("Error fetching clients:", error);
        setClients([]);
      } finally {
        setLoading(false);
      }
    };

    const fetchAllClients = async () => {
      try {
        // Get clients data from API
        const response = await customAxios.get("/v1/annotator/get-all-clients?filter=client");

        if (response.data && response.data.data && response.data.data.data) {
          // Transform the API response to match our component's data structure
          const transformedClients = response.data.data.data.map((client: any) => ({
            id: client.id,
            name: client.client?.name || "Unknown Client",
            email: client.client?.email,
            accountStatus: client.client?.accountStatus,
            projects: client.client?._count?.projectsOwned || 0
          }));

          // Limit to 8 clients for the dashboard widget
          setClients(transformedClients.slice(0, 8));
        } else {
          setClients([]);
        }
      } catch (error) {
        console.error("Error fetching all clients:", error);
        setClients([]);
      }
    };

    fetchClients();
  }, [location.search]);

  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:h-[170px] xl-only:h-[170px] 2xl-only:h-[210px] flex flex-col">
      <div className="flex justify-between items-center lg-only:mb-2 xl-only:mb-2.5 2xl-only:mb-3">
        <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold font-poppins">Clients</h2>
        
      </div>

      <ul className="CustomScroll overflow-y-auto pr-1">
        {loading ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500 text-center">Loading clients...</p>
        ) : clients.length === 0 ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500 text-center">No clients found</p>
        ) : (
          clients.map((client, index) => (
            <li key={client.id || index} className="flex items-center lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3 lg-only:mb-2 xl-only:mb-2.5 2xl-only:mb-3 last:mb-0">
              <img
                src={getAvatarUrl(client.name)}
                alt="client avatar"
                className="lg-only:w-7 lg-only:h-7 xl-only:w-9 xl-only:h-9 2xl-only:w-10 2xl-only:h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="font-medium lg-only:text-[14px] xl-only:text-[16px] 2xl-only:text-[16px] text-[#282828]">
                    {client.name.split(" ").slice(0, 2).join(" ")}
                  </p>
                 
                </div>
                <div className="flex items-center justify-between">
                  <p className="lg-only:text-[12px] xl-only:text-[12px] 2xl-only:text-[12px] text-[#727272]">
                    {client.projects} {client.projects === 1 ? "project" : "projects"}
                  </p>
                
                </div>
              </div>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default ClientsList;
