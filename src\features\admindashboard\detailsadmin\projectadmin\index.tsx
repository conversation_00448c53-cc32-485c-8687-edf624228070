import { useState, useEffect } from "react";
import { getAllProjects } from "./project_api/project_api"; // API importer
import { useNavigate } from "react-router-dom";
import { LucideCalendarClock } from "lucide-react";
import { PiClockCountdown } from "react-icons/pi";
import { CgProfile } from "react-icons/cg";
import { TbCircleDashedCheck } from "react-icons/tb";
import CustomToast from "@/_components/common/customtoast";
// import ProjectCreate from "./components/project-create";

// Type definition for Project
interface Project {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  createdBy: {
    name: string;
  };
}

export default function Projects() {
  const [projects, setProjects] = useState<Project[]>([]); // Always array
  const [showToast, setShowToast] = useState(false);
  // const [isModalOpen, setIsModalOpen] = useState(false);
  const navigate = useNavigate();

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const res = await getAllProjects();
        if (res && Array.isArray(res.data)) {
          setProjects(res.data);
        } else {
          console.error("Expected res.data to be array, got:", res);
        }
      } catch (error) {
        console.error("Failed to fetch projects", error);
      }
    };
    fetchProjects();
  }, []);

  const getLevelStyle = (level: Project["priority"]) => {
    switch (level) {
      case "LOW":
        return "bg-blue-800";
      case "MEDIUM":
        return "bg-orange-500";
      case "HIGH":
        return "bg-red-500";
      default:
        return "bg-gray-400";
    }
  };

  const getLevelLetter = (level: Project["priority"]) => {
    switch (level) {
      case "LOW":
        return "Low";
      case "MEDIUM":
        return "Medium";
      case "HIGH":
        return "High";
      default:
        return "?";
    }
  };

  // const handleSuccess = () => {
  //   setShowToast(true);
  //   setTimeout(() => setShowToast(false), 4000);
  // };

  const getDuration = (start: string, end: string) => {
    const diff = new Date(end).getTime() - new Date(start).getTime();
    const weeks = Math.round(diff / (1000 * 60 * 60 * 24 * 7));
    return `${weeks} week${weeks !== 1 ? "s" : ""}`;
  };

  return (
    <div className="w-full p-4">
      {/* Projects List - Fully responsive grid with Tailwind */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-5 lg:gap-6">
        {projects.length > 0 ? (
          projects.map((project) => (
            <div
              key={project.id}
              className="border border-[#FF577F] rounded-lg shadow-md bg-white hover:shadow-lg transition-shadow duration-300 flex flex-col justify-between h-full"
            >
              <div className="flex flex-col gap-y-2 p-3 md:p-4">
                {/* Card Header */}
                <div className="flex items-center justify-between mb-2">
                  <h2 className="font-semibold text-sm md:text-base lg:text-lg truncate max-w-[70%]">
                    {project.name}
                  </h2>
                  <div
                    className={`px-2 md:px-3 py-1 rounded-full flex items-center justify-center text-white text-[10px] md:text-xs font-medium ${getLevelStyle(
                      project.priority
                    )}`}
                  >
                    {getLevelLetter(project.priority)}
                  </div>
                </div>

                {/* Description */}
                <p className="text-xs md:text-sm text-gray-700 line-clamp-1 mb-2">
                  {project.description}
                </p>

                {/* Project Info */}
                <div className="space-y-2 text-xs md:text-sm">
                  <p className="flex items-center gap-1 text-gray-600">
                    <LucideCalendarClock className="w-3 h-3 md:w-4 md:h-4 text-gray-500" />
                    <div className="flex w-full justify-between px-2">
                      <span className="font-medium">Started:</span>
                      <span>{new Date(project.startDate).toLocaleDateString()}</span>
                    </div>
                  </p>

                  <p className="flex items-center gap-1 text-gray-600">
                    <PiClockCountdown className="w-3 h-3 md:w-4 md:h-4 text-gray-500" />
                    <div className="flex w-full justify-between px-2">
                      <span className="font-medium">Duration:</span>
                      <span>{getDuration(project.startDate, project.dueDate)}</span>
                    </div>
                  </p>

                  <p className="flex items-center gap-1 text-gray-600">
                    <CgProfile className="w-3 h-3 md:w-4 md:h-4 text-gray-500" />
                    <div className="flex w-full justify-between px-2">
                      <span className="font-medium">Posted by:</span>
                      <span>{project.createdBy?.name || "N/A"}</span>
                    </div>
                  </p>

                  <p className="flex items-center justify-between text-gray-600">
                    <div className="flex items-center gap-x-1">
                      <TbCircleDashedCheck className="w-3 h-3 md:w-4 md:h-4 text-gray-500" />
                      <span className="font-medium">Status:</span>
                    </div>
                    <span className="border border-[#E96B1C] text-[#E96B1C] px-2 rounded-xl text-[10px] md:text-xs">
                      {project.status}
                    </span>
                  </p>
                </div>
              </div>

              {/* Button */}
              <div className="p-3 pt-0 md:p-4 md:pt-0">
                <button
                  onClick={() => navigate(`/admin/adminproject-details?id=${project.id}`)}
                  className="w-full py-1.5 md:py-2 text-xs md:text-sm bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg hover:opacity-90 transition-opacity"
                >
                  Project Details
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center text-gray-500 py-8">
           <p className="text-xl text-gray-500 font-semibold">There are no project available at the moment.</p>
          </div>
        )}
      </div>

     
      
      {/* Toast */}
      {showToast && (
        <div className="fixed top-5 right-5 z-50">
          <CustomToast
            title="Project Created"
            message="Your project has been successfully created!"
            type="success"
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
