import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useState, useEffect } from "react";
import {AdminAnnonatorShiftTiming} from "../admindetails_api/admindetails_api"

// Update interface to include annotatorId
interface ShiftChangeProps {
  onClose: () => void;
  onSuccess: () => void;
  annotatorId: string; // Add annotatorId to props
}

export default function ShiftChange({ onClose, onSuccess, annotatorId }: ShiftChangeProps) {
  const [fromTime, setFromTime] = useState("");
  const [toTime, setToTime] = useState("");
  const [note, setNote] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null); // Add state for error handling

  useEffect(() => {
    setIsOpen(true);
  }, []);

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(onClose, 300);
  };

  const generateTimeSlots = () => {
    const times = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let min = 0; min < 60; min += 30) {
        const formattedHour = hour.toString().padStart(2, "0");
        const formattedMin = min.toString().padStart(2, "0");
        times.push(`${formattedHour}:${formattedMin}`);
      }
    }
    return times;
  };

  const timeOptions = generateTimeSlots();

 const handleSubmit = async () => {
  if (!fromTime || !toTime) {
    setError("Please select both From and To time.");
    return;
  }
  try {
    const response = await AdminAnnonatorShiftTiming({
      annotatorId, // Already received as prop
      newFrom: fromTime,
      newTo: toTime,
    });
    if (response.status === 1) {
      console.log("Shift change successful:", response);
      handleClose();
      setTimeout(() => {
        onSuccess();
      }, 300);
    } else {
      setError("Failed to change shift. Please try again.");
    }
  } catch (err) {
    console.error("Error submitting shift change:", err);
    setError("An error occurred. Please try again later.");
  }
};
   
  return (
    <div className="p-4">
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={handleClose}></div>

      <div
        className={`fixed top-0 right-0 px-5 py-4 h-screen xl:w-[22rem] 2xl:w-[30rem] lg:w-[16rem] flex flex-col justify-between bg-white shadow-lg z-50 transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <div className="flex flex-row justify-between text-lg font-bold text-gray-800 mb-4 border-b pb-2">
            <h2 className="">Shift Change</h2>
            <span onClick={handleClose} className="cursor-pointer hover:text-[#585858] text-[#727272]">
              <X />
            </span>
          </div>
          {/* Display error message if any */}
          {error && (
            <div className="mb-4 text-red-500 text-sm">{error}</div>
          )}
          <div className="flex flex-row gap-x-6">
            <div className="mb-4">
              <label className="block text-sm font-medium">From:</label>
              <div className="border-gradient rounded-[8px]">
                <select
                  value={fromTime}
                  onChange={(e) => setFromTime(e.target.value)}
                  className="w-full focus:outline-none focus:ring-0 bg-[#F9EFEF] border p-2 rounded-md text-[13px] text-[#5E5E5E] mr-14"
                >
                  <option value="">00:00 AM</option>
                  {timeOptions.map((time, index) => (
                    <option key={index} value={time}>{time}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium">To:</label>
              <div className="border-gradient rounded-[8px]">
                <select
                  value={toTime}
                  onChange={(e) => setToTime(e.target.value)}
                  className="w-full bg-[#F9EFEF] text-[#5E5E5E] focus:outline-none focus:ring-0 border p-2 rounded-md text-[13px] mr-14"
                >
                  <option value="">00:00 AM</option>
                  {timeOptions.map((time, index) => (
                    <option key={index} value={time}>{time}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          <div className="mb-4 flex flex-col justify-start items-start">
            <label className="block text-sm font-medium">Note:</label>
            <div className="border-gradient rounded-[8px] w-full">
              <div className="bg-[#F9EFEF] rounded-[6px] p-1">
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  className="w-full p-2 placeholder:text-[#5E5E5E] text-[13px] bg-transparent rounded-md h-20 focus:outline-none"
                  placeholder="Enter note*"
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-row items-end justify-end gap-4 mt-4">
          <button onClick={handleClose} className="px-6 py-2 border-gradient rounded-md text-gray-600">
            Cancel
          </button>
          <Button
            onClick={handleSubmit}
            variant={"gradient"}
            className="px-6 py-2 to-[#45ADE2] text-white rounded-md"
          >
            Request
          </Button>
        </div>
      </div>
    </div>
  );
}