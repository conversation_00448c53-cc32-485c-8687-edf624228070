import { useState, useEffect } from "react";
import CustomToast from "@/_components/common/customtoast";
import CoordinatorCard from "./components/CoordinatorCard";
import { getAdminCoordinators } from "../admindetails_api/admindetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

// Interface matching the API response structure
interface CoordinatorProps {
    id: string;
    name: string;
    email: string;
    role: string;
    createdAt: string;
    packageId: string | null;
    assignmentsAsCoordinator: {
        id: string;
        clientId: string;
        developerId: string;
        coordinatorId: string;
        packageId: string;
        createdAt: string;
        client: {
            _count: {
                projectsOwned: number;
                coWorkers: number;
            }
        }
    }[];
    totalClients: number;
    totalAnnotators: number;
    totalCoWorkers: number;
    totalProjects: number;
    // Additional props for UI display
    image?: string;
}

// Response interface type is defined for documentation purposes
// This helps understand the API response structure
/*
type ApiResponse = {
    message: string;
    data: {
        data: CoordinatorProps[];
        totalCount: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
        nextPage: number | null;
        previousPage: number | null;
    };
}

Example response:
{
    "message": "Coordinators fetched successfully",
    "data": {
        "data": [
            {
                "id": "cmaeeznwv000lupvsupnmqbzo",
                "name": "2coordinator dev",
                "email": "<EMAIL>",
                "role": "PROJECT_COORDINATOR",
                "createdAt": "2025-05-07T20:52:18.511Z",
                "packageId": null,
                "assignmentsAsCoordinator": [...],
                "totalClients": 2,
                "totalAnnotators": 2,
                "totalCoWorkers": 0,
                "totalProjects": 2
            }
        ],
        "totalCount": 1,
        "totalPages": 1,
        "hasNextPage": false,
        "hasPreviousPage": false,
        "nextPage": null,
        "previousPage": null
    }
}
*/

export default function CoodinatorAdmin() {
    const [toasts, setToasts] = useState<any[]>([]);
    const [coordinators, setCoordinators] = useState<CoordinatorProps[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Fetch coordinators data from API
    useEffect(() => {
        const fetchCoordinators = async () => {
            try {
                setIsLoading(true);
                const response = await getAdminCoordinators();

                if (response && response.data && Array.isArray(response.data.data)) {
                    setCoordinators(response.data.data);
                } else {
                    setError("Invalid response format");
                }
            } catch (err) {
                console.error("Error fetching coordinators:", err);
                setError("Failed to fetch coordinators");
            } finally {
                setIsLoading(false);
            }
        };

        fetchCoordinators();
    }, []);

    const handleCloseToast = (id: number) => {
        setToasts((prev) => prev.filter((toast) => toast.id !== id));
    };

    // Show loading state
    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-full">
                <BrandedGlobalLoader isLoading={true} />
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="flex justify-center items-center h-full">
                <div className="text-red-500 text-center">
                    <p className="text-xl font-semibold">Error</p>
                    <p>{error}</p>
                </div>
            </div>
        );
    }

    // Show empty state
    if (coordinators.length === 0) {
        return (
            <div className="flex justify-center items-center h-full">
               <div className="text-lg font-medium  mt-10 text-gray-500 flex items-center justify-center text-center "><span>
          There are no coordinator available at the moment.</span></div>
            </div>
        );
    }

    return (
        <div className="relative">
            <div className="grid gap-6 w-full mx-auto max-w-[98%] px-2 lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5">
                {coordinators.map((coordinator, index) => (
                    <CoordinatorCard
                        key={coordinator.id || index}
                        coordinator={coordinator}
                    />
                ))}
            </div>

            {/* Toast Container */}
            <div className="fixed top-4 right-4 space-y-2 z-[9999]">
                {toasts.map((toast) => (
                    <CustomToast
                        key={toast.id}
                        title={toast.title}
                        message={toast.message}
                        type={toast.type}
                        onClose={() => handleCloseToast(toast.id)}
                    />
                ))}
            </div>
        </div>
    );
}