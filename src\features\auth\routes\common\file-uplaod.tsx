import { Button } from "@/components/ui/button";
import { useCallback } from "react";

interface FileUploadProps {
  onChange: (file: File) => void;
  value?: File;
  accept?: string;
}

const FileUpload = ({ onChange, value, accept }: FileUploadProps) => {
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        onChange(file);
      }
    },
    [onChange]
  );

  return (
    <div className="flex items-center gap-4">
      <div className="relative">
        <Button
          variant={"ghost"}
          type="button"
          className="px-6 py-3 relative text-[#767676] hover:bg-transparent hover:text-red-500 cursor-pointer"
        >
          Upload Proof Of Payment
          <input
            type="file"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleFileChange}
            accept={accept}
          />
        </Button>
      </div>
      <div className="text-sm text-gray-600 truncate max-w-[200px]">
        {value?.name}
      </div>
    </div>
  );
};

export default FileUpload;
