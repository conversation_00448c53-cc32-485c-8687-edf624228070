// @ts-ignore
import * as React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { Button } from "@/components/ui/button";
import PackagePage from "./component/package/packagepage";
import FeaturesPage from "./component/features/featurespage";
import PackageModal from "./component/modal/packagemodal";
import FeaturesModal from "./component/modal/featuresmodal";

import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog"; // adjust path if needed
import { Plus } from "lucide-react";
import { IoMdAddCircleOutline } from "react-icons/io";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

export default function PackageAdmin() {
  const [value, setValue] = React.useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
    console.log("handlechange event in package admin", event);
  };

  return (
    <Box sx={{ width: "100%" }}>
      {/* Tabs and button section */}
      <Box
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        {/* Tabs */}
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="tabs for packages and features"
          TabIndicatorProps={{
            style: {
              backgroundColor: "#FF577F",
              height: "4px",
            },
          }}
        >
          <Tab
            label="Packages"
            {...a11yProps(0)}
            sx={{
              borderRadius: "8px 8px 0 0",
              textTransform: "none",
              fontWeight: 600,
              mx: 1,
              fontSize: "18px",
              color: "#000000A8",
              "&.Mui-selected": {
                color: "#FF577F",
              },
              "&:hover": {
                color: "#FF577F",
              },
            }}
          />

          <Tab
            label="Features"
            {...a11yProps(1)}
            sx={{
              borderRadius: "8px 8px 0 0",
              textTransform: "none",
              fontWeight: 600,
              mx: 1,
              fontSize: "18px",
              color: "#000000A8",
              "&.Mui-selected": {
                color: "#FF577F",
              },
              "&:hover": {
                color: "#FF577F",
              },
            }}
          />
        </Tabs>

        {/* Modal buttons */}
        <Box sx={{ pr: 2 }}>
          {value === 0 && (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="gradient"
                  className="px-8 flex items-center gap-1"
                >
                  <span>Add New Package</span>
                  <IoMdAddCircleOutline className="!w-6 !h-6" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <PackageModal />
              </DialogContent>
            </Dialog>
          )}
          {value === 1 && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="gradient" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Add Features
                </Button>
              </DialogTrigger>
              <DialogContent>
                <FeaturesModal />
              </DialogContent>
            </Dialog>
          )}
        </Box>
      </Box>

      {/* Tab content */}
      <CustomTabPanel value={value} index={0}>
        <PackagePage />
      </CustomTabPanel>
      <CustomTabPanel value={value} index={1}>
        <FeaturesPage />
      </CustomTabPanel>
    </Box>
  );
}
