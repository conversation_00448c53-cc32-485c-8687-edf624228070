import React from 'react';
import { useNavigate } from "react-router-dom";
import { FaStar, FaRegStar } from "react-icons/fa";
import { useQuery } from "@tanstack/react-query";
import { getAllPackages } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package";

// Define the annotator props type
export interface AnnotatorCardProps {
  annotator: {
    id: string;
    name: string;
    email: string;
    image: string;
    status: string;
    shiftTiming: string;
    projects: string;
    joiningDate: string;
    subscription: string;
    packageId?: string; // Added for package lookup
    availableFrom?: string | null; // Added for real shift timing
    availableTo?: string | null; // Added for real shift timing
  };
  index: number;
  isStarred: boolean;
  toggleStar: (index: number) => void;
  openModal: (annotator: any) => void;
}



// Info Row Component
const InfoRow: React.FC<{
  label: string;
  value: string | React.ReactNode;
}> = ({
  label,
  value
}) => {
  return (
    <div className="grid grid-cols-2 lg:text-xs lg:py-0.5 xl:text-sm xl:py-0.5 2xl:text-base 2xl:py-1">
      <span className="text-gray-600 font-medium lg:w-[90px] xl:w-[100px] 2xl:w-[120px]">
        {label}
      </span>
      <span className="text-gray-800 text-right">{value}</span>
    </div>
  );
};

// Actions Component
const AnnotatorActions: React.FC<{
  onShiftChange: () => void;
  onAttendanceView: () => void;
}> = ({
  onShiftChange,
  onAttendanceView
}) => {
  return (
    <div className="grid grid-cols-2 gap-2 lg:gap-2 xl:gap-2 2xl:gap-3 lg:mt-2 xl:mt-3 2xl:mt-4">
      <button
        onClick={onShiftChange}
        className="border border-[#FF577F] text-[#FF577F] rounded-lg text-center lg:text-[11px] lg:px-2 lg:py-1 xl:text-xs xl:px-2 xl:py-1.5 2xl:text-sm 2xl:px-3 2xl:py-2"
      >
        Change Shift
      </button>
      <button
        onClick={onAttendanceView}
        className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg text-center lg:text-[10px] lg:px-2 lg:py-1 xl:text-xs xl:px-2 xl:py-1.5 2xl:text-sm 2xl:px-3 2xl:py-2"
      >
        Attendance Log
      </button>
    </div>
  );
};

// Utility functions
// Generate image URL using dicebear
const getCustomAvatarUrl = (name: string): string => {
  if (!name) return "";

  // Get the first name (first word)
  const firstName = name.trim().split(" ")[0];

  // Take the first two letters of the first name
  const initials = firstName.length >= 2
    ? firstName.substring(0, 2).toUpperCase()
    : firstName.charAt(0).toUpperCase();

  return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}`;
};

// Format date to dd/mm/yy
const formatDate = (dateString: string) => {
  if (!dateString) return "0";

  try {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  } catch (error) {
    return "0";
  }
};

// Format shift timing
const formatShiftTiming = (availableFrom: string | null | undefined, availableTo: string | null | undefined) => {
  // If both values are null or empty strings, return "0"
  if ((!availableFrom || availableFrom === "") && (!availableTo || availableTo === "")) {
    return "0";
  }

  // If availableFrom is a time string (not a date object)
  if (availableFrom && !availableFrom.includes('T')) {
    const fromTime = availableFrom || "0";
    const toTime = availableTo || "0";
    return `${fromTime} to ${toTime}`;
  }

  // If availableFrom is a date string
  if (availableFrom) {
    try {
      const fromTime = new Date(availableFrom);
      const fromHours = fromTime.getHours().toString().padStart(2, '0');
      const fromMinutes = fromTime.getMinutes().toString().padStart(2, '0');

      if (availableTo) {
        const toTime = new Date(availableTo);
        const toHours = toTime.getHours().toString().padStart(2, '0');
        const toMinutes = toTime.getMinutes().toString().padStart(2, '0');
        return `${fromHours}:${fromMinutes} to ${toHours}:${toMinutes}`;
      }

      return `${fromHours}:${fromMinutes}`;
    } catch (error) {
      // If there's an error parsing the date, return the raw values
      return `${availableFrom || "0"} to ${availableTo || "0"}`;
    }
  }

  return "0";
};

// Main AnnotatorCard Component
const AnnotatorCard: React.FC<AnnotatorCardProps> = ({
  annotator,
  index,
  isStarred,
  toggleStar,
  openModal
}) => {
  const navigate = useNavigate();

  // Use the package lookup hook
  const { data: packageData, isLoading: isPackageLoading } = useQuery({
    queryKey: ["packageList"],
    queryFn: getAllPackages,
    enabled: !!annotator.packageId, // Only run if we have a packageId
  });

  // Convert package ID to a readable subscription name
  const getSubscriptionName = (packageId: string | undefined) => {
    if (!packageId) return annotator.subscription || "Standard";

    if (isPackageLoading || !packageData || !packageData.data || !packageData.data.packages) {
      return annotator.subscription || "Standard";
    }

    // Check if packageId looks like a UUID (has dashes and is long)
    const isUuid = packageId && packageId.includes('-') && packageId.length > 30;

    if (!isUuid) {
      // If it's not a UUID, it might already be a name, so return it as is
      return packageId || annotator.subscription || "Standard";
    }

    // Find the package with the matching ID
    const packageInfo = packageData.data.packages.find((pkg: any) => pkg.id === packageId);

    if (packageInfo) {
      return packageInfo.name;
    } else {
      // If we can't find the package, return the subscription or a default value
      return annotator.subscription || "Standard";
    }
  };

  // Get the package name
  const packageName = getSubscriptionName(annotator.packageId);

  // Generate avatar URL if needed
  const imageUrl = annotator.image || getCustomAvatarUrl(annotator.name);

  return (
    <div className="border border-[#FF577F] rounded-lg shadow-md bg-white w-full h-full lg:p-3 xl:p-4 2xl:p-5">
      <div className="lg:mb-2 xl:mb-3 2xl:mb-4">
        <div className="grid grid-cols-2 mb-2">
          <div className="flex flex-row gap-2">
            <img
              src={imageUrl}
              alt={annotator.name}
              className="rounded-full lg:w-10 lg:h-10 xl:w-10 xl:h-10 2xl:w-12 2xl:h-12"
            />
            <div>
              <div className="flex items-center gap-x-1">
                <h3 className="font-semibold lg:text-sm xl:text-base 2xl:text-lg">{annotator.name.split(" ").slice(0, 1).join(" ")}

                </h3>
                <span onClick={() => toggleStar(index)} className="cursor-pointer">
                  {isStarred ?
                    <FaStar className="text-[#FFC107] lg:text-lg xl:text-lg 2xl:text-xl" /> :
                    <FaRegStar className="text-[#FFC107] lg:text-lg xl:text-lg 2xl:text-xl" />
                  }
                </span>
              </div>
              <p className="text-gray-500 lg:text-xs xl:text-xs 2xl:text-sm">{annotator.email}</p>
            </div>
          </div>
          <div className="text-right">
            <span className="inline-block bg-[#5AB24A] text-white rounded-full lg:px-2 lg:py-0.5 lg:text-xs xl:px-2 xl:py-0.5 xl:text-xs 2xl:px-3 2xl:py-1 2xl:text-sm">
              {annotator.status}
            </span>
          </div>
        </div>
      </div>

      <div className="space-y-2 lg:space-y-2 xl:space-y-3 2xl:space-y-4">
        <InfoRow
          label="Shift Timing:"
          value={formatShiftTiming(annotator.availableFrom, annotator.availableTo) || annotator.shiftTiming || "0"}
        />
        <InfoRow
          label="Projects:"
          value={annotator.projects || "0"}
        />
        <InfoRow
          label="Date of joining:"
          value={formatDate(annotator.joiningDate) || annotator.joiningDate || "0"}
        />
        <InfoRow
          label="Subscription:"
          value={packageName || annotator.subscription || "0"}
        />
      </div>

      <AnnotatorActions
        onShiftChange={() => openModal(annotator)}
        onAttendanceView={() => navigate(`/dashboard/attendance?id=${annotator.id}&name=${encodeURIComponent(annotator.name)}`)}
      />
    </div>
  );
};

export default AnnotatorCard;
