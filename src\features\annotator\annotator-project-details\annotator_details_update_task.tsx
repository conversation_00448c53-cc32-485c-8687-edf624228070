import React, { useEffect, useState } from "react";
import { IoIosArrowDown } from "react-icons/io";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "react-toastify";
import { getProjectTaskById, updateProjectTaskStatus } from "./api/api";

const colorOptions = [
  { name: "Orange", color: "#FBBF24" },
  { name: "Red", color: "#F87171" },
  { name: "Pink", color: "#F9A8D4" },
  { name: "Yellow", color: "#FACC15" },
  { name: "Blue", color: "#60A5FA" },
];

const initialForm = {
  name: "",
  description: "",
  priority: "",
  status: "",
  startDate: "",
  dueDate: "",
};

interface AdminUpdateTaskProps {
  taskId: string;
  onTaskUpdated: () => void;
}

const AnnotatorUpdateProjectTask: React.FC<AdminUpdateTaskProps> = ({
  taskId,
  onTaskUpdated,
}) => {
  // No need for dialog state anymore
  const [formData, setFormData] = useState(initialForm);
  const [errors, setErrors] = useState({
    name: false,
    description: false,
    priority: false,
    status: false,
    startDate: false,
    dueDate: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedColor, setSelectedColor] = useState("#60A5FA");
  const [showColorDropdown, setShowColorDropdown] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: false }));
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setShowColorDropdown(false);
  };

  const validateForm = () => {
    const newErrors = {
      name: !formData.name.trim(),
      description: !formData.description.trim(),
      priority: !formData.priority,
      status: !formData.status,
      startDate: !formData.startDate,
      dueDate: !formData.dueDate,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const taskData = {
        name: formData.name,
        description: formData.description,
        priority: formData.priority,
        status: formData.status,
        startDate: new Date(formData.startDate).toISOString(),
        dueDate: new Date(formData.dueDate).toISOString(),
        color: selectedColor,
      };

      await updateProjectTaskStatus(taskId, taskData);
      toast.success("Task updated successfully!");
      onTaskUpdated();
      handleClose();
    } catch (error) {
      console.error("Error updating task:", error);
      toast.error("Failed to update task");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Just reset the form
    setFormData(initialForm);
    setErrors({
      name: false,
      description: false,
      priority: false,
      status: false,
      startDate: false,
      dueDate: false,
    });
    setSelectedColor("#60A5FA");
    setShowColorDropdown(false);

    // Call onTaskUpdated to close the modal from parent
    onTaskUpdated();
  };

  const fetchTaskData = async () => {
    try {
      const response = await getProjectTaskById(taskId);

      // Handle different response structures
      let task;
      if (response && response.data) {
        task = response.data;
      } else if (response && response.status === 1) {
        task = response;
      } else {
        task = response;
      }

      if (task) {
        // Use either name or title field
        const taskName = task.name || task.title || "";

        setFormData({
          name: taskName,
          description: task.description || "",
          priority: task.priority || "",
          status: task.status || "",
          startDate: task.startDate
            ? new Date(task.startDate).toISOString().split("T")[0]
            : "",
          dueDate: task.dueDate
            ? new Date(task.dueDate).toISOString().split("T")[0]
            : "",
        });

        if (task.color) {
          setSelectedColor(task.color);
        }
      }
    } catch (error) {
      console.error("Error fetching task:", error);
    }
  };

  useEffect(() => {
    // Fetch task data when component mounts
    fetchTaskData();
  }, []);

  return (
    <div className="pointer-events-auto">
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Name + Color */}
        <div>
          <label className="text-sm font-medium text-[#282828]">
            Task Name *
          </label>
          <div className="flex items-center gap-2 mt-1">
            <div className="border-gradient w-full rounded-md">
              <input
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter task name"
                className={`w-full rounded-md px-4 py-2 bg-red-50 text-gray-700 placeholder-gray-500 focus:outline-none ${
                  errors.name ? "border border-red-500" : ""
                }`}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onPointerDown={(e) => e.stopPropagation()}
              />
            </div>
            <div className="relative">
              <div
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowColorDropdown((prev) => !prev);
                }}
              >
                <div className="w-[54px] h-[40px] bg-red-50 flex justify-center items-center gap-2 rounded-md border border-gradient">
                  <div
                    className="w-5 h-5 rounded-full border"
                    style={{ backgroundColor: selectedColor }}
                  />
                  <IoIosArrowDown />
                </div>
              </div>

              {showColorDropdown && (
                <div className="absolute top-[110%] right-0 bg-white border rounded-md shadow-md p-2 z-50 flex flex-col gap-2">
                  {colorOptions.map((c) => (
                    <div
                      key={c.name}
                      className="w-5 h-5 rounded-full cursor-pointer border hover:scale-110 transition"
                      style={{ backgroundColor: c.color }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleColorSelect(c.color);
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">Name is required</p>
          )}
        </div>

        {/* Description */}
        <div>
          <label className="text-sm font-medium text-[#282828]">
            Description *
          </label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={`w-full h-[9rem] overflow-y-scroll border-gradient mt-1 bg-[#F9EFEF] text-sm rounded-md focus:outline-none ${
              errors.description ? "border border-red-500" : ""
            }`}
            placeholder="Type your message here."
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
            onPointerDown={(e) => e.stopPropagation()}
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">Description is required</p>
          )}
        </div>

        {/* Priority & Status */}
        <div className="flex gap-4">
          <div className="flex-1">
            <label className="text-sm font-medium text-[#282828]">
              Priority *
            </label>
            <Select
              value={formData.priority}
              defaultValue={formData.priority}
              onValueChange={(value) => {
                setFormData((prev) => ({ ...prev, priority: value }));
                setErrors((prev) => ({ ...prev, priority: false }));
              }}
            >
              <SelectTrigger
                className={`w-full text-[#5E5E5E] py-3 focus:outline-none text-xs font-normal bg-[#F9EFEF] rounded-md ${
                  errors.priority ? "border border-red-500" : "border-gradient"
                }`}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onPointerDown={(e) => e.stopPropagation()}
              >
                <SelectValue placeholder="Select a priority">
                  {formData.priority || "Select a priority"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Select a priority</SelectLabel>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
            {errors.priority && (
              <p className="text-red-500 text-sm mt-1">Priority is required</p>
            )}
          </div>

          <div className="flex-1">
            <label className="text-sm font-medium text-[#282828]">
              Status *
            </label>
            <Select
              value={formData.status}
              defaultValue={formData.status}
              onValueChange={(value) => {
                setFormData((prev) => ({ ...prev, status: value }));
                setErrors((prev) => ({ ...prev, status: false }));
              }}
            >
              <SelectTrigger
                className={`w-full text-[#5E5E5E] py-3 focus:outline-none text-xs font-normal bg-[#F9EFEF] rounded-md ${
                  errors.status ? "border border-red-500" : "border-gradient"
                }`}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onPointerDown={(e) => e.stopPropagation()}
              >
                <SelectValue placeholder="Select a status">
                  {formData.status || "Select a status"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Select a status</SelectLabel>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-red-500 text-sm mt-1">Status is required</p>
            )}
          </div>
        </div>

        {/* Dates */}
        <div className="flex gap-4">
          <div className="flex-1">
            <label className="text-sm font-medium text-[#282828]">
              Start Date *
            </label>
            <input
              type="date"
              name="startDate"
              value={formData.startDate}
              onChange={handleChange}
              className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              onClick={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
              onPointerDown={(e) => e.stopPropagation()}
            />
            {errors.startDate && (
              <p className="text-red-500 text-sm mt-1">
                Start Date is required
              </p>
            )}
          </div>
          <div className="flex-1">
            <label className="text-sm font-medium text-[#282828]">
              Due Date *
            </label>
            <input
              type="date"
              name="dueDate"
              value={formData.dueDate}
              onChange={handleChange}
              className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              onClick={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
              onPointerDown={(e) => e.stopPropagation()}
            />
            {errors.dueDate && (
              <p className="text-red-500 text-sm mt-1">Due Date is required</p>
            )}
          </div>
        </div>

        <div className="flex gap-4 justify-end items-center">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleClose();
            }}
            type="reset"
            className="w-32 px-3 py-2 font-bold text-base rounded-md border-gradient border text-black transition"
            disabled={isSubmitting}
            onMouseDown={(e) => e.stopPropagation()}
            onPointerDown={(e) => e.stopPropagation()}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="w-32 px-3 py-2 font-bold text-base rounded-md bg-gradient-to-r from-[#E91C24] to-[#45ADE2] text-white transition"
            disabled={isSubmitting}
            onClick={(e) => e.stopPropagation()}
            onMouseDown={(e) => e.stopPropagation()}
            onPointerDown={(e) => e.stopPropagation()}
          >
            {isSubmitting ? "Updating..." : "Update Task"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AnnotatorUpdateProjectTask;
