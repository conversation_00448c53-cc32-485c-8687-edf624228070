import { baseUrl } from "@/globalurl/baseurl";
import axios, { AxiosResponse } from "axios";

// Create axios instance with proper type inference
export const customAxios = axios.create({
  baseURL: baseUrl,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Request interceptor
customAxios.interceptors.request.use(
  (config) => {
    // Add token to headers if needed
    // const token = localStorage.getItem("token");
    // if (token) {
    //   config.headers.Authorization = `${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
customAxios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error("Unauthorized access");
      localStorage.clear();
      // Optionally redirect to login page
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);