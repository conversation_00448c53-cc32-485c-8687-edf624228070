export interface TaskData {
    id: string;
    name: string;
    description: string;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED'; // You can adjust if there are more statuses
    assignedToId: string;
    createdById: string;
    startDate: string; // ISO date string
    dueDate: string;   // ISO date string
    createdAt: string; // ISO date string
    updatedAt: string; // ISO date string
}

// export interface CreateTaskResponse {
//     status: number;
//     message: string;
//     data: TaskData;
// }
