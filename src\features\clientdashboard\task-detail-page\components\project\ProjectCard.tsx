import React from 'react';
import { useNavigate } from "react-router-dom";

// Define the project props type
export interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description: string;
    image?: string;
    status: string;
    startDate: string;
    endDate: string;
    progress?: number;
    annotators?: string;
    tasks?: string;
  };
  index?: number;
  isStarred?: boolean;
  toggleStar?: (index: number) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  index = 0,
  isStarred = false,
  toggleStar = () => {}
}) => {
  const navigate = useNavigate();

  // Get status color based on project status
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500';
      case 'in progress':
        return 'bg-yellow-500';
      case 'pending':
        return 'bg-blue-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-[#FF577F] w-full h-full lg:p-2 xl:p-3 2xl:p-4">
      <div className="rounded-2xl lg:gap-y-2 lg:p-1 xl:gap-y-2.5 xl:p-1.5 2xl:gap-y-3 2xl:p-2">
        <div className="grid grid-cols-5 mb-2 lg:mb-2 xl:mb-3 2xl:mb-4">
          <div className="grid grid-cols-[auto_1fr] gap-2">
            {project.image && (
              <img
                src={project.image}
                alt={project.name}
                className="rounded-full object-cover lg:w-10 lg:h-10 xl:w-11 xl:h-11 2xl:w-12 2xl:h-12"
              />
            )}
            <div>
              <h3 className="font-semibold text-gray-800 lg:text-[16px] xl:text-[17px] 2xl:text-[18px]">
                {project.name.split(" ").slice(0, 3).join(" ")}
              </h3>
              <p className="font-normal line-clamp-1 text-gray-600 lg:text-xs xl:text-xs 2xl:text-sm">{project.description}</p>
            </div>
          </div>
          <div className="text-right">
            <span className={`inline-block text-white rounded-full lg:px-2 lg:py-0.5 lg:text-[9px] xl:px-2.5 xl:py-0.5 xl:text-[10px] 2xl:px-3 2xl:py-1 2xl:text-xs ${getStatusColor(project.status)}`}>
              {project.status}
            </span>
            {toggleStar && (
              <button
                onClick={() => toggleStar(index)}
                className="inline-block ml-2 focus:outline-none"
              >
                {isStarred ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="inline-block text-yellow-500 lg:h-4 lg:w-4 xl:h-4.5 xl:w-4.5 2xl:h-5 2xl:w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="inline-block text-gray-400 lg:h-4 lg:w-4 xl:h-4.5 xl:w-4.5 2xl:h-5 2xl:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                )}
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 lg:mb-2 xl:mb-3 2xl:mb-4">
          <div className="lg:text-xs xl:text-xs 2xl:text-sm">
            <span className="font-medium text-gray-700">Start Date: </span>
            <span className="text-gray-600">{project.startDate}</span>
          </div>
          <div className="lg:text-xs xl:text-xs 2xl:text-sm">
            <span className="font-medium text-gray-700">End Date: </span>
            <span className="text-gray-600">{project.endDate}</span>
          </div>
          {project.annotators && (
            <div className="lg:text-xs xl:text-xs 2xl:text-sm">
              <span className="font-medium text-gray-700">Annotators: </span>
              <span className="text-gray-600">{project.annotators}</span>
            </div>
          )}
          {project.tasks && (
            <div className="lg:text-xs xl:text-xs 2xl:text-sm">
              <span className="font-medium text-gray-700">Tasks: </span>
              <span className="text-gray-600">{project.tasks}</span>
            </div>
          )}
        </div>

        {project.progress !== undefined && (
          <div className="lg:mb-2 xl:mb-3 2xl:mb-4">
            <div className="grid grid-cols-2 mb-1">
              <span className="font-medium text-gray-700 lg:text-xs xl:text-xs 2xl:text-sm">Progress</span>
              <span className="font-medium text-gray-700 lg:text-xs xl:text-xs 2xl:text-sm text-right">{project.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full lg:h-1.5 xl:h-2 2xl:h-2.5">
              <div
                className="bg-gradient-to-r from-[#E91C24] to-[#45ADE2] rounded-full lg:h-1.5 xl:h-2 2xl:h-2.5"
                style={{ width: `${project.progress}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      <div className="w-full text-right space-x-2 lg:mt-2 xl:mt-3 2xl:mt-4">
        <button
          onClick={() => navigate(`/dashboard/project-details?id=${project.id}`)}
          className="text-white bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] rounded hover:opacity-90 transition-colors lg:px-2 lg:py-1 lg:text-xs xl:px-2.5 xl:py-1 xl:text-xs 2xl:px-3 2xl:py-1.5 2xl:text-sm"
        >
          View Details
        </button>
        <button
          onClick={() => navigate(`/dashboard/project/edit/${project.id}`)}
          className="text-white bg-blue-500 rounded hover:bg-blue-600 transition-colors lg:px-2 lg:py-1 lg:text-xs xl:px-2.5 xl:py-1 xl:text-xs 2xl:px-3 2xl:py-1.5 2xl:text-sm"
        >
          Edit
        </button>
        <button
          onClick={() => {
            if (window.confirm('Are you sure you want to delete this project?')) {
              console.log('Delete project', project.id);
            }
          }}
          className="text-white bg-red-500 rounded hover:bg-red-600 transition-colors lg:px-2 lg:py-1 lg:text-xs xl:px-2.5 xl:py-1 xl:text-xs 2xl:px-3 2xl:py-1.5 2xl:text-sm"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

export default ProjectCard;
