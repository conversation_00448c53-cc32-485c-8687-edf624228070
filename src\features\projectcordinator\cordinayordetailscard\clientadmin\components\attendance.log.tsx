import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { projectDummyData } from "./dummydata"; 
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";

const AdminClients = () => {
  const navigate = useNavigate();

  return (
    <div className=" bg-white h-full space-y-2">

      <div className="flex  flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)} 
        />

        <h1 className="text-[#282828] text-[24px]"><PERSON><PERSON><PERSON></h1>
      </div>

      <DataTable
        title="Forms"
        columns={useAdminColumns()}
        data={projectDummyData} // 👈 Pass dummy data here
        loading={false}
        disablePagination 
      />
    </div>
  );
};

export default AdminClients;
