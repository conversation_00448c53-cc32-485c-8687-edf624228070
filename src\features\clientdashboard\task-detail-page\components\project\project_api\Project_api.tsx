// utils/project_api.ts
import { customAxios } from "@/utils/axio-interceptor";

// Get all client-side projects
export const getAllClientProjects = async () => {
  const response = await customAxios.get("/v1/projects/client-projects");
  return response.data;
};

export const getAllClientCooworkerProjects = async () => {
  const response = await customAxios.get("/v1/projects/cooworker/client-projects");
  return response.data;
};
// Create a new project with multipart/form-data
export const createProject = async (formData: FormData, user: string | undefined) => {
  try {
    // Log the request being made
    console.log("Making API request to create project");

    const response = await customAxios.post(user === "CLIENT"? "/v1/projects/create-project": "/v1/projects/cooworker/create-project", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    console.log("Project creation successful:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("API error in createProject:", error);

    // Log more detailed error information
    if (error.response) {
      console.error("Error response status:", error.response.status);
      console.error("Error response data:", error.response.data);
    }

    throw error;
  }
};




export const AllAnoonatorClient = async () => {
  const response = await customAxios.get("/v1/dashboard/client-annotators");
  return response.data;
};

export const AllCooworkerAnoonatorClient = async () => {
  const response = await customAxios.get("/v1/dashboard/cooworker/client-annotators");
  return response.data;
};