import React, { useState } from 'react';
import ProjectCard from './ProjectCard';

interface Project {
  id: string;
  name: string;
  description: string;
  image?: string;
  status: string;
  startDate: string;
  endDate: string;
  progress?: number;
  annotators?: string;
  tasks?: string;
}

interface ProjectListProps {
  projects: Project[];
}

const ProjectList: React.FC<ProjectListProps> = ({ projects }) => {
  const [starredProjects, setStarredProjects] = useState<number[]>([]);

  const toggleStar = (index: number) => {
    if (starredProjects.includes(index)) {
      setStarredProjects(starredProjects.filter(i => i !== index));
    } else {
      setStarredProjects([...starredProjects, index]);
    }
  };

  return (
    <div className="w-full mx-auto px-4 lg:px-6 xl:px-8 2xl:px-10">
      <div className="grid grid-cols-5 gap-4 py-2 lg:grid-cols-5 lg:gap-5 lg:py-2 xl:grid-cols-5 xl:gap-4 xl:py-2.5 2xl:grid-cols-5 2xl:gap-5 2xl:py-3">
        {projects.map((project, index) => (
          <ProjectCard
            key={project.id}
            project={project}
            index={index}
            isStarred={starredProjects.includes(index)}
            toggleStar={toggleStar}
          />
        ))}
      </div>
    </div>
  );
};

export default ProjectList;
