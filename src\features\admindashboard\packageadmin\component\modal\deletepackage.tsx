// usePackageColumns.ts
import { deletePackageApi } from "../package/api_package/api_package";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { Dialog, DialogTrigger, DialogContent } from "@/components/ui/dialog";
import EditPackage from "./editpackage";
import { Button } from "@/components/ui/button";

export const usePackageColumns = () => {
  const queryClient = useQueryClient();

  const { mutate: deletePackage } = useMutation({
    mutationFn: (id: string) => deletePackageApi(id),
    onSuccess: () => {
      toast.success("Package deleted successfully", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      queryClient.invalidateQueries({ queryKey: ["packageList"] });
    },
    onError: (error) => {
      toast.error(`Failed to delete package: ${error.message}`, {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    },
  });

  return [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "original", header: "Original" },
    { accessorKey: "discount", header: "Discount" },
    { accessorKey: "status", header: "Status" },
    {
      header: "Actions",
      cell: ({ row }: any) => {
        const item = row.original;

        return (
          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">Edit</Button>
              </DialogTrigger>
              <DialogContent>
                <EditPackage data={item} />
              </DialogContent>
            </Dialog>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => deletePackage(item.id)}
            >
              Delete
            </Button>
          </div>
        );
      },
    },
  ];
};