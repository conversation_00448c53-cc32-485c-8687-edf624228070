// /pages/DashboardPage.tsx
import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import AnnotatorDetails from "@/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/annotatordetails";
import { getProjectDetails } from "@/features/clientdashboard/task-detail-page/components/projectdetails/projectdetails_api/projectdetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { NoData } from "@/_components/common";
import { baseUrl } from "@/globalurl/baseurl";
import ClientDetailsDescription from "@/features/clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/clientsproejctdescription";
import AdminTaskRecord from "./admintask";
import ProjectDetailsKanban from "./projectadmin/projectdetailskanban";
import { Button } from "@/components/ui/button";

// Interface for project details
interface ProjectData {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  createdBy: {
    name: string;
  };
  annotator: {
    name: string;
    email: string;
  };
  tasks: any[];
}

const ProjectDetails: React.FC = () => {
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();

  // Helper function to format date to dd/mm/yy
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = String(date.getFullYear()).slice(-2);
      return `${day}/${month}/${year}`;
    } catch (error) {
      return "Invalid date";
    }
  };

  // Helper function to calculate duration in weeks
  const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return "N/A";
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const weeks = Math.floor(diffDays / 7);
      const days = diffDays % 7;

      let result = [];
      if (weeks > 0) {
        result.push(`${weeks} ${weeks === 1 ? "Week" : "Weeks"}`);
      }
      if (days > 0) {
        result.push(`${days} ${days === 1 ? "Day" : "Days"}`);
      }

      return result.length > 0 ? result.join(" and ") : "0 Days";
    } catch (error) {
      return "N/A";
    }
  };
  // Helper function to get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority?.toUpperCase()) {
      case "HIGH":
        return "bg-red-500";
      case "MEDIUM":
        return "bg-orange-500";
      case "LOW":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Helper function to get status badge color
  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case "COMPLETED":
        return "bg-[#009A51]";
      case "PENDING":
        return "bg-yellow-500";
      case "IN_PROGRESS":
      case "INPROGRESS":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Helper function to format status text
  const formatStatus = (status: string) => {
    if (!status) return "Unknown";

    // Replace underscores with spaces and capitalize each word
    return status
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Function to handle attachment download
  const handleDownloadAttachment = (attachmentPath: string) => {
    if (!attachmentPath) return;

    // Check if attachmentPath is an absolute URL (starts with http:// or https://)
    const isAbsoluteUrl = /^https?:\/\//i.test(attachmentPath);
    let attachmentUrl = attachmentPath;

    // If it's not an absolute URL, prepend baseUrl
    if (!isAbsoluteUrl) {
      const formattedBaseUrl = baseUrl.endsWith("/")
        ? baseUrl.slice(0, -1)
        : baseUrl;
      const formattedPath = attachmentPath.startsWith("/")
        ? attachmentPath
        : `/${attachmentPath}`;
      attachmentUrl = `${formattedBaseUrl}${formattedPath}`;
    }

    console.log("Downloading attachment from URL:", attachmentUrl);

    const link = document.createElement("a");
    link.href = attachmentUrl;
    link.target = "_blank";
    link.rel = "noopener noreferrer";
    const fileName = attachmentPath.split("/").pop() || "attachment";
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Get project ID from URL parameters
  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get project ID from URL query parameters
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get("id");

        // If no project ID is provided, use a default or show an error
        if (!projectId) {
          console.error("No project ID provided in URL");
          setError(
            "Project ID is missing. Please go back and select a project."
          );
          setLoading(false);
          return;
        }

        // Fetch project details using the API
        const response = await getProjectDetails(projectId);

        if (response && response.status === 1 && response.data) {
          console.log("Project details:", response.data);
          setProject(response.data);
        } else {
          console.error("Invalid API response format:", response);
          setError("Failed to load project details. Please try again later.");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        setError("An error occurred while loading project details.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [location.search]);

  // Show loading state
  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message={error} />
      </div>
    );
  }

  // Show no data state if project is null
  if (!project) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message="No project details available" />
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col overflow-x-hidden">
      {" "}
      {/* Added overflow-x-hidden */}
      <div className="px-4">
        {" "}
        {/* Added padding to prevent edge sticking */}
        <div className="flex flex-col 2xl:flex-row xl:flex-row justify-between gap-x-14 gap-y-4">
          {/* Left/main content section */}
          <div className="flex-1 flex flex-col gap-5 min-w-0">
            {" "}
            {/* Added min-w-0 to prevent overflow */}
            {/* Project header */}
            <div className="flex items-center gap-3 flex-wrap">
              {" "}
              {/* Added flex-wrap */}
              <h1 className="text-[#122539] font-poppins font-semibold text-2xl">
                {project.name}
              </h1>
              <span
                className={`text-white text-xs px-2 py-1 rounded-full ${getPriorityColor(
                  project.priority
                )}`}
              >
                {project.priority}
              </span>
              <span
                className={`text-white text-xs px-2 py-1 rounded-full ${getStatusColor(
                  project.status
                )}`}
              >
                {formatStatus(project.status)}
              </span>
            </div>
            {/* Description */}
            <div className="w-full">
              <h1 className="text-[#122539]  font-sans font-normal text-sm lg:text-sm xl:text-sm 2xl:text-xl break-words">
                {project.description}
              </h1>
            </div>
            {/* Dates row - made responsive */}
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-5 sm:items-center  justify-between flex-wrap">
              <div className="flex gap-2 sm:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-[14px]">
                  Duration
                </h1>
                <p className="text-[#5E5E5E] font-normal text-[14px]">
                  {calculateDuration(project.startDate, project.dueDate)}
                </p>
              </div>
              <div className="flex gap-2 sm:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-[14px]">
                  Start Date
                </h1>
                <p className="text-[#5E5E5E] font-normal text-[14px]">
                  {formatDate(project.startDate)}
                </p>
              </div>
              <div className="flex gap-2 sm:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-[14px]">
                  End Date
                </h1>
                <p className="text-[#5E5E5E] font-normal text-[14px]">
                  {formatDate(project.dueDate)}
                </p>
              </div>
            </div>
            {/* Kanban board - removed fixed height */}
            <div className="mt-3">
              <ProjectDetailsKanban projectId={project.id} />
            </div>
          </div>

          {/* Right sidebar */}
          <div className="xl:w-[20%] 2xl:w-[30%] flex h-[5rem] flex-col gap-4">
            <Button
              variant={"gradient"}
              className="w-full  xl:w-full 2xl:w-full py-6 text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium border-gradient rounded-md"
              onClick={() => {
                if (project.attachment && project.attachment.length > 0) {
                  handleDownloadAttachment(project.attachment[0]);
                } else {
                  alert("No attachment available for this project");
                }
              }}
              disabled={!project.attachment || project.attachment.length === 0}
            >
              {project.attachment && project.attachment.length > 0
                ? "Download Attachment"
                : "No Attachment"}
            </Button>

            {/* Sidebar components */}
            <div className="flex flex-col gap-4">
              <div>
                <AdminTaskRecord projectId={project.id} />
              </div>
              <div>
                <AnnotatorDetails />
              </div>
              <div>
                <ClientDetailsDescription />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetails;
