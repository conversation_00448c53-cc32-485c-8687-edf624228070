// // BankOtherTable.tsx
// import { DataTable } from "@/components/globalfiles/data.table";
// import { useAdminColumns } from "./bankcolumn";
// import { FaArrowLeft } from "react-icons/fa6";
// import { useNavigate } from "react-router-dom";
// import BrandedGlobalLoader from "@/components/loaders/loaders.one";
// import { useEffect, useState } from "react";
// import { AdminGetBanktranferList } from "../banktransferapi/bank_transfer_api";
// import { BankDetailsType } from "./bankdetails.type";

// const BankOtherTable = () => {
//   const navigate = useNavigate();
//   const [data, setData] = useState<BankDetailsType[]>([]);
//   const [isLoading, setIsLoading] = useState(true);

//   const fetchData = async () => {
//     try {
//       setIsLoading(true);
//       const response = await AdminGetBanktranferList();
//       const transformedData: BankDetailsType[] = response.data.payments.map((payment) => ({
//         id: payment.id,
//         paymentId: payment.paymentId,
//         name: payment.user.name,
//         transactionId: payment.transactionId,
//         transactionDate: payment.createdAt,
//         transferedAccNo: payment.accountNumber,
//         bankHolderName: payment.bankHolderName,
//         amount: payment.amount,
//         accountNumber: payment.accountNumber,
//         status: payment.status,
//         screenshotUrl: payment.screenshotUrl,
//       }));
//       setData(transformedData);
//     } catch (error) {
//       console.error("Error fetching bank transfer data:", error);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   useEffect(() => {
//     fetchData();
//   }, []);

//   return (
//     <div className="bg-white h-full space-y-4 p-4">
//       <div className="flex flex-wrap gap-3 items-center">
//         <FaArrowLeft
//           className="text-2xl text-[#FF577F] cursor-pointer"
//           onClick={() => navigate(-1)}
//         />
//         <h1 className="text-[#282828] text-[24px] font-bold">
//           Bank Transactions
//         </h1>
//       </div>

//       {isLoading ? (
//         <BrandedGlobalLoader isLoading={true} />
//       ) : (
//         <div className="bg-[#FBF4F4] rounded-lg p-4 shadow-sm">
//           <DataTable
//             title="Transactions"
//             columns={useAdminColumns(fetchData)} // Pass fetchData to useAdminColumns
//             data={data}
//             loading={false}
//             disablePagination={false}
//           />
//         </div>
//       )}
//     </div>
//   );
// };

// export default BankOtherTable;