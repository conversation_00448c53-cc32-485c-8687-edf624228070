import React from 'react';

type Notification = {
  id: number;
  title: string;
  description: string;
};

const notifications: Notification[] = [
  {
    id: 1,
    title: '@Co-worker has accepted requested for joining the projects',
    description: 'Your request for joining the project has been accepted.',
  },
  {
    id: 2,
    title: '@Person has mentioned you in @Project/Task Title',
    description: 'Your request for joining the project has been accepted.',
  },
  {
    id: 3,
    title: '@Client name has assigned you a task in @Project/Task Title',
    description: 'Your request for joining the project has been accepted.',
  },
  {
    id: 4,
    title: '@coardinator has accepted your leave on @Date',
    description: 'Your request for joining the project has been accepted.',
  },
];

const NotificationCard: React.FC<Notification> = ({ title, description }) => (
  <div className="flex items-start space-x-3 bg-white rounded-lg shadow-md  p-7 mb-3">
    <img
      src="https://randomuser.me/api/portraits/women/44.jpg"
      alt="profile"
      className="w-13 h-14 rounded-full"
    />
    <div>
      <h4 className="text-md font-semibold  text-gray-800 font-inter">{title}</h4>
      <p className="text-[15px] text-gray-500 font-inter">{description}</p>
    </div>
    <span className="ml-auto text-xs text-gray-400 font-inter">1 day</span>
  </div>
);

const NotificationPanel: React.FC = () => {
  return (
    <div className="mx-auto mt-10">
      <div className="bg-white shadow-md rounded-lg p-7 mb-6">
        <h3 className="text-[22px] font-medium text-gray-800 mb-1 font-inter">
          @Enamuel Sah has requested for leave on 21st March, 2023
        </h3>
        <p className="text-[15px] mt-2 text-gray-500 font-inter">
          One of your team has requested edit access to your project{' '}
          <span className=" font-inter">Website Design</span>.
        </p>
        <div className="flex items-center mt-4 ">
          <img
            src="https://randomuser.me/api/portraits/women/44.jpg"
            alt="Heidi Turner"
            className="w-13 h-14 rounded-full mr-3"
          />
          <div>
            <p className="text-md font-semibold  text-gray-800 font-inter">Heidi Turner</p>
            <p className="text-[15px] text-gray-500 font-inter"><EMAIL></p>
          </div>
          <div className="ml-auto space-x-2">
            <button className="px-6 py-3 text-sm border rounded-md font-semibold text-gray-700 border-gray-300 hover:bg-gray-100 font-inter">
              Deny
            </button>
            <button className="px-5 py-3 text-sm bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 font-inter">
              Confirm
            </button>
          </div>
        </div>
      </div>

      {/* Notification Cards */}
      {notifications.map((note) => (
        <NotificationCard key={note.id} {...note} />
      ))}
    </div>
  );
};

export default NotificationPanel;
