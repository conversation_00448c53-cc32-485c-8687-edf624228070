// import React from 'react';

// interface BorderGradientExampleProps {
//   children: React.ReactNode;
//   className?: string;
//   useAlt?: boolean; // Whether to use the alternative implementation
// }

// /**
//  * A component that demonstrates how to use the border-gradient class
//  * 
//  * @param children - The content to display inside the border
//  * @param className - Additional classes to apply
//  * @param useAlt - Whether to use the alternative implementation (better for some browsers)
//  */
// const BorderGradientExample: React.FC<BorderGradientExampleProps> = ({ 
//   children, 
//   className = "", 
//   useAlt = false 
// }) => {
//   // Use the alternative implementation if specified
//   const gradientClass = useAlt ? 'border-gradient-alt' : 'border-gradient';
  
//   return (
//     <div className={`${gradientClass} ${className}`}>
//       {children}
//     </div>
//   );
// };

// export default BorderGradientExample;
