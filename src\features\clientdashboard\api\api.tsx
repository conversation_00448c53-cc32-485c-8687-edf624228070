import { customAxios } from "@/utils/axio-interceptor";

export const fetchAllAdmins = async () => {
  try {
    const response = await customAxios.get(`/v1/admin/get-all-admin`);
    if (response.data?.status === 1) {
      return response.data.data; // returns array of admins
    } else {
      return [];
    }
  } catch (error) {
    console.error("Error fetching admins:", error);
    return [];
  }
};

// Create Task API
export const createTaskApi = async (taskData: any) => {
  try {
    const response = await customAxios.post(
      "/v1/self-tasks/create-task",
      taskData
    ); // Replace with your actual endpoint
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getTasks = async () => {
  try {
    const response = await customAxios.get("/v1/self-tasks/get-all-task");
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(
      `/v1/self-tasks/get-single-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateTaskStatus = async (taskId: string, taskData: any) => {
  try {
    const response = await customAxios.patch(
      `/v1/self-tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/self-tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};

// src/types.ts
export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  status: string;
  createdAt?: string;
  priority?: string;
  name?: string; // Some APIs might return name instead of title
};

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};

//topsection dashboard client
export const getClientDashboardData = async () => {
  try {
    const response = await customAxios.get("/v1/dashboard/get-dashboard-data?");
    return response.data;
  } catch (error) {
    console.error("Error in getClientDashboardData:", error);
    throw error;
  }
};

export const createSubscription = async (data: any) => {
  try {
    const response = await customAxios.patch("/v1/clients/details", data);
    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error("Error creating subscription:", error);
    throw error;
  }
};



// annonator table histor show and download 
export const annonatorHistory = async (id: string) => {
  try {
    const response = await customAxios.get(`/v1/attendance/history/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching attendance history:", error);
    throw error;
  }
};
