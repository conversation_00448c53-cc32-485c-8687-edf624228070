import img1 from "@/assets/clients/Ellipse.png";
import RemovePage from "./componets/remove-page";
import { useState } from "react";
import CustomToast from "@/_components/common/customtoast";
interface AnnotatorProps {
  name: string;
  email: string;
  role: string;
  projects: string;
  joiningDate: string;
  image: string;
}

// Fake Data for Testing
const annotators: AnnotatorProps[] = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "01",
    joiningDate: "23 March 2025",
    image: img1,
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "02",
    joiningDate: "15 February 2025",
    image: img1,
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "03",
    joiningDate: "10 January 2025",
    image: img1,
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "05",
    joiningDate: "05 March 2025",
    image: img1,
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "06",
    joiningDate: "23 March 2025",
    image: img1,
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    projects: "07",
    joiningDate: "15 February 2025",
    image: img1,
  },
  {
    name: "Liam Johnson",
    email: "<EMAIL>",
    role: "Editor",
    projects: "08",
    joiningDate: "10 January 2025",
    image: img1,
  },
  {
    name: "Emily Davis",
    email: "<EMAIL>",
    role: "Editor",
    projects: "09",
    joiningDate: "05 March 2025",
    image: img1,
  },
];

export default function Coworkers() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState<number | null>(null);
  const [toasts, setToasts] = useState<any[]>([]);

  const showToast = (
    title: string,
    message: string,
    type?: "success" | "error" | "info"
  ) => {
    const id = Date.now();
    setToasts((prev) => [...prev, { id, title, message, type }]);
  };

  const handleCloseToast = (id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  const handleOpenModal = (name: string) => {
    setSelectedUser(name);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  const handleRemove = () => {
    setIsModalOpen(false);
    // Toast will be shown inside RemoveModal via showToast prop
  };

  const handleAccessChange = (name: string, access: string) => {
    setDropdownOpen(null);
    showToast("Role Updated", `${name}'s role changed to ${access}`, "info");
  };
  if (selectedUser !== null) {
    console.log(`Selected user: ${selectedUser}`);
  }

  return (
    <div className="relative">
      <div className="grid grid-cols-4 gap-4">
        {annotators.map((annotator, index) => (
          <div
            key={index}
            className="border border-[#FF577F] p-2 rounded-lg shadow-md bg-white"
          >
            <div className="flex gap-2 items-center">
              <img src={annotator.image} className="w-12 h-12 rounded-full" />
              <div>
                <h3 className="text-sm font-semibold">{annotator.name}</h3>
                <p className="text-xs text-[#5B5B5B]">{annotator.email}</p>
              </div>
            </div>
            <div className="mt-2 text-xs flex flex-col gap-2">
              <p>
                <strong>Role:</strong> {annotator.role}
              </p>
              <p>
                <strong>Projects:</strong> {annotator.projects}
              </p>
              <p>
                <strong>Date of joining:</strong> {annotator.joiningDate}
              </p>
            </div>
            <div className="flex gap-3 mt-2">
              <div className="relative">
                <button
                  className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]  px-[10px] py-[6px]  text-white text-xs rounded-[8px]"
                  onClick={() =>
                    setDropdownOpen(dropdownOpen === index ? null : index)
                  }
                >
                  Change Access
                </button>
                {dropdownOpen === index && (
                  <div className="absolute left-0 mt-1 w-24 bg-white border rounded shadow-lg z-50">
                    <button
                      onClick={() => handleAccessChange(annotator.name, "Edit")}
                      className="block w-full px-4 py-2 text-sm hover:bg-gray-200"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleAccessChange(annotator.name, "View")}
                      className="block w-full px-4 py-2 text-sm hover:bg-gray-200"
                    >
                      View
                    </button>
                  </div>
                )}
              </div>
              <button
                className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]  px-[10px] py-[6px] text-white text-xs rounded-[8px]"
                onClick={() => handleOpenModal(annotator.name)}
              >
                Remove Access
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Toast Container */}
      <div className="fixed top-4 right-4 space-y-2 z-[9999]">
        {toasts.map((toast) => (
          <CustomToast
            key={toast.id}
            title={toast.title}
            message={toast.message}
            type={toast.type}
            onClose={() => handleCloseToast(toast.id)}
          />
        ))}
      </div>

      <RemovePage
        open={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleRemove}
        showToast={showToast}
      />
    </div>
  );
}
