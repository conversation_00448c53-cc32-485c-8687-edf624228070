import { useRef, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import { activateUser } from "../../api/onboarding.api";

interface ReactiveModalProps {
  userId: string;
  onSuccess?: () => void; // Optional callback for additional success handling
}

const ReactiveModal = ({ userId, onSuccess }: ReactiveModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const closeRef = useRef<HTMLButtonElement>(null);
  const queryClient = useQueryClient();

  const handleReactivate = async () => {
    try {
      setIsLoading(true);
      await activateUser(userId);

      toast.success("User reactivated successfully", {
        position: "top-right",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });

      // Programmatically close the dialog
      closeRef.current?.click();

      // Invalidate the query to refetch the table data
      queryClient.invalidateQueries({ queryKey: ["users"] });

      // Call optional onSuccess callback for additional handling
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      console.error("Error reactivating admin:", err);
      toast.error(err.response?.data?.message || "Failed to reactivate admin", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Re-activate Account
        </Button>
      </DialogTrigger>
      <DialogContent className="flex flex-col gap-5 items-center justify-center">
        <DialogHeader className="flex flex-row justify-center">
          <DialogTitle className="text-xl text-center text-[#282828]">
            Do you want to reactivate this account?
          </DialogTitle>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="ghost" className="border-gradient px-14 py-6">
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant="gradient"
            className="px-14 py-6"
            disabled={isLoading}
            onClick={handleReactivate}
          >
            {isLoading ? "Processing..." : "Reactivate"}
          </Button>
          <DialogClose asChild>
            <button ref={closeRef} className="hidden" />
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReactiveModal;