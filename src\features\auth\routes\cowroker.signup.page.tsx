// import React, { useState } from "react";
// import { <PERSON>, EyeOff, MoveRight } from "lucide-react";
// import { Link, useNavigate } from "react-router-dom";
// import { CoWorkerInviteAccept } from "@/features/auth/api/client-api";
// import ReCAPTCHAComponent from "./common/ReCAPTCHA";

// interface SignupFormData {
//   firstName: string;
//   lastName: string;
//   email: string;
//   password: string;
//   confirmPassword: string;
//   agreePolicy: boolean;
//   receiveMarketing: boolean;
// }

// const CoWrokerSignUp: React.FC = () => {
//   const navigate = useNavigate();
//   const [formData, setFormData] = useState<SignupFormData>({
//     firstName: "",
//     lastName: "",
//     email: "",
//     password: "",
//     confirmPassword: "",
//     agreePolicy: false,
//     receiveMarketing: false,
//   });

//   const [errors, setErrors] = useState<{ [key: string]: string }>({});
//   const [showPassword, setShowPassword] = useState(false);
//   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
//   const [isSubmitting, setIsSubmitting] = useState(false);

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
//     const { name, value, type, checked } = e.target;
//     setFormData((prevData) => ({
//       ...prevData,
//       [name]: type === "checkbox" ? checked : value,
//     }));

//     // Clear error when user starts typing
//     if (errors[name]) {
//       setErrors((prev) => {
//         const newErrors = { ...prev };
//         delete newErrors[name];
//         return newErrors;
//       });
//     }
//   };

//   const validate = (): boolean => {
//     const newErrors: { [key: string]: string } = {};
//     const businessEmailPattern =
//       /^[a-zA-Z0-9._%+-]+@(?!gmail\.com$|yahoo\.com$|hotmail\.com$|outlook\.com$)[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
//     const passwordPattern =
//       /^(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

//     if (!formData.firstName.trim())
//       newErrors.firstName = "First name is required.";
//     if (!formData.lastName.trim())
//       newErrors.lastName = "Last name is required.";
//     if (!formData.email.trim()) {
//       newErrors.email = "Email is required.";
//     } else if (!businessEmailPattern.test(formData.email)) {
//       newErrors.email = "Only business emails are allowed.";
//     }
//     if (!formData.password) {
//       newErrors.password = "Password is required.";
//     } else if (!passwordPattern.test(formData.password)) {
//       newErrors.password =
//         "Password must be at least 8 characters, include 1 uppercase, 1 number, and 1 special character.";
//     }
//     if (formData.confirmPassword !== formData.password) {
//       newErrors.confirmPassword = "Passwords do not match.";
//     }
//     if (!formData.agreePolicy) {
//       newErrors.agreePolicy = "You must agree to the privacy policy and terms.";
//     }
//     if (!formData.receiveMarketing) {
//       newErrors.receiveMarketing =
//         "You must agree to receive marketing communication from Macgence";
//     }

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
//     e.preventDefault();
//     setIsSubmitting(true);
//     console.log("Form data:", formData);

//     if (!validate()) {
//       console.log("Validation failed", errors);
//       setIsSubmitting(false);
//       console.groupEnd();
//       return;
//     }

//     try {
//       const name = `${formData.firstName} ${formData.lastName}`;

//       const response = await CoWorkerInviteAccept(
//         formData.email,
//         formData.password,
//         name,
//         token
//       );

//       console.log("✅ Signup API response:", response);

//       // Store email for OTP verification
//       localStorage.setItem("emailForOTPVerification", formData.email);

//       navigate("/auth/login");
//     } catch (error: any) {
//       console.error("❌ Signup error details:", {
//         message: error.message,
//         stack: error.stack,
//       });

//       setErrors({
//         ...errors,
//         apiError: error.message,
//       });

//       // Check if navigation is available
//       console.log("Navigate function exists:", !!navigate);
//     } finally {
//       setIsSubmitting(false);
//       console.groupEnd();
//     }
//   };

//   return (
//     <div className="w-full flex items-center justify-center">
//       <div className="mx-auto min-h-[350px] shadow-[0px_3px_48px_10px_#0000000F] px-6 py-3 rounded-2xl">
//         <h2 className="text-[24px] font-bold font-inter text-[#282828] mb-2 mt-2">
//           CoWorker Sign Up
//         </h2>

//         {errors.apiError && (
//           <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
//             {errors.apiError}
//           </div>
//         )}

//         <form onSubmit={handleSubmit} className="space-y-3 w-full">
//           {/* First and Last Name */}
//           <div className="flex gap-4">
//             <div className="w-1/2">
//               <label className="block text-[14px] font-medium text-[#757575] mb-1">
//                 First Name*
//               </label>
//               <input
//                 type="text"
//                 name="firstName"
//                 value={formData.firstName}
//                 onChange={handleChange}
//                 className={`w-full p-2 border bg-[#F9EFEF] rounded-lg ${
//                   errors.firstName ? "border-red-500" : ""
//                 }`}
//                 placeholder="First Name"
//               />
//               {errors.firstName && (
//                 <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
//               )}
//             </div>
//             <div className="w-1/2">
//               <label className="block text-[14px] font-medium text-[#757575] mb-1">
//                 Last Name*
//               </label>
//               <input
//                 type="text"
//                 name="lastName"
//                 value={formData.lastName}
//                 onChange={handleChange}
//                 className={`w-full p-2 border bg-[#F9EFEF] rounded-lg ${
//                   errors.lastName ? "border-red-500" : ""
//                 }`}
//                 placeholder="Last Name"
//               />
//               {errors.lastName && (
//                 <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
//               )}
//             </div>
//           </div>

//           {/* Email */}
//           <div>
//             <label className="block text-[14px] font-medium text-[#757575] mb-1">
//               Email*
//             </label>
//             <input
//               type="email"
//               name="email"
//               value={formData.email}
//               onChange={handleChange}
//               className={`w-full p-2 border bg-[#F9EFEF] rounded-lg ${
//                 errors.email ? "border-red-500" : ""
//               }`}
//               placeholder="Enter your business email"
//             />
//             {errors.email && (
//               <p className="text-red-500 text-sm mt-1">{errors.email}</p>
//             )}
//           </div>

//           {/* Password & Confirm Password */}
//           <div className="flex gap-2">
//             <div className="w-1/2">
//               <label className="text-[14px] font-medium text-[#757575]">
//                 Password*
//               </label>
//               <div className="relative">
//                 <input
//                   type={showPassword ? "text" : "password"}
//                   name="password"
//                   value={formData.password}
//                   onChange={handleChange}
//                   className={`w-full p-2 pr-10 border bg-[#F9EFEF] rounded-lg ${
//                     errors.password ? "border-red-500" : ""
//                   }`}
//                   placeholder="Enter your password"
//                 />
//                 <div
//                   className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
//                   onClick={() => setShowPassword(!showPassword)}
//                 >
//                   {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
//                 </div>
//               </div>
//               {errors.password && (
//                 <p className="text-red-500 text-sm mt-1">{errors.password}</p>
//               )}
//             </div>

//             <div className="w-1/2">
//               <label className="block text-[14px] font-medium text-[#757575] mb-1">
//                 Confirm Password*
//               </label>
//               <div className="relative">
//                 <input
//                   type={showConfirmPassword ? "text" : "password"}
//                   name="confirmPassword"
//                   value={formData.confirmPassword}
//                   onChange={handleChange}
//                   className={`w-full p-2 pr-10 bg-[#F9EFEF] rounded-lg ${
//                     errors.confirmPassword ? "border-red-500" : ""
//                   }`}
//                   placeholder="Confirm your password"
//                 />
//                 <div
//                   className="absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer text-[#757575]"
//                   onClick={() => setShowConfirmPassword(!showConfirmPassword)}
//                 >
//                   {showConfirmPassword ? (
//                     <EyeOff size={18} />
//                   ) : (
//                     <Eye size={18} />
//                   )}
//                 </div>
//               </div>
//               {errors.confirmPassword && (
//                 <p className="text-red-500 text-sm mt-1">
//                   {errors.confirmPassword}
//                 </p>
//               )}
//             </div>
//           </div>

//           {/* Checkboxes */}
//           <div className="space-y-2">
//             <div className="flex items-start gap-2">
//               <input
//                 type="checkbox"
//                 name="agreePolicy"
//                 checked={formData.agreePolicy}
//                 onChange={handleChange}
//                 className={`mt-1 ${errors.agreePolicy ? "border-red-500" : ""}`}
//               />
//               <span className="text-sm text-gray-600">
//                 I agree with Macgence{" "}
//                 <a href="#" className="text-blue-500 underline">
//                   Privacy Policy
//                 </a>{" "}
//                 and{" "}
//                 <a href="#" className="text-blue-500 underline">
//                   Terms of Service
//                 </a>
//               </span>
//             </div>
//             {errors.agreePolicy && (
//               <p className="text-red-500 text-sm ml-6 -mt-2">
//                 {errors.agreePolicy}
//               </p>
//             )}

//             <div className="flex items-start gap-2">
//               <input
//                 type="checkbox"
//                 name="receiveMarketing"
//                 checked={formData.receiveMarketing}
//                 onChange={handleChange}
//                 className={`mt-1 ${
//                   errors.receiveMarketing ? "border-red-500" : ""
//                 }`}
//               />
//               <span className="text-sm text-gray-600">
//                 I agree to receive marketing communication from Macgence.
//               </span>
//             </div>
//             {errors.receiveMarketing && (
//               <p className="text-red-500 text-sm ml-6 -mt-2">
//                 {errors.receiveMarketing}
//               </p>
//             )}
//           </div>

//           {/* ReCAPTCHA */}
//           <ReCAPTCHAComponent />

//           {/* Submit Button */}
//           <div className="flex flex-row items-center">
//             <button
//               type="submit"
//               disabled={isSubmitting}
//               className={`px-10 py-3 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all flex items-center justify-center ${
//                 isSubmitting ? "opacity-70 cursor-not-allowed" : ""
//               }`}
//             >
//               {isSubmitting ? (
//                 "Processing..."
//               ) : (
//                 <>
//                   Sign Up <MoveRight className="ml-2 w-4 h-4" />
//                 </>
//               )}
//             </button>
//           </div>
//         </form>

//         {/* Login Link */}
//         <p className="text-center mt-3 text-sm text-gray-600">
//           Already have an account?{" "}
//           <Link
//             to="/auth/login"
//             className="text-red-500 font-semibold hover:text-blue-600"
//           >
//             Log in
//           </Link>
//         </p>
//       </div>
//     </div>
//   );
// };

// export default CoWrokerSignUp;
