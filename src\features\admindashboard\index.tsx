import { Route, Routes } from "react-router-dom";
// import Attendacelog from "./attendance.log";
import AdminDashboard from "./components/admindashboard";
import SettingFile from "@/features/settings/setting-file";
import AdminFAQs from "./components/adminfaqs";
import AdminTaskDetails from "./detailsadmin/AdminTaskDetails";
// import AdminAttendance from "./detailsadmin/annonatoradmin/attendance.log";
import AdminCoodinator from "./detailsadmin/coodinatoradmin/components/attendance.log";
import AdminClients from "./detailsadmin/clientadmin/components/attendance.log";
import AdminOnboard from "./onboard/attendance.log";
import MatchMakingmain from "./matchmaking";
import UserAllList from "./userall";
import AdminsList from "./admin-list";
import UserDetails from "./userall/components/formuserdetails/userdetails";
import AdminNotification from "./adminnotification";
// import Subscription from "./billing/subscription";

export const AdminDashboardPage = () => {
  return (
    <Routes>
      <Route path="*" element={<AdminDashboard />} />

      {/* <Route path="/attendance" element={<Attendacelog />} />
      <Route path="/billing/subscription" element={<Subscription />} /> */}

      {/* setting support faqs */}
      <Route path="/adminsettings" element={<SettingFile />} />
      <Route path="/faqsadmin" element={<AdminFAQs />} />
      <Route path="/admindetails" element={<AdminTaskDetails />} />
      <Route path="/userdetails" element={<UserDetails />} />

      {/* <Route path="/adminattendance" element={<AdminAttendance/>} /> */}
      <Route path="/admincoodinator" element={<AdminCoodinator />} />
      <Route path="/adminclients" element={<AdminClients />} />
      <Route path="/onboard" element={<AdminOnboard />} />
      <Route path="/notifications" element={<AdminNotification />} />
      <Route path="/match-making" element={<MatchMakingmain />} />
      <Route path="/total-users" element={<UserAllList />} />
      <Route path="/admins-list" element={<AdminsList />} />
    </Routes>
  );
};
