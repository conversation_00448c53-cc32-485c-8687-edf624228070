import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive project create form styles
export const useProjectCreateStyles = () => {
  const { isLaptopMd } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Get styles based on screen size
  const getStyles = () => {
    // 1024px
    if (windowWidth <= 1024) {
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50",
        formContainer: "bg-white p-5 rounded-lg shadow-lg w-[480px] max-h-[85vh] overflow-y-auto",
        heading: "text-[18px] font-semibold mb-3",
        formSpacing: "space-y-3",

        // Form fields
        label: "block text-[13px] text-[#282828] font-medium",
        inputContainer: "border-gradient",
        input: "w-full p-2 bg-[#F9EFEF] placeholder:text-[11px] placeholder:text-[#5E5E5E] text-[12px] rounded-lg outline-none",

        // Textarea
        textareaContainer: "border-gradient rounded-[8px] w-full",
        textareaInner: "bg-[#F9EFEF] rounded-[6px] p-1",
        textarea: "w-full p-2 placeholder:text-[#5E5E5E] text-[12px] bg-transparent rounded-md h-16 focus:outline-none placeholder:text-[11px]",

        // Select fields
        flexContainer: "flex flex-row gap-x-4 justify-between items-start",
        selectContainer: "border-gradient rounded-lg w-[12rem]",
        select: "w-full p-2.5 bg-[#F9EFEF] text-[#5E5E5E] text-[11px] rounded-lg outline-none",

        // Date fields
        dateContainer: "border-gradient rounded-lg w-[12rem]",
        dateInput: "w-full p-2 bg-[#F9EFEF] text-[#5E5E5E] text-[11px] rounded-lg outline-none",

        // Upload
        uploadContainer: "relative w-full border-gradient rounded-lg",
        uploadInner: "flex justify-center items-center gap-2 p-1.5 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-4 h-4 text-gray-600",
        uploadText: "text-xs text-gray-700",
        uploadInput: "absolute inset-0 w-full h-full opacity-0 cursor-pointer",

        // Help text
        helpContainer: "text-[11px] text-[#424242] flex flex-row gap-x-1 items-center justify-start",
        helpIcon: "w-3.5 h-3.5 text-gray-600",

        // Buttons
        buttonContainer: "flex justify-end items-center gap-2",
        cancelButton: "px-8 py-1.5 border-gradient rounded-lg text-sm",
        submitButton: "px-8 py-1.5 bg-gradient-to-r from-[#E91C24] to-[#45ADE2] text-white rounded-lg text-sm",

        // Error
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-3 py-1.5 rounded-md mb-3 text-xs",
      };
    }
    // 1440px
    else if (isLaptopMd) {
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50",
        formContainer: "bg-white p-6 rounded-lg shadow-lg w-[500px] max-h-[90vh] overflow-y-auto",
        heading: "text-[20px] font-semibold mb-4",
        formSpacing: "space-y-4",

        // Form fields
        label: "block text-[14px] text-[#282828] font-medium",
        inputContainer: "border-gradient",
        input: "w-full p-2 bg-[#F9EFEF] placeholder:text-[12px] placeholder:text-[#5E5E5E] text-[13px] rounded-lg outline-none",

        // Textarea
        textareaContainer: "border-gradient rounded-[8px] w-full",
        textareaInner: "bg-[#F9EFEF] rounded-[6px] p-1",
        textarea: "w-full p-2 placeholder:text-[#5E5E5E] text-[13px] bg-transparent rounded-md h-20 focus:outline-none placeholder:text-[12px]",

        // Select fields
        flexContainer: "flex flex-row gap-x-6 justify-between items-start",
        selectContainer: "border-gradient rounded-lg w-[13rem]",
        select: "w-full p-3 bg-[#F9EFEF] text-[#5E5E5E] text-[12px] rounded-lg outline-none",

        // Date fields
        dateContainer: "border-gradient rounded-lg w-[13rem]",
        dateInput: "w-full p-2 bg-[#F9EFEF] text-[#5E5E5E] text-[12px] rounded-lg outline-none",

        // Upload
        uploadContainer: "relative w-full border-gradient rounded-lg",
        uploadInner: "flex justify-center items-center gap-2 p-2 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-5 h-5 text-gray-600",
        uploadText: "text-sm text-gray-700",
        uploadInput: "absolute inset-0 w-full h-full opacity-0 cursor-pointer",

        // Help text
        helpContainer: "text-[12px] text-[#424242] flex flex-row gap-x-1 items-center justify-start",
        helpIcon: "w-4 h-4 text-gray-600",

        // Buttons
        buttonContainer: "flex justify-end items-center gap-3",
        cancelButton: "px-10 py-2 border-gradient rounded-lg text-sm",
        submitButton: "px-10 py-2 bg-gradient-to-r from-[#E91C24] to-[#45ADE2] text-white rounded-lg text-sm",

        // Error
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md mb-4 text-sm",
      };
    }
    // 2560px (4K)
    else {
      return {
        modalContainer: "fixed inset-0 flex items-center justify-center bg-black bg-opacity-50",
        formContainer: "bg-white p-7 rounded-lg shadow-lg w-[600px] max-h-[90vh] overflow-y-auto",
        heading: "text-[24px] font-semibold mb-5",
        formSpacing: "space-y-5",

        // Form fields
        label: "block text-[16px] text-[#282828] font-medium",
        inputContainer: "border-gradient",
        input: "w-full p-3 bg-[#F9EFEF] placeholder:text-[13px] placeholder:text-[#5E5E5E] text-[14px] rounded-lg outline-none",

        // Textarea
        textareaContainer: "border-gradient rounded-[8px] w-full",
        textareaInner: "bg-[#F9EFEF] rounded-[6px] p-1",
        textarea: "w-full p-3 placeholder:text-[#5E5E5E] text-[14px] bg-transparent rounded-md h-24 focus:outline-none placeholder:text-[13px]",

        // Select fields
        flexContainer: "flex flex-row gap-x-8 justify-between items-start",
        selectContainer: "border-gradient rounded-lg w-[15rem]",
        select: "w-full p-3.5 bg-[#F9EFEF] text-[#5E5E5E] text-[13px] rounded-lg outline-none",

        // Date fields
        dateContainer: "border-gradient rounded-lg w-[15rem]",
        dateInput: "w-full p-3 bg-[#F9EFEF] text-[#5E5E5E] text-[13px] rounded-lg outline-none",

        // Upload
        uploadContainer: "relative w-full border-gradient rounded-lg",
        uploadInner: "flex justify-center items-center gap-2 p-3 bg-[#F9EFEF] rounded-lg",
        uploadIcon: "w-6 h-6 text-gray-600",
        uploadText: "text-base text-gray-700",
        uploadInput: "absolute inset-0 w-full h-full opacity-0 cursor-pointer",

        // Help text
        helpContainer: "text-[14px] text-[#424242] flex flex-row gap-x-2 items-center justify-start",
        helpIcon: "w-5 h-5 text-gray-600",

        // Buttons
        buttonContainer: "flex justify-end items-center gap-4",
        cancelButton: "px-12 py-2.5 border-gradient rounded-lg text-base",
        submitButton: "px-12 py-2.5 bg-gradient-to-r from-[#E91C24] to-[#45ADE2] text-white rounded-lg text-base",

        // Error
        errorContainer: "bg-red-50 border border-red-200 text-red-600 px-5 py-2.5 rounded-md mb-5 text-base",
      };
    }
  };

  return getStyles();
};
