// @ts-ignore
import { customAxios } from '@/utils/axio-interceptor'; // Adjust the import path

export const FirstSubscriptionApi = async (userId: string) => {
  try {
    // Step 1: Fetch all subscriptions
    const subscriptionResponse = await customAxios.get('/v1/matchmaking/get');
    const subscriptions = subscriptionResponse.data.data.data;

    // Step 2: Filter subscriptions by userId and valid subscriptionId, then sort by startDate
    const userSubscriptions = subscriptions
      .filter(
        (sub: any) => sub.id === userId && sub.subscriptionId !== null
      )
      .sort((a: any, b: any) => 
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
      );

    // Step 3: Get the first subscription
    const firstSubscription = userSubscriptions[0];
    if (!firstSubscription) {
      throw new Error('No valid subscription found for this user');
    }

    // Step 4: Fetch subscription details using subscriptionId
    const detailsResponse = await customAxios.get(
      `/v1/matchmaking/details/${firstSubscription.subscriptionId}`
    );
    const subscriptionDetails = detailsResponse.data.getDetails;

    // Step 5: Fetch package details to get package name
    const packageResponse = await customAxios.get('/v1/packages/get');
    const packages = packageResponse.data.data.packages;
    const packageData = packages.find(
      (pkg: any) => pkg.id === subscriptionDetails.packageId
    );

    // Step 6: Combine and format the data
    const shiftTiming = `${subscriptionDetails.availableFrom} - ${subscriptionDetails.availableTo}`;
    const createdAt = new Date(subscriptionDetails.createdAt).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });

    return {
      packageName: packageData?.name || 'No data',
      shiftTiming,
      timezone: subscriptionDetails.timezone || 'No data',
      industry: subscriptionDetails.industry || 'No data',
      category: subscriptionDetails.category || 'No data',
      startOn: subscriptionDetails.startOn
        ? new Date(subscriptionDetails.startOn).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          })
        : 'No data',
      description: subscriptionDetails.description || 'No data',
      createdAt
    };
  } catch (error) {
    console.error('Error fetching first subscription:', error);
    throw error;
  }
};