import { customAxios } from "@/utils/axio-interceptor";

export const AnnotatorLeaveApi = {
  requestLeave: async (leaveData: {
    startDate: string;
    endDate: string;
    reason: string;
  }) => {
    try {
      const response = await customAxios.post("/v1/leave/request", leaveData);
      return response.data;
    } catch (error) {
      throw new Error("Failed to submit leave request");
    }
  },
};




//annonator ki ropfile details availabe to and avalilable from yaha se pick karega and other data bi

export const AnnonatorDetailsApi = async () => {
  try {
    const response = await customAxios.get("/v1/users/profile");
    if (response.data.status === 1) {
      return response.data.data; // Return profile data (contains availableFrom, availableTo, etc.)
    } else {
      throw new Error(response.data.message || "Failed to fetch profile");
    }
  } catch (error) {
    console.error("Error fetching profile:", error);
    throw error;
  }
};