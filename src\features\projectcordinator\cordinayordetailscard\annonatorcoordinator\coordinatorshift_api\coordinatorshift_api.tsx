import { customAxios } from "@/utils/axio-interceptor";



export const coordinatorChangeShift = async (data: any) => {
  const response = await customAxios.post(
    `/v1/shift/create-shift-change`,
    {
      annotatorId: data.annotatorId,
      newFrom: data.availableFrom,
      newTo: data.availableTo,
      // description: data.note // Will be added later when backend supports it
    }
  );
  return response.data;
};
