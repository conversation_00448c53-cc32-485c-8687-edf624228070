// /pages/DashboardPage.tsx
import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import ProjectKanban from "./projectkanban";
import TaskRecord from "./componentsproejctdetails/taskrecord";
import AnnotatorDetails from "./componentsproejctdetails/annotatordetails";
import CoworkerDetails from "./componentsproejctdetails/coworkerdetails";
import { getProjectDetails } from "./projectdetails_api/projectdetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { NoData } from "@/_components/common";
import { baseUrl } from "@/globalurl/baseurl";
import { FaEdit } from "react-icons/fa";
import ClientProjectUpdate from "./projectdetails_api/ClientProjectUpdate";

// Updated interface to match ClientProjectUpdate expectations
interface ProjectData {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  attachment: string[]; // Changed from string[] | null to string[]
  createdBy: {
    id: string;
    name: string;
  };
  annotators?: {
    // Changed from annotator to annotators (array)
    id: string;
    name: string;
    email: string;
  }[];
  tasks: any[];
}

const ProjectDetails: React.FC = () => {
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, "0");
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const year = String(date.getFullYear()).slice(-2);
      return `${day}/${month}/${year}`;
    } catch (error) {
      return "Invalid date";
    }
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return "N/A";
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const weeks = Math.floor(diffDays / 7);
      const days = diffDays % 7;
      let result = [];
      if (weeks > 0) {
        result.push(`${weeks} ${weeks === 1 ? "Week" : "Weeks"}`);
      }
      if (days > 0) {
        result.push(`${days} ${days === 1 ? "Day" : "Days"}`);
      }
      return result.length > 0 ? result.join(" and ") : "0 Days";
    } catch (error) {
      return "N/A";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toUpperCase()) {
      case "HIGH":
        return "bg-red-500";
      case "MEDIUM":
        return "bg-orange-500";
      case "LOW":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case "COMPLETED":
        return "bg-[#009A51]";
      case "PENDING":
        return "bg-yellow-500";
      case "IN_PROGRESS":
      case "INPROGRESS":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const formatStatus = (status: string) => {
    if (!status) return "Unknown";
    return status
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const handleDownloadAttachment = (attachmentPath: string) => {
    if (!attachmentPath) return;

    // Check if attachmentPath is an absolute URL (starts with http:// or https://)
    const isAbsoluteUrl = /^https?:\/\//i.test(attachmentPath);
    let attachmentUrl = attachmentPath;

    // If it's not an absolute URL, prepend baseUrl
    if (!isAbsoluteUrl) {
      const formattedBaseUrl = baseUrl.endsWith("/")
        ? baseUrl.slice(0, -1)
        : baseUrl;
      const formattedPath = attachmentPath.startsWith("/")
        ? attachmentPath
        : `/${attachmentPath}`;
      attachmentUrl = `${formattedBaseUrl}${formattedPath}`;
    }

    console.log("Downloading attachment from URL:", attachmentUrl);

    const link = document.createElement("a");
    link.href = attachmentUrl;
    link.target = "_blank";
    link.rel = "noopener noreferrer";
    const fileName = attachmentPath.split("/").pop() || "attachment";
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const refreshProjectDetails = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams(location.search);
      const projectId = queryParams.get("id");
      if (!projectId) {
        setError("Project ID is missing. Please go back and select a project.");
        return;
      }
      const response = await getProjectDetails(projectId);
      if (response && response.status === 1 && response.data) {
        setProject(response.data);
      } else {
        setError("Failed to load project details. Please try again later.");
      }
    } catch (error) {
      setError("An error occurred while loading project details.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get("id");
        if (!projectId) {
          console.error("No project ID provided in URL");
          setError(
            "Project ID is missing. Please go back and select a project."
          );
          setLoading(false);
          return;
        }
        const response = await getProjectDetails(projectId);
        if (response && response.status === 1 && response.data) {
          console.log("Project details:", response.data);
          setProject(response.data);
        } else {
          console.error("Invalid API response format:", response);
          setError("Failed to load project details. Please try again later.");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        setError("An error occurred while loading project details.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [location.search]);

  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  if (error) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message={error} />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <NoData message="No project details available" />
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col px-2 lg:px-4 xl:px-6 2xl:px-8">
      <div className="w-full">
        <div className="flex flex-col lg:flex-col xl:flex-row 2xl:flex-row justify-start xl:justify-between 2xl:justify-between gap-4 lg:gap-6 xl:gap-8 2xl:gap-10 items-start">
          <div className="flex flex-col gap-4 lg:gap-5 xl:gap-6 2xl:gap-8 w-full xl:w-3/4 2xl:w-3/4">
            <div className="flex flex-wrap items-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5">
              <h1 className="text-[#122539] font-poppins font-semibold text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl">
                {project.name}
              </h1>
              <span
                className={`text-white text-xs lg:text-sm xl:text-base 2xl:text-lg px-2 py-1 rounded-full ${getPriorityColor(
                  project.priority
                )}`}
              >
                {project.priority}
              </span>
              <span
                className={`text-white text-xs lg:text-sm xl:text-base 2xl:text-lg px-[8px] py-1 rounded-full ${getStatusColor(
                  project.status
                )}`}
              >
                {formatStatus(project.status)}
              </span>
              {/* Updated to handle null check for project */}
              {project && (
                <div
                  className="text-xl hover:text-blue-500 cursor-pointer mb-6"
                  onClick={() => setIsUpdateModalOpen(true)}
                >
                  <FaEdit />
                </div>
              )}
            </div>

            <div className="w-full">
              <h1 className="text-[#122539]   font-sans font-normal text-sm lg:text-sm xl:text-sm 2xl:text-xl break-words whitespace-normal">
                {project.description}
              </h1>
            </div>

            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-2 lg:gap-0">
              <div className="flex gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  Duration
                </h1>
                <p className="text-[#5E5E5E] font-normal text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  {calculateDuration(project.startDate, project.dueDate)}
                </p>
              </div>
              <div className="flex gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  Start Date
                </h1>
                <p className="text-[#5E5E5E] font-normal text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  {formatDate(project.startDate)}
                </p>
              </div>
              <div className="flex gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 items-center">
                <h1 className="text-[#122539] font-medium text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  End Date
                </h1>
                <p className="text-[#5E5E5E] font-normal text-xs lg:text-sm xl:text-base 2xl:text-lg">
                  {formatDate(project.dueDate)}
                </p>
              </div>
            </div>

            <div className="w-full mt-2 lg:mt-3 xl:mt-4 2xl:mt-5 lg:h-auto xl:h-[calc(100vh-250px)] 2xl:h-[calc(100vh-250px)]">
              <ProjectKanban projectId={project.id} />
            </div>
          </div>

          <div className="w-full xl:w-[30%] 2xl:w-1/4 flex flex-col gap-4 lg:gap-5 xl:gap-6 2xl:gap-8 lg:h-auto xl:h-[calc(100vh-250px)] 2xl:h-[calc(100vh-250px)]">
            <div className="flex flex-col lg:flex-row xl:flex-col 2xl:flex-col gap-3 w-full">
              <button
                onClick={() => navigate("/dashboard/chat")}
                className="w-full lg:flex-1 xl:w-full 2xl:w-full p-2 lg:p-3 xl:p-3 2xl:p-4 text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-md"
              >
                Chat
              </button>
              <button
                className="w-full lg:flex-1 xl:w-full 2xl:w-full p-2 lg:p-3 xl:p-3 2xl:p-4 text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium border-gradient text-[#FF577F] rounded-md"
                onClick={() => {
                  if (project.attachment && project.attachment.length > 0) {
                    handleDownloadAttachment(project.attachment[0]);
                  } else {
                    alert("No attachment available for this project");
                  }
                }}
                disabled={
                  !project.attachment || project.attachment.length === 0
                }
              >
                {project.attachment && project.attachment.length > 0
                  ? "Download Attachment"
                  : "No Attachment"}
              </button>
            </div>
            <div className="w-full grid grid-cols-3 xl:grid-cols-1 2xl:grid-cols-1 gap-4 lg:gap-5 xl:gap-6 2xl:gap-8 flex-grow">
              <TaskRecord projectId={project.id} />
              <AnnotatorDetails />
              <CoworkerDetails />
            </div>
          </div>
        </div>
      </div>

      {isUpdateModalOpen && project && (
        <ClientProjectUpdate
          project={{
            ...project,
            // Ensure annotators is properly formatted
            annotators: project.annotators || [],
            // Ensure attachment is always an array
            attachment: project.attachment || [],
          }}
          onClose={() => setIsUpdateModalOpen(false)}
          onSuccess={() => {
            setIsUpdateModalOpen(false);
            refreshProjectDetails();
          }}
        />
      )}
    </div>
  );
};

export default ProjectDetails;
