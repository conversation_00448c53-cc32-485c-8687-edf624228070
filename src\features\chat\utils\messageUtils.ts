// Message formatting and sorting utilities

export interface Message {
  id?: string;
  text: string;
  time: string;
  self: boolean;
  fileUrl?: string | null;
  fileType?: string | null;
  senderId?: string;
  receiverId?: string;
}

// Format a message from API response to UI format
export const formatMessage = (msg: {
  id?: string;
  text?: string;
  createdAt?: string | number | Date;
  fileUrl?: string | null;
  fileType?: string | null;
  senderId?: string;
  receiverId?: string;
}, userId: string | undefined): Message => ({
  id: msg.id,
  text: msg?.text || "",
  time: new Date(msg.createdAt || Date.now()).toLocaleTimeString(),
  self: msg?.senderId === userId,
  fileUrl: msg.fileUrl,
  fileType: msg.fileType,
  senderId: msg.senderId,
  receiverId: msg.receiverId,
});

// Scroll to the bottom of the message list
export const scrollToBottom = (scrollRef: React.RefObject<HTMLDivElement>) => {
  if (scrollRef.current) {
    setTimeout(() => {
      // Try scrollIntoView first
      scrollRef.current?.scrollIntoView({ behavior: "smooth" });
      
      // Also ensure the parent container scrolls to the bottom
      const messageContainer = scrollRef.current?.parentElement;
      if (messageContainer) {
        messageContainer.scrollTop = messageContainer.scrollHeight;
      }
    }, 100);
  }
};

// Sort messages by timestamp
export function sortMessagesByCreatedAt(messages: Message[]) {
  return [...messages].sort((a, b) => {
    // First try to use the createdAt property if available
    if (a.time && b.time) {
      return new Date(a.time).getTime() - new Date(b.time).getTime();
    }

    const getTimestampFromId = (id: string | undefined) => {
      if (!id) return 0;
      const parts = id.toString().split('-');
      const lastPart = parts[parts.length - 1];
      const timestamp = parseInt(lastPart);
      return isNaN(timestamp) ? 0 : timestamp;
    };
    
    const timeA = getTimestampFromId(a.id);
    const timeB = getTimestampFromId(b.id);
    
    if (timeA && timeB) {
      return timeA - timeB;
    }
    
    // If all else fails, maintain original order
    return 0;
  });
}



