// /pages/DashboardPage.tsx
import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import TaskRecord from "../clientdashboard/task-detail-page/components/projectdetails/componentsproejctdetails/taskrecord";
import ClientsList from "./dashboard/component/clientslist";
import CoworkerList from "./dashboard/component/coworkerlist";
import { getAnnonatorProjectDetails } from "./annonator_api/annonator_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { NoData } from "@/_components/common";
import { baseUrl } from "@/globalurl/baseurl";
// import AnnotatorKanbanBoard from "./annotatordashboard";
import AnnotatorProjectDetailsKanban from "./annotator-project-details/annotator_details_kanban";

// Define project data interface based on the API response
interface ProjectData {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  createdBy: {
    name: string;
  };
  annotator: {
    name: string;
    email: string;
  };
  tasks: any[];
}

const AnnotatorDetails: React.FC = () => {
  const location = useLocation();
  const [project, setProject] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Format date to DD-MM-YYYY
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, "0")}-${(
        date.getMonth() + 1
      )
        .toString()
        .padStart(2, "0")}-${date.getFullYear()}`;
    } catch (e) {
      return "Invalid date";
    }
  };

  // Calculate duration in weeks
  const calculateDuration = (startDate: string, endDate: string) => {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const diffWeeks = Math.ceil(diffDays / 7);
      return `${diffWeeks} ${diffWeeks === 1 ? "Week" : "Weeks"}`;
    } catch (e) {
      return "Unknown duration";
    }
  };

  // Get status badge style
  const getStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case "COMPLETED":
        return "bg-[#009A51]";
      case "IN_PROGRESS":
        return "bg-[#E96B1C]";
      case "PENDING":
        return "bg-[#F59E0B]";
      default:
        return "bg-gray-500";
    }
  };

  // Get priority badge style
  const getPriorityBadge = (priority: string) => {
    switch (priority.toUpperCase()) {
      case "HIGH":
        return "bg-red-500";
      case "MEDIUM":
        return "bg-orange-500";
      case "LOW":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  useEffect(() => {
    const fetchProjectDetails = async () => {
      try {
        setLoading(true);

        // Get project ID from URL query parameters
        const queryParams = new URLSearchParams(location.search);
        const projectId = queryParams.get("id");

        if (!projectId) {
          console.error("No project ID found in URL");
          setError("Project ID is missing");
          setLoading(false);
          return;
        }

        const response = await getAnnonatorProjectDetails(projectId);

        if (response && response.status === 1 && response.data) {
          console.log("Project details:", response.data);
          setProject(response.data);
        } else {
          console.error("Invalid API response format:", response);
          setError("Failed to load project details");
        }
      } catch (err) {
        console.error("Error fetching project details:", err);
        setError("An error occurred while loading project details");
      } finally {
        setLoading(false);
      }
    };

    fetchProjectDetails();
  }, [location.search]);

  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  if (error || !project) {
    return <NoData message={error || "Project details not found"} />;
  }

  return (
    <div className="w-full flex flex-col">
      <div className="lg-only:px-3 xl-only:px-4 2xl-only:px-5">
        <div className="flex flex-col xl-only:flex-row 2xl-only:flex-row justify-between lg-only:gap-4 xl-only:gap-6 2xl-only:gap-8 items-start lg-only:mt-3 xl-only:mt-4 2xl-only:mt-5">
          {/* top section */}
          <div className="flex flex-col lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5 w-full xl-only:w-[75%] 2xl-only:w-[75%]">
            <div className="flex flex-wrap items-center lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3">
              {/*name project*/}
              <h1 className="text-[#122539] font-poppins font-semibold lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl">
                {project.name}
              </h1>
              <span
                className={`text-white lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-1 2xl-only:px-3 2xl-only:py-1.5 rounded-full ${getPriorityBadge(
                  project.priority
                )}`}
              >
                {project.priority}
              </span>
              <span
                className={`text-white lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-1 2xl-only:px-3 2xl-only:py-1.5 rounded-full ${getStatusBadge(
                  project.status
                )}`}
              >
                {project.status}
              </span>
            </div>
            {/* Description */}
            <div className="w-full">
              <h1 className="text-[#122539] font-sans font-normal text-sm lg:text-sm xl:text-sm 2xl:text-xl break-words">
                {project.description}
              </h1>
            </div>

            <div className="flex flex-col lg-only:flex-row xl-only:flex-row 2xl-only:flex-row justify-between items-start lg-only:items-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4">
              <div className="flex lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 items-center">
                {/* duration */}
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  Duration
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  {calculateDuration(project.startDate, project.dueDate)}
                </p>
              </div>
              <div className="flex lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 items-center">
                {/* start date */}
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  Start Date
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  {formatDate(project.startDate)}
                </p>
              </div>
              <div className="flex lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 items-center">
                {/* end date */}
                <h1 className="text-[#122539] font-medium lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  End Date
                </h1>
                <p className="text-[#5E5E5E] font-normal lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                  {formatDate(project.dueDate)}
                </p>
              </div>
            </div>
            {/* <div className="mt-3">
              <AnnotatorKanbanBoard />
            </div> */}
            <div className="mt-3">
              <AnnotatorProjectDetailsKanban projectId={project.id} />
            </div>
          </div>
          {/* <div className="flex flex-col gap-4">
            {project.attachment && project.attachment.length > 0 && (
              <button
                className="w-full p-3 text-sm font-medium border-gradient text-[#FF577F] rounded-md"
                onClick={() =>
                  window.open(`${baseUrl}/${project.attachment[0]}`, "_blank")
                }
              >
                View Attachment
              </button>
            )}
            <button className="w-full p-3 text-sm font-medium border-gradient text-[#FF577F] rounded-md">
              Chat
            </button>
            <div className="mt-3 lg-only:h-[500px] xl-only:h-[calc(100vh-300px)] 2xl-only:h-[calc(100vh-250px)]">
              <ProjectKanban />
            </div>
          </div> */}

          {/* sidebar section - right on xl/2xl, bottom on lg-only */}
          <div className="w-full xl-only:w-[23%] 2xl-only:w-[23%] flex flex-col lg-only:mt-4 lg-only:gap-3 xl-only:gap-4 2xl-only:gap-5">
            <div className="lg-only:flex lg-only:flex-row lg-only:gap-3 lg-only:justify-between xl-only:space-y-3 2xl-only:space-y-4">
              {project.attachment && project.attachment.length > 0 && (
                <button
                  className="lg-only:w-[48%] xl-only:w-full 2xl-only:w-full lg-only:py-2 xl-only:py-2.5 hover:bg-[#FBF4F4] 2xl-only:py-3 lg-only:text-xs xl-only:text-sm 2xl-only:text-base font-medium border border-gradient text-[#FF577F] rounded-md"
                  onClick={() =>
                    window.open(`${baseUrl}/${project.attachment[0]}`, "_blank")
                  }
                >
                  View Attachment
                </button>
              )}
              <button className="lg-only:w-[48%] xl-only:w-full hover:bg-[#FBF4F4] 2xl-only:w-full lg-only:py-2 xl-only:py-2.5 2xl-only:py-3 lg-only:text-xs xl-only:text-sm 2xl-only:text-base font-medium border border-gradient text-[#FF577F] rounded-md xl-only:mt-3 2xl-only:mt-4">
                Chat
              </button>
            </div>

            <div className="lg-only:grid lg-only:grid-cols-3 lg-only:gap-3 xl-only:space-y-4 2xl-only:space-y-5 xl-only:mt-5 2xl-only:mt-6">
              <TaskRecord projectId={project.id} />
              <div className="xl-only:mt-4 2xl-only:mt-5">
                <ClientsList />
              </div>
              <div className="xl-only:mt-4 2xl-only:mt-5">
                <CoworkerList />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnotatorDetails;
