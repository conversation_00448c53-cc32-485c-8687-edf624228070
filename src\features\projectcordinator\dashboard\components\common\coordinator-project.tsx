import React, { useEffect, useState } from "react";
import { getCoordinatorProjects } from "@/features/projectcordinator/api/api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { Link } from "react-router-dom";

// type ProjectStatus = "active" | "completed" | "pending" | "in_progress";

type Project = {
  id: string;
  name: string;
  tasksAssigned: number;
  status: string;
};

const statusColors: Record<string, string> = {
  "View Project": "bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white",
};

const Coordinator_Project: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await getCoordinatorProjects();

        // Transform the API response to match our component's data structure
        const transformedProjects = response.data.map((project: any) => ({
          id: project.id,
          name: project.name || "Unnamed Project",
          tasksAssigned: project._count?.tasks || 0,
          status: "View Project"
        }));

        setProjects(transformedProjects);
      } catch (error) {
        console.error("Error fetching projects:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:h-[150px] xl-only:h-[180px] 2xl-only:h-[200px] flex flex-col">
      <div className="flex justify-between items-center lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4">
        <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold font-poppins">Projects</h2>
       <Link to="/coordinator/projectdetails/projects">
        <button className="lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-semibold lg-only:px-2 lg-only:py-0.5 xl-only:px-3 xl-only:py-1 2xl-only:px-4 2xl-only:py-1.5 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all">
          View All
        </button> 
       </Link>
      </div>
      <ul className="overflow-y-auto pr-1">
        {loading ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">Loading projects...</p>
        ) : projects.length === 0 ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">No projects found</p>
        ) : (
          projects.map((project, index) => (
            <li key={project.id || index} className="flex items-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4 last:mb-0">
              <img
                src={getAvatarUrl(project.name)}
                alt="avatar"
                className="lg-only:w-5 lg-only:h-5 xl-only:w-6 xl-only:h-6 2xl-only:w-7 2xl-only:h-7 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="font-medium lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-[#282828]">
                  {project.name.split(" ").slice(0, 1).join(" ")}
                </p>
                <p className="lg-only:text-[8px] xl-only:text-[10px] 2xl-only:text-xs text-[#727272]">
                  {project.tasksAssigned} task{project.tasksAssigned !== 1 ? "s" : ""} assigned
                </p>
              </div>
              <span
                className={`text-white lg-only:text-[8px] xl-only:text-[10px] 2xl-only:text-xs lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-0.5 2xl-only:px-2.5 2xl-only:py-1 rounded-md ${
                  statusColors[project.status]
                }`}
              >
                {project.status}
              </span>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default Coordinator_Project;
