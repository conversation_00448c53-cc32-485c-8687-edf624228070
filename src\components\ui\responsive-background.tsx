import React from "react";
import { useResponsive } from "@/hooks/use-responsive";

interface ResponsiveBackgroundProps {
  imageSrc: string;
  children?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  overlayColor?: string;
  overlayOpacity?: number;
}

/**
 * A component that displays a responsive background image with content overlay
 * Ensures the background image is properly displayed across all supported screen sizes
 */
export const ResponsiveBackground: React.FC<ResponsiveBackgroundProps> = ({
  imageSrc,
  children,
  className = "",
  contentClassName = "",
  overlayColor = "rgba(0, 0, 0, 0)",
  overlayOpacity = 0
}) => {
  const { isLaptopMd, isLaptopLg } = useResponsive();

  // Determine background size and position based on screen size
  const getBackgroundStyles = () => {
    if (isLaptopLg) {
      // 4K/2560px screens
      return {
        backgroundSize: 'cover',
        backgroundPosition: 'center center'
      };
    } else if (isLaptopMd) {
      // 1440px screens
      return {
        backgroundSize: '100% 100%',
        backgroundPosition: 'center center'
      };
    } else {
      // 1024px screens
      return {
        backgroundSize: '100% 100%',
        backgroundPosition: 'center center'
      };
    }
  };

  const bgStyles = getBackgroundStyles();

  return (
    <div className={`relative min-h-screen w-full overflow-hidden ${className}`}>
      {/* Background image */}
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `url(${imageSrc})`,
          backgroundRepeat: 'no-repeat',
          ...bgStyles
        }}
      />

      {/* Optional overlay */}
      {overlayOpacity > 0 && (
        <div
          className="absolute inset-0"
          style={{
            backgroundColor: overlayColor,
            opacity: overlayOpacity
          }}
        />
      )}

      {/* Content */}
      <div className={`relative z-10 flex flex-col justify-center items-center min-h-screen w-full ${contentClassName}`}>
        {children}
      </div>
    </div>
  );
};

export default ResponsiveBackground;
