// Represents the structure of the "Subscription" field
type Subscription = {
  id: string;
  startDate: string;
  endDate: string;
  package: {
    name: string;
    id: string;
  };
};

// Represents the counts related to the "client" entity
type ClientCount = {
  assignmentsAsClient: number;
  projectsOwned: number;
  coWorkers: number; // Added for coworkers count
};

// Represents the structure of the "client" field in the "assignment" data
type Client = {
  id: string;
  name: string;
  email: string;
  role: "CLIENT"; // You could also generalize this if you need more roles in the future
  createdAt: string;
  updatedAt: string;
  Subscription: Subscription[];
  _count: ClientCount;
};

// Represents a single "assignment" object
export type Assignment = {
  id: string;
  clientId: string;
  developerId: string;
  coordinatorId: string;
  createdAt: string;
  client: Client;
};
