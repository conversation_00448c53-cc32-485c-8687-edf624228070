import { useState, useRef } from "react";
import CustomToast from "@/_components/common/customtoast"; // adjust path as per your file structure

interface OTPModalProps {
  onClose: () => void;
}

export default function OTPModal({ onClose }: OTPModalProps) {
  const [otp, setOtp] = useState(["", "", "", ""]);
  const [error, setError] = useState("");
  const [showToast, setShowToast] = useState(false);
  const inputsRef = useRef<Array<HTMLInputElement | null>>([]);

  const handleChange = (index: number, value: string) => {
    if (!/^\d*$/.test(value)) return; // allow only digits
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 3) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputsRef.current[index - 1]?.focus();
    }
  };

  const handleVerify = () => {
    const finalOtp = otp.join("");
    if (finalOtp.length < 4) {
      setError("Please enter the full OTP");
    } else {
      setError("");
      console.log("Entered OTP:", finalOtp);
      // Trigger toast after verification
      setShowToast(true);
      // You can add API call to verify OTP here
    }
  };

  const handleResend = () => {
    console.log("Resend OTP clicked");
    setOtp(["", "", "", ""]);
    inputsRef.current[0]?.focus();
    // Add resend API call here
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white rounded-xl shadow-lg p-6 w-[380px] text-center">
          <h2 className="text-xl font-semibold mb-2">Enter OTP to delete account</h2>
          <p className="text-sm text-gray-600 mb-1">
            If you don't want to delete then click on NO.
          </p>
          <p className="text-xs text-red-500 mb-4">Note: This action is irreversible</p>

          <div className="flex justify-center gap-2 mb-2">
            {otp.map((digit, index) => (
              <input
                key={index}
                type="text"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                ref={(el) => (inputsRef.current[index] = el)}
                className="w-12 h-12 text-center border border-gray-300 rounded-md text-lg font-semibold focus:outline-pink-500"
              />
            ))}
          </div>

          {error && <p className="text-xs text-red-500 mb-4">⚠️ {error}</p>}

          <div className="flex flex-row gap-6 justify-center items-center mb-2">
            <button
              onClick={onClose}
              className="border border-pink-500 text-pink-500 px-12 py-2 rounded-md hover:bg-pink-100 transition"
            >
              No
            </button>
            <button
              onClick={handleVerify}
              className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white px-10 py-2 rounded-md hover:opacity-90 transition"
            >
              Verify
            </button>
          </div>

          <p className="text-sm text-gray-600">
            Didn’t receive the code?{" "}
            <button
              onClick={handleResend}
              className="text-pink-500 font-semibold hover:underline"
            >
              Resend
            </button>
          </p>
        </div>
      </div>

      {showToast && (
        <div className="fixed top-6 right-6 z-50">
          <CustomToast
            title="Account Deleted"
            message="Your account has been permanently deleted."
            type="success"
            onClose={() => {
              setShowToast(false);
              onClose(); // Optional: close modal after toast
            }}
          />
        </div>
      )}
    </>
  );
}
