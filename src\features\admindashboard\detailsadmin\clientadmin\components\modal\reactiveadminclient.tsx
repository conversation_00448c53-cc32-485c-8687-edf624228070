import { adminClientActiveApi } from "../adminclientsuspenddelete_api/adminclientssuspenddelete_api";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ReactiveAdminClientProps {
  client: {
    id: string;
    name: string;
  };
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void; // Add this line
}

export default function ReactiveAdminClient({ client, open, onOpenChange, onSuccess }: ReactiveAdminClientProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleReactivate = async () => {
    try {
      setIsLoading(true);
      await adminClientActiveApi(client.id);
       
      // Call the success callback if it exists
      if (onSuccess) onSuccess();
      if (onOpenChange) onOpenChange(false);
    } catch (error) {
      console.error("Error reactivating client:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col gap-5 items-center justify-center sm:max-w-[425px]">
        <DialogHeader className="flex flex-row justify-center">
          <DialogTitle className="text-xl text-center text-[#282828]">
            Do you want to reactivate this account?
          </DialogTitle>
        </DialogHeader>
        
        <DialogFooter className="w-full flex justify-center gap-4">
          <Button 
            variant="ghost" 
            className="border-gradient px-14 py-6"
            onClick={() => onOpenChange?.(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            className="px-14 py-6"
            onClick={handleReactivate}
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : "Reactivate"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}