import { useParams } from 'react-router-dom';
import AdminProfileUserallData from "./adminprofiledetials"
import AdminBillingData from './adminclientbilling';
import { BackButton } from '@/_components/common';

const AdminClientProfileRoute = () => {
  const { clientId } = useParams<{ clientId: string }>();
  
  if (!clientId) {
    return <div>Client not found</div>;
  }

  const isEditing = false; // You need to define this value
  const onSaveSuccess = () => {}; // You need to define this function
  const onCancel = () => {}; // You need to define this function

  return (
    <div className='flex flex-col w-full p-4'>
      {/* Your existing component JSX */}
      <div className='flex flex-row items-center'>
        <BackButton/>
        <p className='text-[22px] font-semibold'>Profile Details</p>
      </div>
      <AdminProfileUserallData 
        clientId={clientId} 
        isEditing={isEditing} 
        onSaveSuccess={onSaveSuccess} 
        onCancel={onCancel} 
      />

      <AdminBillingData/>
    </div>
  );
};

export default AdminClientProfileRoute