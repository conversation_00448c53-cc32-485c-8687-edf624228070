import { customAxios } from "@/utils/axio-interceptor";

// Pending leave get
export const PendingLeaveApi = async () => {
  try {
    const response = await customAxios.get("/v1/leave/pending");
    return response.data;
  } catch (error) {
    console.error("Error fetching pending leaves:", error);
    throw error;
  }
};

// Approve leave post
export const AproovLeave = async (id: string) => {
  try {
    const response = await customAxios.post(`/v1/leave/approve/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error approving leave:", error);
    throw error;
  }
};

// Reject leave post
export const RejectLeave = async (id: string) => {
  try {
    const response = await customAxios.post(`/v1/leave/reject/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error rejecting leave:", error);
    throw error;
  }
};


export const getcordinatorclientLeaves = async () => {
  try {
    const response = await customAxios.get("/v1/leave/leave-requests");
    return response.data;
  } catch (error) {
    console.error("Error fetching all leaves:", error);
    throw error;
  }
};








export const getNotificationShift = async () => {
    try {
      const response = await customAxios.get("/v1/shift/pending-shift-requests");
      return response.data;
    } catch (error) {
      console.error("Error fetching notification:", error);
      return [];
    }
  };
  


  export const acceptShiftRequest = async (id: string) => {
    try {
      const response = await customAxios.post(`/v1/shift/approve-shift-change/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error approving shift request:", error);
      throw error;
    }
  };
  
 
  
// Get all shift notifications
// Get all shift notifications
export const GetClientCordinatorShiftNotification = async () => {
  try {
    const response = await customAxios.get("/v1/shift/shift-requests");
    return response.data;
  } catch (error) {
    console.error("Error fetching all shift requests:", error);
    throw error;
  }
};





