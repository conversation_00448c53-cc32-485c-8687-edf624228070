# Responsive Design Guidelines

## Overview

This project is designed to be responsive across laptop screen sizes (1024px, 1440px, and 4K/2560px) but is not intended for mobile and tablet devices. The application will display a friendly message when accessed from devices with screen widths below 1024px.

## Breakpoints

The following breakpoints are used throughout the application:

- **Mobile**: < 768px (not supported)
- **Tablet**: 768px - 1023px (not supported)
- **Laptop Small**: 1024px - 1439px
- **Laptop Medium**: 1440px - 2559px
- **Laptop Large/4K**: 2560px and above

## Implementation Details

### Device Restriction

The application prevents usage on mobile and tablet devices by:

1. Checking screen width in `App.tsx` using a React effect
2. Displaying a user-friendly message when the screen width is below 1024px

### Responsive Utilities

The project includes responsive utilities to help with implementing responsive design:

1. **CSS Media Queries**: Located in `src/index.css`, these adjust base font sizes for different screen sizes
2. **useResponsive Hook**: Located in `src/hooks/use-responsive.tsx`, this provides screen size detection and responsive rendering
3. **ResponsiveBackground Component**: Located in `src/components/ui/responsive-background.tsx`, this component ensures background images display properly across all supported screen sizes
4. **ResponsiveImage Component**: Located in `src/components/ui/responsive-image.tsx`, this component handles image display across different screen sizes
5. **bg-responsive CSS Class**: A utility class in `src/index.css` that provides consistent background image styling across different screen sizes
6. **auth-background CSS Class**: A utility class in `src/index.css` specifically for handling auth screen backgrounds

### Using the Responsive Hook

```tsx
import { useResponsive } from "@/hooks/use-responsive";

function MyComponent() {
  const {
    isLaptopSm,  // 1024px - 1439px
    isLaptopMd,  // 1440px - 2559px
    isLaptopLg,  // 2560px+
    width        // Current window width
  } = useResponsive();

  // Conditionally render or style based on screen size
  return (
    <div className={isLaptopLg ? "text-xl" : isLaptopMd ? "text-lg" : "text-base"}>
      Responsive Text
    </div>
  );
}
```

### Responsive Component

For conditional rendering based on screen size, use the `Responsive` component:

```tsx
import { Responsive } from "@/hooks/use-responsive";

function MyComponent() {
  return (
    <>
      <Responsive showOnLaptopSm={true} showOnLaptopMd={true} showOnLaptopLg={false}>
        <div>This content shows on 1024px and 1440px screens, but not on 4K</div>
      </Responsive>

      <Responsive showOnLaptopLg={true}>
        <div>This content only shows on 4K screens</div>
      </Responsive>
    </>
  );
}
```

## Best Practices

1. **Use Relative Units**: Prefer `rem`, `em`, and percentages over fixed pixel values
2. **Tailwind Responsive Classes**: Use Tailwind's responsive prefixes (e.g., `md:`, `lg:`) for quick responsive adjustments
3. **Test All Breakpoints**: Always test your changes at all supported screen sizes (1024px, 1440px, 2560px)
4. **Avoid Fixed Widths**: Use flexible layouts with `max-width` instead of fixed widths
5. **Responsive Typography**: Adjust text sizes based on screen size using the responsive hook

## Testing Responsive Design

You can test different screen sizes using:

1. Browser developer tools (responsive design mode)
2. Resizing your browser window
3. Using the responsive hook's `width` value for debugging

## Examples

### Responsive Text Size

```tsx
const getTitleSize = () => {
  if (isLaptopLg) return "text-5xl"; // 4K/2560px
  if (isLaptopMd) return "text-4xl"; // 1440px
  return "text-3xl"; // 1024px (default)
};

<h2 className={`${getTitleSize()} font-bold`}>Responsive Heading</h2>
```

### Responsive Layout

```tsx
<div className={`
  grid
  ${isLaptopLg ? 'grid-cols-4 gap-8' :
    isLaptopMd ? 'grid-cols-3 gap-6' :
    'grid-cols-2 gap-4'}
`}>
  {/* Grid items */}
</div>
```

### Responsive Background Images

Using the ResponsiveBackground component:

```tsx
import ResponsiveBackground from "@/components/ui/responsive-background";
import backgroundImage from "@/assets/background.png";

function MyComponent() {
  return (
    <ResponsiveBackground
      imageSrc={backgroundImage}
      overlayColor="rgba(0, 0, 0, 0.5)"
      overlayOpacity={0.3}
    >
      <div className="text-white text-center">
        <h1 className="text-4xl font-bold">My Content</h1>
        <p>This content will appear over the responsive background</p>
      </div>
    </ResponsiveBackground>
  );
}
```

Using the bg-responsive CSS class directly:

```tsx
function MyComponent() {
  return (
    <div className="relative min-h-screen">
      <div
        className="absolute inset-0 bg-responsive"
        style={{ backgroundImage: `url(${backgroundImage})` }}
      />
      <div className="relative z-10">
        {/* Your content here */}
      </div>
    </div>
  );
}
```

### Responsive Images

Using the ResponsiveImage component:

```tsx
import ResponsiveImage from "@/components/ui/responsive-image";
import myImage from "@/assets/image.png";

function MyComponent() {
  return (
    <div className="relative h-64 w-full">
      <ResponsiveImage
        src={myImage}
        alt="My responsive image"
        containerClassName="rounded-lg overflow-hidden"
      />
    </div>
  );
}
```

Using the auth-background CSS class for full-screen images:

```tsx
function MyComponent() {
  return (
    <div className="relative min-h-screen w-full overflow-hidden">
      <div className="absolute inset-0 z-0">
        <ResponsiveImage
          src={backgroundImage}
          alt="Background"
          containerClassName="w-full h-full"
        />
      </div>
      <div className="relative z-10">
        {/* Your content here */}
      </div>
    </div>
  );
}
```
