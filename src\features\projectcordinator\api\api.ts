import { PaginatedResponse } from "@/types/generics";
import { customAxios } from "@/utils/axio-interceptor";
import { Assignment } from "../types/client.types";
import { QueryFunctionContext } from "@tanstack/react-query";

export const getClientsData = async (
  _context: QueryFunctionContext
): Promise<PaginatedResponse<Assignment>> => {
  // Using a fixed endpoint with filter=client as requested
  const response = await customAxios.get("/v1/annotator/get-all-clients?filter=client");

  return response.data.data;
};



//proejcts coordinator all show
export const getCoordinatorProjects = async () => {
  const response = await customAxios.get("/v1/annotator/coordinator-projects");
  return response.data;
};





//annonator all show
export const getCoordinatorAnnonators = async () => {
  try {
    const response = await customAxios.get("/v1/annotator/get-all-clients?filter=developer");
    console.log("API Response in getCoordinatorAnnonators:", response);
    return response.data;
  } catch (error) {
    console.error("Error in getCoordinatorAnnonators:", error);
    throw error;
  }
};


// annotator id data shiow in table(id)

export const getCoordinatorAnnonatorById = async (id: string) => {
  try {
    console.log("Fetching annotator projects for ID:", id);
    const response = await customAxios.get(`/v1/annotator/annotator-projects/${id}`);

    // Log the raw response
    console.log("Raw API Response in getCoordinatorAnnonatorById:", response);

    // Check if the response has data
    if (!response || !response.data) {
      console.error("API response is missing data property:", response);
      throw new Error("API response is missing data");
    }

    // Return the data
    return response.data;
  } catch (error: any) {
    console.error("Error in getCoordinatorAnnonatorById:", error);
    // Add more details to the error
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error("Error response data:", error.response.data);
      console.error("Error response status:", error.response.status);
      console.error("Error response headers:", error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error("Error request:", error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error("Error message:", error.message || "Unknown error");
    }
    throw error;
  }
};


// Create Task API
export const createTaskApi = async (taskData: any) => {
  try {
    const response = await customAxios.post(
      "/v1/self-tasks/create-task",
      taskData
    ); // Replace with your actual endpoint
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getTasks = async () => {
  try {
    const response = await customAxios.get("/v1/self-tasks/get-all-task");
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(
      `/v1/self-tasks/get-single-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateTaskStatus = async (taskId: string, taskData: any) => {
  try {
    const response = await customAxios.patch(
      `/v1/self-tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/self-tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};

// src/types.ts
export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  status: string;
  createdAt?: string;
  priority?: string;
  name?: string; // Some APIs might return name instead of title
};

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};


export const getCoordinatorClientById = async (id: string) => {
  try {
    console.log("Fetching client projects for ID:", id);
    const response = await customAxios.get(`/v1/annotator/coordinator-client-projects/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error in getCoordinatorClientById:", error);
    throw error;
  }
};







//isme dashbaord top boxes div

export const getCoordinatorDashboardData = async () => {
  try {
    const response = await customAxios.get("/v1/dashboard/coordinator-dashboard");
    return response.data;
  } catch (error) {
    console.error("Error in getCoordinatorDashboardData:", error);
    throw error;
  }
};
