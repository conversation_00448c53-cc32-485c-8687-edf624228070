// // @ts-ignore
// import React from "react";
// import {
//   Dialog,
//   DialogTrigger,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogFooter,
//   DialogClose,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
// import { Label } from "@/components/ui/label";
// import { useForm } from "react-hook-form";
// import { useSuspendUserMutation } from "../../api/mutation";

// type FormValues = {
//   suspensionPeriod: "24h" | "7d" | "30d" | "always";
// };

// const SuspendModal = ({ userId }: { userId: string }) => {
//   const { mutate: suspendUser } = useSuspendUserMutation(); // Assuming you have a mutation for suspending users
//   const { handleSubmit, watch, setValue } = useForm<FormValues>({
//     defaultValues: {
//       suspensionPeriod: "24h",
//     },
//   });

//   const onSubmit = (data: FormValues) => {
//     console.log("Selected suspension period:", data.suspensionPeriod);

//     // Example: You can now send `data.suspensionPeriod` to your suspend API
//     // Handle based on "24h", "7d", "30d", or "always"
//     const suspensionDuration = {
//       "24h": 24 * 60 * 60 * 1000,
//       "7d": 7 * 24 * 60 * 60 * 1000,
//       "30d": 30 * 24 * 60 * 60 * 1000,
//       always: null, // or some other value
//     }[data.suspensionPeriod];
//     const suspendedUntil = suspensionDuration
//       ? Date.now() + suspensionDuration
//       : null;
//     suspendUser({
//       id: userId,
//       suspendedUntil,
//     });
//   };

//   const selectedValue = watch("suspensionPeriod");

//   return (
//     <Dialog>
//       <DialogTrigger asChild>
//         <Button variant="ghost" className="w-full justify-start">
//           Suspend Account
//         </Button>
//       </DialogTrigger>
//       <DialogContent>
//         <form
//           onSubmit={handleSubmit(onSubmit)}
//           className="flex flex-col justify-center gap-3 items-center"
//         >
//           <DialogHeader className="flex flex-col justify-center gap-10 items-center">
//             <DialogTitle className="text-xl md:text-xl text-center font-semibold">
//               Do you want to suspend this account?
//             </DialogTitle>
//           </DialogHeader>

//           <div className="flex justify-center my-4">
//             <RadioGroup
//               value={selectedValue}
//               onValueChange={(value) =>
//                 setValue(
//                   "suspensionPeriod",
//                   value as FormValues["suspensionPeriod"]
//                 )
//               }
//               className="flex gap-6"
//             >
//               <div className="flex items-center space-x-2">
//                 <RadioGroupItem value="24h" id="24h" />
//                 <Label htmlFor="24h">24 hours</Label>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <RadioGroupItem value="7d" id="7d" />
//                 <Label htmlFor="7d">7 days</Label>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <RadioGroupItem value="30d" id="30d" />
//                 <Label htmlFor="30d">30 days</Label>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <RadioGroupItem value="always" id="always" />
//                 <Label htmlFor="always">Always</Label>
//               </div>
//             </RadioGroup>
//           </div>

//           <DialogFooter className="flex justify-center gap-4">
//             <DialogClose asChild>
//               <Button
//                 variant="ghost"
//                 className="border-gradient text-black px-14 py-2"
//               >
//                 Cancel
//               </Button>
//             </DialogClose>
//             <Button
//               type="submit"
//               variant="gradient"
//               className="text-white px-14 py-2"
//             >
//               Suspend
//             </Button>
//           </DialogFooter>
//         </form>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default SuspendModal;

import { useRef, useState } from "react";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { useSuspendUserMutation } from "../../api/mutation";
type FormValues = {
  suspensionPeriod: "24" | "7" | "30" | "always";
};

const SuspendModalAdminlist = ({
  userId,
  // onSuccessRefresh,
}: {
  userId: string;
  // onSuccessRefresh: () => void;
}) => {
  const { mutate: suspendAdmin } = useSuspendUserMutation();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [showToast, setShowToast] = useState(false);
  const { handleSubmit, watch, setValue } = useForm<FormValues>({
    defaultValues: {
      suspensionPeriod: "24",
    },
  });

  {
    showToast && (
      <div>
        <p>User suspended successfully!</p>
      </div>
    );
  }
  const onSubmit = (data: FormValues) => {
    console.log("Selected suspension period:", data.suspensionPeriod);
    console.log("Suspending admin with ID:", userId);

    // Call the suspendAdmin API with the selected duration
    suspendAdmin(
      {
        id: userId,
        suspendedUntil: data.suspensionPeriod,
      },
      {
        onSuccess: () => {
          setShowToast(true);
          // onSuccessRefresh();
          closeRef.current?.click();
          setTimeout(() => setShowToast(false), 3000);
        },
      }
    );
  };

  const selectedValue = watch("suspensionPeriod");

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Suspend Account
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-col gap-y-3"
        >
          <DialogHeader className="flex flex-col gap-y-20 items-center">
            <DialogTitle className="text-xl text-center font-semibold">
              Do you want to suspend this account?
            </DialogTitle>
          </DialogHeader>

          <div className="flex justify-center my-4">
            <RadioGroup
              value={selectedValue}
              onValueChange={(value) =>
                setValue(
                  "suspensionPeriod",
                  value as FormValues["suspensionPeriod"]
                )
              }
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="24" id="24" />
                <Label htmlFor="24">24 hours</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="7" id="7" />
                <Label htmlFor="7">7 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="30" id="30" />
                <Label htmlFor="30">30 days</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="always" id="always" />
                <Label htmlFor="always">Always</Label>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter className="flex justify-center gap-3">
            <DialogClose asChild>
              <Button
                variant="ghost"
                className="border-gradient text-black px-14 py-2"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              variant="gradient"
              className="text-white px-14 py-2"
            >
              Suspend
              {/* {isLoading ? "Processing..." : "Suspend"} */}
            </Button>
          </DialogFooter>
          {/* Hidden DialogClose for programmatic closing */}
          <DialogClose asChild>
            <button ref={closeRef} className="hidden" />
          </DialogClose>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SuspendModalAdminlist;
