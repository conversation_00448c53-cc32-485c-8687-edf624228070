import { useEffect, useRef, useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DialogClose } from "@radix-ui/react-dialog";
import { usePackageList } from "../../api/query";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { customAxios } from "@/utils/axio-interceptor";

interface FormValues {
  name: string;
  lastname: string;
  email: string;
  domain: string;
  customDomain: string;
  role: string;
  packageId: string;
}

interface OnboardFormProps {
  userData?: Partial<FormValues> & { id?: string };
  onSuccess?: () => void;
}

const UpdateOnboardForm: React.FC<OnboardFormProps> = ({
  userData,
  onSuccess,
}) => {
  const { data } = usePackageList();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  const { register, control, handleSubmit, watch, setValue, reset } =
    useForm<FormValues>({
      defaultValues: {
        name: "",
        lastname: "",
        email: "",
        domain: "",
        customDomain: "",
        role: "",
        packageId: "",
      },
    });

  // Mutation for update API
  const updateUserMutation = useMutation({
    mutationFn: async (payload: any) => {
      const response = await customAxios.patch(
        `/v1/onboarding/update/${userData?.id}`,
        payload
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("User updated successfully");
      queryClient.invalidateQueries({ queryKey: ["onboarding"] });
      if (onSuccess) onSuccess();
      closeRef.current?.click();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to update user");
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  // Prefill form when editing
  useEffect(() => {
    if (userData) {
      // Extract domain from email
      let domain = "";
      let emailWithoutDomain = userData.email || "";

      if (userData.email?.includes("@macgence.com")) {
        domain = "domain1";
        emailWithoutDomain = userData.email.replace("@macgence.com", "");
      } else if (userData.email?.includes("@macgence.in")) {
        domain = "domain2";
        emailWithoutDomain = userData.email.replace("@macgence.in", "");
      } else if (userData.email) {
        // Handle custom domain
        const atIndex = userData.email.indexOf("@");
        if (atIndex !== -1) {
          domain = "domain3";
          emailWithoutDomain = userData.email.substring(0, atIndex);
          setValue("customDomain", userData.email.substring(atIndex));
        }
      }

      reset({
        name: userData.name || "",
        lastname: userData.lastname || "",
        email: emailWithoutDomain,
        domain,
        customDomain:
          domain === "domain3"
            ? userData.email?.substring(userData.email.indexOf("@")) || ""
            : "",
        role: userData.role || "",
        packageId: userData.packageId || "",
      });
    }
  }, [userData, reset, setValue]);

  const role = watch("role");
  const email = watch("email");
  const domain = watch("domain");
  const customDomain = watch("customDomain");

  const validatePrimaryName = (value: string) => {
    if (!value) return "Primary name is required";
    if (value.includes("@")) return "Primary name should not contain @ symbol";
    if (!/^[a-zA-Z0-9]+(?:[._-][a-zA-Z0-9]+)*$/.test(value)) {
      return "Primary name can only contain letters, numbers, dots, underscores or hyphens";
    }
    return true;
  };

  const onSubmit = (data: FormValues) => {
    const { name, lastname, email, domain, customDomain, role, packageId } =
      data;

    // Use customDomain if domain is "domain3" (custom domain)
    let finalDomain;
    if (domain === "domain3") {
      // Ensure the custom domain starts with @
      finalDomain = customDomain.startsWith("@")
        ? customDomain
        : `@${customDomain}`;
    } else if (domain === "domain1") {
      finalDomain = "@macgence.com";
    } else if (domain === "domain2") {
      finalDomain = "@macgence.in";
    } else {
      finalDomain = domain;
    }

    // Combine primary name and domain to create the complete email
    const completeEmail = `${email}${finalDomain}`;

    const payload = {
      name: `${name}`,
      lastname: `${lastname}`,
      email: completeEmail,
      role,
      packageId: role === "ANNOTATOR" ? packageId : undefined,
    };

    setIsSubmitting(true);
    updateUserMutation.mutate(payload);
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full lg-only:max-w-2xl xl-only:max-w-4xl 2xl-only:max-w-5xl lg-only:p-1 xl-only:p-2 2xl-only:p-8"
    >
      <h2 className="lg-only:text-base xl-only:text-xl 2xl-only:text-2xl font-semibold lg-only:mb-2 xl-only:mb-4 2xl-only:mb-5">
        User Information
      </h2>

      <div className="grid grid-cols-1 lg-only:grid-cols-2 xl-only:grid-cols-2 2xl-only:grid-cols-2 lg-only:gap-2 xl-only:gap-4 2xl-only:gap-5">
        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            First Name *
          </Label>
          <div className="border-gradient rounded-lg">
            <Input
              {...register("name", { required: "First name is required" })}
              placeholder="Enter first name"
              className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
            />
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Last Name
          </Label>
          <div className="border-gradient rounded-lg">
            <Input
              {...register("lastname")}
              placeholder="Enter last name"
              className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
            />
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Primary Email *
          </Label>
          <div className="border-gradient rounded-lg">
            <Input
              {...register("email", {
                validate: validatePrimaryName,
                onChange: (e) => {
                  // Remove any @ symbols if user tries to enter them
                  const value = e.target.value.replace(/@/g, "");
                  setValue("email", value);
                },
              })}
              placeholder="Enter primary email"
              className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
            />
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2 h-[5rem]">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Domain
          </Label>
          {domain === "domain3" ? (
            <div className="">
              <div className="border-gradient rounded-lg">
                <Input
                  {...register("customDomain", {
                    required: "Custom domain is required",
                    validate: (value) =>
                      value.includes("@") || "Domain must start with @",
                  })}
                  placeholder="Enter Domain (@example.com)"
                  className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
                />
              </div>
              <div className="flex flex-col justify-between lg-only:mt-0.5 xl-only:mt-1.5 2xl-only:mt-2">
                <span className="lg-only:text-[8px] xl-only:text-[12px] 2xl-only:text-base text-gray-500">
                  Full email: {email}
                  {customDomain.startsWith("@")
                    ? customDomain
                    : `@${customDomain}`}
                </span>
                <span
                  className="lg-only:text-[10px] xl-only:text-[12px] 2xl-only:text-base text-blue-500 cursor-pointer"
                  onClick={() => setValue("domain", "")}
                >
                  Change domain
                </span>
              </div>
            </div>
          ) : (
            <div>
              <Controller
                control={control}
                name="domain"
                rules={{ required: "Domain is required" }}
                render={({ field }) => (
                  <div className="border-gradient rounded-lg">
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                        <SelectValue placeholder="Select domain" />
                      </SelectTrigger>
                      <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                        <SelectItem value="domain1">@macgence.com</SelectItem>
                        <SelectItem value="domain2">@macgence.in</SelectItem>
                        <SelectItem value="domain3">Custom Domain</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              />
              {(domain === "domain1" || domain === "domain2") && (
                <span className="lg-only:text-[10px] xl-only:text-sm 2xl-only:text-base text-gray-500 lg-only:mt-0.5 xl-only:mt-1.5 2xl-only:mt-2 block">
                  Complete email: {email}
                  {domain === "domain1" ? "@macgence.com" : "@macgence.in"}
                </span>
              )}
            </div>
          )}
        </div>

        <div className="mt-4">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Role *
          </Label>
          <Controller
            control={control}
            name="role"
            rules={{ required: "Role is required" }}
            render={({ field }) => (
              <div className="border-gradient rounded-lg">
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                    <SelectItem value="ANNOTATOR">Annotator</SelectItem>
                    <SelectItem value="PROJECT_COORDINATOR">
                      Coordinator
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          />
        </div>

        {role === "ANNOTATOR" && (
          <div className="mt-4">
            <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
              Package *
            </Label>
            <Controller
              control={control}
              name="packageId"
              rules={{
                required:
                  role === "ANNOTATOR"
                    ? "Package is required for annotators"
                    : false,
              }}
              render={({ field }) => {
                const packages =
                  data?.pages.flatMap((page) => page.packages) || [];
                return (
                  <div className="border-gradient rounded-lg">
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                        <SelectValue placeholder="Select package" />
                      </SelectTrigger>
                      <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                        {packages.map((pkg: any) => (
                          <SelectItem key={pkg.id} value={pkg.id}>
                            {pkg.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }}
            />
          </div>
        )}
      </div>

      <div className="lg-only:mt-2 xl-only:mt-6 2xl-only:mt-8 flex justify-end lg-only:gap-1 xl-only:gap-3 2xl-only:gap-4">
        <Button
          type="submit"
          variant="gradient"
          className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[4rem] xl-only:py-6 2xl-only:px-[5rem] 2xl-only:py-7 lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Processing..." : "Save"}
        </Button>
        <DialogClose asChild>
          <Button
            type="button"
            variant="ghost"
            className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[4rem] xl-only:py-6 2xl-only:px-[5rem] 2xl-only:py-7 border-gradient lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </DialogClose>
        <DialogClose asChild>
          <button ref={closeRef} className="hidden">
            Close
          </button>
        </DialogClose>
      </div>
    </form>
  );
};

export default UpdateOnboardForm;
