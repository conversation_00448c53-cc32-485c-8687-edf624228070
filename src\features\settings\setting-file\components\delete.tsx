import { ChevronRight } from "lucide-react";
import { useState } from "react";
import ConfirmModal from './confimmodal';

export default function Delete() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    return (
        <div>
            {/* Delete Account Section */}
            <div  className="w-full mt-6  bg-[#F9EFEF] rounded-md p-4 flex flex-row items-center justify-between border-gradient">
                <div onClick={() => setIsModalOpen(true)}  className="cursor-pointer flex flex-row justify-between w-full mx-2 items-center">
                    Delete account
                    <div className="text-[#818080]">
                        <ChevronRight />
                    </div>
                </div>

                {isModalOpen && (
                    <ConfirmModal onClose={() => setIsModalOpen(false)} />
                )}

            </div>
        </div>
    )
}