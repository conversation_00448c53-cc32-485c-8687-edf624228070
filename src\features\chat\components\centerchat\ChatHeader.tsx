import React, {  useState, useRef } from 'react';
import { Search } from "lucide-react";
import VideoCallDropdown from "./videocalldropdown";

interface SelectedUser {
  id?: string;
  userId?: string;
  name: string;
  avatar: string;
  isGroup?: boolean;
}

const ChatHeader: React.FC<{ selectedUser: SelectedUser | null }> = ({ selectedUser }) => {
  // State management
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [videoDropdownOpen, setVideoDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  // Video call handlers
  const handleGoogleMeet = async (type: 'create' | 'instant' | 'schedule') => {
    try {
      console.log(`Creating Google Meet: ${type}`);
      // Implement your Google Meet API call here
      // Example:
      // const meeting = await createGoogleMeeting(type);
      // window.open(meeting.url, '_blank');
    } catch (error) {
      console.error('Google Meet error:', error);
    }
  };

  const handleZoomMeeting = async (type: 'create' | 'instant' | 'schedule') => {
    try {
      console.log(`Creating Zoom Meeting: ${type}`);
      // Implement your Zoom API call here
      // Example:
      // const meeting = await createZoomMeeting(type);
      // window.open(meeting.join_url, '_blank');
    } catch (error) {
      console.error('Zoom Meeting error:', error);
    }
  };

  if (!selectedUser) return null;

  return (
    <div className="flex items-center rounded-xl justify-between p-4 shadow-[0px_1px_12.24px_0px_#D8E4F7] relative">
      {/* User Info */}
      <div className="flex items-center gap-2">
        <img
          src={selectedUser.avatar}
          className="w-10 h-10 rounded-full"
          alt="avatar"
        />
        <div>
          <p className="font-semibold">{selectedUser.name}</p>
          <p className="text-xs text-gray-400">
            {selectedUser.isGroup ? "Group Chat" : "Online"}
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div
        className="flex gap-4 text-gray-500 items-center relative"
        ref={dropdownRef}
      >
        {/* Search */}
        <div className="relative" ref={searchRef}>
          <Search
            className="cursor-pointer"
            onClick={() => setSearchOpen((prev) => !prev)}
          />
          {searchOpen && (
            <div className="absolute -top-1 right-[1.5rem] flex items-center border p-1 rounded-md w-[11rem] text-black z-10 bg-white shadow">
              <input
                type="text"
                placeholder="Search messages..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="flex-1 text-sm outline-none"
              />
            </div>
          )}
        </div>

        {/* Video Call Dropdown */}
        <VideoCallDropdown
          dropdownRef={dropdownRef}
          videoDropdownOpen={videoDropdownOpen}
          setVideoDropdownOpen={setVideoDropdownOpen}
          handleGoogleMeet={handleGoogleMeet}
          handleZoomMeeting={handleZoomMeeting}
        />
      </div>
    </div>
  );
};

export default ChatHeader;