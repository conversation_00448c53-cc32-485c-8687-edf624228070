// @ts-ignore
import React from "react";
import { Route, Routes } from "react-router-dom";
import TransactionList from "./transaction/transaction";
import Subscription from "./subscription/index";
import PaymentMethod from "./payment_method/paymentmethod";

const BillingRoute = () => {
  return (
    <Routes>
      <Route path="/" element={<TransactionList />} />
      <Route path="/subscription" element={<Subscription />} />
      <Route path="/payment-method" element={<PaymentMethod />} />
    </Routes>
  );
};

export default BillingRoute;
