import { baseUrl } from "@/globalurl/baseurl";
import { customAxios } from "@/utils/axio-interceptor";
import axios from "axios";

export const fetchUserData = async (email: string, password: string) => {
  try {
    const response = await axios.post(`${baseUrl}admin/admin_login`, {
      email,
      password,
    });
    localStorage.setItem("access-token", response.data.token.access.token);
    localStorage.setItem("refresh-token", response.data.token.refresh.token);
    return response;
  } catch (error) {
    localStorage.clear();
  }
};

export const AdminLogin = async (email: string, password: string) => {
  try {
    const response = await customAxios.post(
      `newadmin/login`,
      {
        email,
        password,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
        withCredentials: true,
      }
    );
    localStorage.setItem("token", response.data.data.accessToken);
    localStorage.setItem("refreshToken", response.data.data.refreshToken);
    console.log(response);
    return response;
  } catch (error) {
    localStorage.clear();
  }
};
