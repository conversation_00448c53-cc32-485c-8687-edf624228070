import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { IoIosArrowDown } from "react-icons/io";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "react-toastify";
import { createTaskApi } from "../../annonator_api/annonator_api";

type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  priority: string;
  startDate: string;
  endDate: string;
  color: string;
  columnId: string;
};

type Props = {
  columnId: string;
  onAddTask: (task: Task) => void;
};

const colorOptions = [
  { name: "Orange", color: "#FBBF24" },
  { name: "Red", color: "#F87171" },
  { name: "<PERSON>", color: "#F9A8D4" },
  { name: "Yellow", color: "#FACC15" },
  { name: "<PERSON>", color: "#60A5FA" },
];

const initialForm = {
  title: "",
  description: "",
  level: "",
  priority: "",
  startDate: "",
  endDate: "",
};

const AnnotatorCreatetask: React.FC<Props> = ({ columnId, onAddTask }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState(initialForm);
  const [errors, setErrors] = useState({
    title: false,
    description: false,
    priority: false,
    startDate: false,
    endDate: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedColor, setSelectedColor] = useState(colorOptions[0].color);
  const [showColorDropdown, setShowColorDropdown] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: false }));
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setShowColorDropdown(false);
  };

  const validateForm = () => {
    const newErrors = {
      title: !formData.title.trim(),
      description: !formData.description.trim(),
      priority: !formData.priority,
      startDate: !formData.startDate,
      endDate: !formData.endDate,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Prepare the task data for the API
      const taskData = {
        name: formData.title,
        description: formData.description,
        priority: formData.priority.toUpperCase(), // Convert to uppercase to match API enum
        status: "PENDING", // Default status
        startDate: new Date(formData.startDate).toISOString(),
        dueDate: new Date(formData.endDate).toISOString(),
        color: selectedColor,
      };

      // Call the API
      const response = await createTaskApi(taskData);
      console.log("API Response:", response);
      toast.success("Task created successfully!");

      // Create the local task object
      const newTask: Task = {
        id: response.data.id,
        title: response.data.name,
        description: response.data.description,
        level: "", // Not in API response
        priority: response.data.priority.toLowerCase(), // Convert to lowercase for local state
        startDate: response.data.startDate,
        endDate: response.data.dueDate,
        color: selectedColor,
        columnId,
      };

      // Update local state
      onAddTask(newTask);
      handleClose();
    } catch (error) {
      console.error("Error creating task:", error);
      // You might want to show an error message to the user here
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsDialogOpen(false);
    setFormData(initialForm);
    setErrors({
      title: false,
      description: false,
      priority: false,
      startDate: false,
      endDate: false,
    });
    setSelectedColor("#60A5FA");
    setShowColorDropdown(false);
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <div className="flex items-center justify-center cursor-pointer">
          <Plus className="text-white text-xs" />
        </div>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-[#282828] font-poppins font-semibold text-xl">
            Create Task
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title + Color */}
          <div>
            <label className="text-sm font-medium text-[#282828]">
              Task Name *
            </label>
            <div className="flex items-center gap-2 mt-2">
              <div className="border-gradient w-full rounded-md">
                <input
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="Enter task name"
                  className={`w-full rounded-md px-4 py-2 bg-red-50 text-gray-700 placeholder-gray-500 focus:outline-none ${
                    errors.title ? "border border-red-500" : ""
                  }`}
                />
              </div>
              <div className="relative">
                <div
                  className="cursor-pointer"
                  onClick={() => setShowColorDropdown((prev) => !prev)}
                >
                  <div className="w-[54px] h-[40px] bg-red-50 flex justify-center items-center gap-2 rounded-md border border-gradient">
                    <div
                      className="w-5 h-5 rounded-full border"
                      style={{ backgroundColor: selectedColor }}
                    />
                    <IoIosArrowDown />
                  </div>
                </div>

                {showColorDropdown && (
                  <div className="absolute top-[110%] right-0 bg-white border rounded-md shadow-md p-2 z-10 flex flex-col gap-2">
                    {colorOptions.map((c) => (
                      <div
                        key={c.name}
                        className="w-5 h-5 rounded-full cursor-pointer border hover:scale-110 transition"
                        style={{ backgroundColor: c.color }}
                        onClick={() => handleColorSelect(c.color)}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">Title is required</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="text-sm font-medium text-[#282828]">
              Description *
            </label>
            <Textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={`w-full p-3 overflow-y-scroll border-gradient mt-2 bg-[#F9EFEF]  text-sm rounded-md focus:outline-none ${
                errors.description ? "border border-red-500" : ""
              }`}
              placeholder="Type your message here."
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">
                Description is required
              </p>
            )}
          </div>

          {/* Priority & Admin */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Priority *
              </label>
              <Select
                value={formData.priority}
                onValueChange={(value) => {
                  setFormData((prev) => ({ ...prev, priority: value }));
                  setErrors((prev) => ({ ...prev, priority: false }));
                }}
              >
                <SelectTrigger
                  className={`w-full text-[#5E5E5E] py-3 focus:outline-none text-xs font-normal bg-[#F9EFEF] rounded-md ${
                    errors.priority
                      ? "border border-red-500"
                      : "border-gradient"
                  }`}
                >
                  <SelectValue placeholder="Select a priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Select a priority</SelectLabel>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              {errors.priority && (
                <p className="text-red-500 text-sm mt-1">
                  Priority is required
                </p>
              )}
            </div>
          </div>

          {/* Deadline */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Start Date *
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              />
              {errors.startDate && (
                <p className="text-red-500 text-sm mt-1">
                  Start Date is required
                </p>
              )}
            </div>
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Due Date *
              </label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              />
              {errors.endDate && (
                <p className="text-red-500 text-sm mt-1">
                  Due Date is required
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-4 justify-end items-center">
            <button
              onClick={handleClose}
              type="reset"
              className="w-32 px-3 py-2 font-bold text-base rounded-md border-gradient border text-black transition"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="w-32 px-3 py-2 font-bold text-base rounded-md bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white transition"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creating..." : "Create Task"}
            </button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AnnotatorCreatetask;
