import { DataTable } from "@/components/globalfiles/data.table";
import { ClientcoworkerColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { useGetCoworkerList } from "../api/query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

const ClientcoworkerTable = () => {
  const navigate = useNavigate();
  const { data, isLoading } = useGetCoworkerList();

  const annotators = data?.pages.flatMap((page) => page.data) || [];
  console.log("data", annotators);
  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }
  return (
    <div className=" bg-white h-full space-y-2">
      <div className="flex  flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />

        <h1 className="text-[#282828] text-[24px] font-semibold">
          Recently invited co-worker
        </h1>
      </div>

      <DataTable
        title="Forms"
        columns={ClientcoworkerColumns()}
        data={annotators || []} // 👈 Pass dummy data here
        loading={isLoading}
        disablePagination
      />
    </div>
  );
};

export default ClientcoworkerTable;
