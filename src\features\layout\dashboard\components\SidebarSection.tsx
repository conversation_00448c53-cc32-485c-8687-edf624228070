import React from "react";
import <PERSON>barItem from "./SidebarItem";

interface SidebarSectionProps {
  section: {
    items: {
      section: string;
      items: {
        name: string;
        path?: string;
        icon: React.ReactNode;
        children?: {
          name: string;
          path: string;
        }[];
      }[];
    }[];
  };
  sectionType: string;
  openDropdowns: { [key: string]: boolean };
  toggleDropdown: (name: string) => void;
  setOpenDropdowns: (value: {}) => void;
  hasAnnotators: boolean;
  styles: {
    sectionContainer: string;
    sectionTitle: string;
    itemContainer: string;
    link: string;
    linkActive: string;
    linkInactive: string;
    iconTextContainer: string;
    icon: string;
    text: string;
    dropdownButton: string;
    dropdownContainer: string;
    dropdownLine: string;
    dropdownItem: string;
    dropdownItemActive: string;
    dropdownItemLine: string;
  };
}

const SidebarSection: React.FC<SidebarSectionProps> = ({
  section,
  sectionType,
  openDropdowns,
  toggleDropdown,
  setOpenDropdowns,
  styles,
  hasAnnotators,
}) => {
  return (
    <div>
      {section.items.map(
        (subSection, subIndex) =>
          subSection.section === sectionType && (
            <div key={subIndex} className={styles.sectionContainer}>
              <h3 className={styles.sectionTitle}>{subSection.section}</h3>
              {subSection.items.map((item, index) => (
                <SidebarItem
                  key={index}
                  item={item}
                  isDropdownOpen={openDropdowns[item.name] || false}
                  toggleDropdown={toggleDropdown}
                  setOpenDropdowns={setOpenDropdowns}
                  styles={styles}
                  hasAnnotators={hasAnnotators}
                  sectionType={sectionType} // Pass sectionType
                />
              ))}
            </div>
          )
      )}
    </div>
  );
};

export default SidebarSection;