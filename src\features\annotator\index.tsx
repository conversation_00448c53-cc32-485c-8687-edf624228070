import { Route, Routes } from "react-router-dom";
// import AnnotatorDashboard from "./dashboard/annotator"
// import Attendance from "./dashboard/attendence"
// import Notifications from "./dashboard/notification"
import AnnotatorDashboard from "./dashboard/annotator";
import AnnotatorProjectManagement from "./dashboard/projectmangement";
import AnnotatorDetails from "./annotatordescription";
import AnnonatorNotification from "./dashboard/annonator_notification/annonator_notification";

const Annotatorroutespage = () => {
  return (
    <Routes>
      <Route path="/" element={<AnnotatorDashboard />} />
      <Route
        path="/annotator/annotatordetail"
        element={<AnnotatorProjectManagement />}
      />
      <Route
        path="/annotator/notification"
        element={<AnnonatorNotification />}
      />
      <Route
        path="/annotator/annotatordescription"
        element={<AnnotatorDetails />}
      />
    </Routes>
  );
};
export default Annotatorroutespage;
