import { errorMessage } from "@/utils/errorHandler";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import {
  inviteCoworker,
  resendCoworkerInvite,
  updateCoworkerPermission,
} from "./coworkers.api";

export function useInviteCoWorkerMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: {
      email: string;
      notify: boolean;
      permission: "VIEW" | "EDIT";
    }) => inviteCoworker(content),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Coworker invited successfully");
        await queryClient.invalidateQueries({ queryKey: ["coworkers"] }); // Update queryKey accordingly
      }
    },
  });
}

export function useUpdateCoWorkerPermissionMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: { id: string; permission: "VIEW" | "EDIT" }) =>
      updateCoworkerPermission(content),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Coworker permission updated successfully");
        await queryClient.invalidateQueries({ queryKey: ["coworkers"] }); // Update queryKey accordingly
      }
    },
  });
}

export function useResendCoWorkerInviteMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => resendCoworkerInvite(id),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Coworker invite resent successfully");
        await queryClient.invalidateQueries({ queryKey: ["coworkers"] }); // Update queryKey accordingly
      }
    },
  });
}
