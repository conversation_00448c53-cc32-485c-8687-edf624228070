import { useEffect } from "react";
import { Socket } from "socket.io-client";

interface UseGroupRoomProps {
  socket: Socket | null;
  userId: string;
  groupId: string;
}

export const useJoinGroupRoom = ({
  socket,
  userId,
  groupId,
}: UseGroupRoomProps) => {
  useEffect(() => {
    if (!socket || !userId || !groupId) return;

    // Join the group room
    socket.emit("join_group", { userId, groupId });

    return () => {
      console.log(`Leaving group room: group:${groupId}`);
    };
  }, [socket, userId, groupId]);
};
