import React, { RefObject, useState, useMemo } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  FileText,
  Download,
  X,
  Image,
  FileSpreadsheet,
  Archive,
} from "lucide-react";

interface Message {
  id: string; // Added as required for unique key
  text: string;
  time: string;
  self?: boolean;
  fileUrl?: string;
  fileType?: string;
  sender: {
    id: string;
    name: string;
  };
  isUploading?: boolean; // Added to fix compile error
}

interface MessageListProps {
  messages: Message[];
  filteredMessages: Message[];
  searchText: string;
  scrollRef: RefObject<HTMLDivElement>;
  onMention?: (username: string) => void;
  uploadProgress: number;
  uploadingMessageId: string | null;
}

const MessageList: React.FC<MessageListProps> = React.memo(
  ({
    messages,
    filteredMessages,
    searchText,
    scrollRef,
    onMention,
    uploadProgress,
    //uploadingMessageId,
  }) => {
    const list = useMemo(() => {
      const uniqueMessages = new Map<string, Message>();
      (searchText ? filteredMessages : messages).forEach((msg) =>
        uniqueMessages.set(msg.id, msg)
      );
      return Array.from(uniqueMessages.values());
    }, [messages, filteredMessages, searchText]);

    const [expandedImage, setExpandedImage] = useState<string | null>(null);
    const [hiddenMessages, setHiddenMessages] = useState<
      Record<string, boolean>
    >({});

      console.log("setHiddenMessages", setHiddenMessages);

    const getFileIcon = (fileType?: string) => {
      switch (fileType?.toUpperCase()) {
        case "IMAGE":
          return <Image className="w-5 h-5" />;
        case "EXCEL":
        case "PPT":
          return <FileSpreadsheet className="w-5 h-5" />;
        case "ZIP":
          return <Archive className="w-5 h-5" />;
        case "PDF":
          return <FileText className="w-5 h-5 text-red-500" />;
        case "DOC":
          return <FileText className="w-5 h-5" />;
        default:
          return <FileText className="w-5 h-5" />;
      }
    };

    const renderFileContent = (msg: Message) => {
      // Skip rendering stale temp messages
      if (msg.id?.startsWith("temp-") && !msg.isUploading) return null;

      // Show upload progress if still uploading
      if (msg.isUploading) {
        return (
          <div className="mb-2 relative flex items-center justify-center">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <span className="ml-2 text-sm text-gray-500">
              {uploadProgress === 100
                ? "Processing..."
                : `Uploading... ${uploadProgress}%`}
            </span>
          </div>
        );
      }

      // Show image preview
      if (msg.fileType?.toUpperCase() === "IMAGE" && msg.fileUrl) {
        return (
          <div className="mb-2 relative">
            <img
              src={msg.fileUrl}
              alt="Shared content"
              className="max-w-full max-h-64 rounded-lg cursor-pointer"
              onError={(e) => {
                console.error("Failed to load image:", msg.fileUrl);
                e.currentTarget.src = "/image-placeholder.png";
              }}
            />
          </div>
        );
      }

      // Show file download link
      const fileName = msg.fileUrl?.split("/").pop() || "file";
      return (
        <div className="flex items-center gap-2 p-2 bg-gray-100 rounded-lg mb-2">
          {getFileIcon(msg.fileType)}
          <span className="text-sm truncate max-w-[200px]">{fileName}</span>
          <a
            href={msg.fileUrl}
            download={fileName}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-auto text-blue-500"
          >
            <Download className="w-4 h-4" />
          </a>
        </div>
      );
    };

    const renderMessageText = (text: string) => {
      const mentionRegex = /@(\w+)/g;
      const parts = text.split(mentionRegex);

      return parts.map((part, index) => {
        if (index % 2 === 1) {
          return (
            <span
              key={index}
              className="font-semibold text-blue-600 cursor-pointer hover:underline"
              onClick={() => onMention?.(part)}
            >
              @{part}
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      });
    };

    return (
      <ScrollArea className="flex-1 px-4 py-2">
        <div className="flex flex-col gap-2">
          {list.map((msg) => (
            <div
              key={msg.id} // Changed from index to message ID
              data-msg={msg.text}
              className={`max-w-lg p-3 rounded-xl text-sm break-words ${
                msg.self ? "bg-[#CED6E2] self-end" : "bg-white self-start"
              }`}
            >
              {!msg.self && msg.sender?.name && (
                <div className="font-semibold text-xs text-gray-600 mb-1">
                  {msg.sender.name}
                </div>
              )}
              {msg.fileUrl && renderFileContent(msg)}
              {msg.text && (
                <div className="flex items-start justify-between">
                  <p
                    className={
                      hiddenMessages[msg.id] ? "filter blur-md select-none" : ""
                    }
                  >
                    {msg.text.includes("http")
                      ? (() => {
                          try {
                            const url = new URL(
                              msg.text.split(" ").pop() || ""
                            );
                            const textWithoutUrl = msg.text.split(url.href)[0];
                            return (
                              <>
                                {renderMessageText(textWithoutUrl)}
                                <a
                                  href={url.href}
                                  target="_blank"
                                  rel="noreferrer"
                                  className="text-blue-600 underline"
                                >
                                  {url.hostname}
                                </a>
                              </>
                            );
                          } catch {
                            return renderMessageText(msg.text);
                          }
                        })()
                      : renderMessageText(msg.text)}
                  </p>
                </div>
              )}
              <span className="text-[10px] text-gray-500 float-right">
                {msg.time}
              </span>
            </div>
          ))}
          <div ref={scrollRef} />
        </div>

        {expandedImage && (
          <div
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
            onClick={() => setExpandedImage(null)}
          >
            <div className="relative max-w-4xl max-h-[90vh]">
              <button
                className="absolute -top-10 right-0 text-white"
                onClick={() => setExpandedImage(null)}
              >
                <X className="w-6 h-6" />
              </button>
              <img
                src={expandedImage}
                alt="Expanded view"
                className="max-w-full max-h-[90vh] object-contain"
                onError={(e) => {
                  console.error(
                    "Failed to load expanded image:",
                    expandedImage
                  );
                  e.currentTarget.style.display = "none";
                  e.currentTarget.alt = "Failed to load image";
                }}
              />
            </div>
          </div>
        )}
      </ScrollArea>
    );
  }
);

export default MessageList;
