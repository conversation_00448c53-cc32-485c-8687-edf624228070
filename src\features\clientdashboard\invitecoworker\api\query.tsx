import { useInfiniteQuery } from "@tanstack/react-query";
import { getCoworkerList } from "./coworkers.api";
import { PaginatedResponse } from "@/types/generics";
import { Annotator } from "@/types/onboarding.types";

export const useGetCoworkerList = () => {
  return useInfiniteQuery<PaginatedResponse<Annotator>, Error>({
    queryKey: ["coworkers"],
    queryFn: getCoworkerList,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.nextPage : undefined,
    initialPageParam: 1, // default is 0, you can override it here
  });
};