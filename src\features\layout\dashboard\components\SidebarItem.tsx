import React from "react";
import { Link, useLocation } from "react-router-dom";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useHasAccess } from "@/utils/role-checks";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";

interface SidebarItemProps {
  item: {
    name: string;
    path?: string;
    icon: React.ReactNode;
    children?: {
      name: string;
      path: string;
    }[];
  };
  isDropdownOpen: boolean;
  toggleDropdown: (name: string) => void;
  setOpenDropdowns: (value: {}) => void;
  hasAnnotators: boolean;
  sectionType: string; // Added to identify Main or More section
  styles: {
    itemContainer: string;
    link: string;
    linkActive: string;
    linkInactive: string;
    iconTextContainer: string;
    icon: string;
    text: string;
    dropdownButton: string;
    dropdownContainer: string;
    dropdownLine: string;
    dropdownItem: string;
    dropdownItemActive: string;
    dropdownItemLine: string;
  };
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  item,
  isDropdownOpen,
  toggleDropdown,
  setOpenDropdowns,
  styles,
  hasAnnotators,
  sectionType,
}) => {
  const location = useLocation();
  const isActive = location.pathname === item.path;
  const hasAccess = useHasAccess();

  const role = useAppSelector((state: RootState) => state.auth.user?.role);

  // Conditionally skip rendering if it's Billing and access is not allowed
  if (item.name === "Billing" && !hasAccess) {
    return null;
  }

  if (item.name === "Add on" && role === "COWORKER") {
    return null;
  }
  if (item.name === "Invite Co-worker" && role === "COWORKER") {
    return null;
  }

  // Allow "Billing" or items in "More" section to be accessible for CLIENT even if hasAnnotators is false
  const isAllowed =
    role === "CLIENT" && (item.name === "Billing" || sectionType === "More")
      ? true
      : hasAnnotators;

  const linkClasses = `flex items-center gap-2 rounded-md cursor-pointer lg:px-1.5 lg:py-[8px] xl:px-2 xl:py-[10px] 2xl:px-3 2xl:py-[12px] ${
    isActive
      ? "bg-[#f6f6f6] text-[#FF577F]"
      : isAllowed
      ? "hover:bg-[#f6f6f6] hover:text-[#FF577F]"
      : "opacity-50 cursor-not-allowed"
  }`;

  return (
    <div className={styles.itemContainer}>
      {item.path ? (
        <Link
          to={isAllowed ? item.path : "#"}
          onClick={() => isAllowed && setOpenDropdowns({})}
          className={linkClasses}
        >
          <div className={styles.iconTextContainer}>
            <div className={styles.icon}>{item.icon}</div>
            <span className={styles.text}>{item.name}</span>
          </div>
        </Link>
      ) : (
        <button
          className={`flex items-center gap-4 rounded-md cursor-pointer w-full text-left text-[#757575] lg:p-1.5 xl:p-2 2xl:p-3 ${
            isAllowed
              ? "hover:bg-gray-200 hover:text-[#FF577F]"
              : "opacity-50 cursor-not-allowed"
          }`}
          onClick={() => isAllowed && toggleDropdown(item.name)}
          disabled={!isAllowed}
        >
          <div className={styles.icon}>{item.icon}</div>
          <span className={styles.text}>{item.name}</span>
          <div className="ml-auto">
            {isDropdownOpen ? <ChevronDown /> : <ChevronRight />}
          </div>
        </button>
      )}
      {isDropdownOpen && item.children && (
        <div className={styles.dropdownContainer}>
          <div className={styles.dropdownLine}></div>
          {item.children.map((subItem, subIndex) => {
            const isSubActive = location.pathname === subItem.path;
            return (
              <Link
                key={subIndex}
                to={isAllowed ? subItem.path : "#"}
                onClick={() => isAllowed && setOpenDropdowns({})}
                className={`${styles.dropdownItem} ${
                  isSubActive ? styles.dropdownItemActive : ""
                }`}
              >
                {subItem.name}
                <span className={styles.dropdownItemLine}></span>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default SidebarItem;