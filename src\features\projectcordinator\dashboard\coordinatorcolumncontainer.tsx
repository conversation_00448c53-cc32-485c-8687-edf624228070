import { useDroppable } from "@dnd-kit/core";
import { AnimatePresence } from "framer-motion";
// import { Task } from "@/types/adminkanbantype";
import { CoordinatorDraggableTask } from "./coordinatordraggable";
import CoordinatorCreatetask from "./coordinatorcreatetask";
import { DetailsTask } from "@/types/kanbantasktype";

export type Column = {
  id: string;
  title: string;
  tasks: DetailsTask[];
};

export function CoordinatorColumncontainer({
  column,
  tasks,
  onDeleteTask,
  onAddTask,
  fetchTasksProp,
}: {
  column: Column;
  tasks: DetailsTask[];
  onDeleteTask: (task: DetailsTask) => void;
  onAddTask: (newTask: DetailsTask, columnId: string) => void;
  fetchTasksProp: (saveToLocalStorage: boolean) => Promise<void>;
}) {
  const { setNodeRef } = useDroppable({
    id: column.id,
  });
  const fetchTasks = () => fetchTasksProp(true);

  const getColumnColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-[#FBF4F4]";
      case "in-progress":
        return "bg-[#FBF4F4]";
      case "done":
        return "bg-[#FBF4F4]";
      default:
        return "bg-[#FBF4F4]";
    }
  };

  const getTitleIndicatorColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]";
      case "in-progress":
        return "bg-white";
      case "done":
        return "bg-white";
      default:
        return "bg-gray-400";
    }
  };

  return (
    <div>
      <div
        className={`w-32 p-3 flex justify-center items-center rounded-full ${getTitleIndicatorColor(
          column.id
        )}`}
      >
        <CoordinatorCreatetask
          onAddTask={(task) => onAddTask(task, column.id)}
          columnId={column.id}
        />
      </div>
      <div className="flex justify-between px-1 mb-2 items-center mt-4">
        <h2 className="font-semibold text-[#545454] text-muted-foreground">
          {column.title}
        </h2>
      </div>
      <div
        ref={setNodeRef}
        className={`rounded-lg p-4 mt-4 border border-red-500 ${getColumnColor(
          column.id
        )} lg-only:h-[calc(100vh-100px)] xl-only:h-[calc(100vh-110px)] 2xl-only:h-[calc(100vh-150px)] overflow-y-auto`}
      >
        <AnimatePresence mode="popLayout">
          {tasks.map((task) => (
            <CoordinatorDraggableTask
              key={task.id}
              task={task}
              column={column}
              onDelete={onDeleteTask}
              fetchTasks={fetchTasks}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
