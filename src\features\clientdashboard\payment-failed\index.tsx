import { motion } from "framer-motion";
import { FaTimesCircle } from "react-icons/fa";
import { useNavigate } from "react-router-dom"; // Import useNavigate for navigation

const PaymentFailed = () => {
  const navigate = useNavigate(); // Initialize navigate hook

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full text-center"
      >
        {/* Animated X icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.2,
          }}
          className="flex justify-center mb-6"
        >
          <div className="relative">
            <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center">
              <FaTimesCircle className="text-red-500 text-5xl" />
            </div>

            {/* Pulsing circle effect */}
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 1,
              }}
              className="absolute inset-0 border-4 border-red-200 rounded-full"
            />
          </div>
        </motion.div>

        <motion.h1
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{
            duration: 0.5,
            delay: 0.4,
            type: "spring",
            stiffness: 120,
          }}
          className="text-3xl font-bold text-gray-800 mb-2"
        >
          Payment Failed
        </motion.h1>
        <p className="text-gray-600 mb-6">
          We couldn't process your payment. Please try again or use a different
          payment method.
        </p>

        <div className="space-y-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-lg shadow-md transition-colors w-full"
            onClick={() => navigate(-1)} // Navigate back one step
          >
            Try Again
          </motion.button>
        </div>

        <p className="text-sm text-gray-500 mt-4">
          Contact support if the problem persists.
        </p>
      </motion.div>
    </div>
  );
};

export default PaymentFailed;