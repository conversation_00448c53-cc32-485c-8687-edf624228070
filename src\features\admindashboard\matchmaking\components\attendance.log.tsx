import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { useInfiniteMatchMakingList } from "../api/query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import QuestionireDetails from "./questionairedetails/questioniredetails";

const AdminMatchMaking = () => {
  const navigate = useNavigate();
  const { data, isLoading } = useInfiniteMatchMakingList();
  const { columns, isModalOpen, selectedClient, closeModal } = useAdminColumns();

  const annotators = data?.pages.flatMap((page) => page.data) || [];

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h1 className="text-[#282828] text-[24px] font-bold">Match Making</h1>
      </div>

      <DataTable
        title="Forms"
        columns={columns}
        data={annotators}
        loading={false}
        disablePagination
      />

      {isModalOpen && selectedClient && (
        <QuestionireDetails
          isOpen={isModalOpen}
          onClose={closeModal}
          client={selectedClient}
          subscriptionId={selectedClient.subscriptionId ?? ""} // Fallback to empty string if null
        />
      )}
    </div>
  );
};

export default AdminMatchMaking;