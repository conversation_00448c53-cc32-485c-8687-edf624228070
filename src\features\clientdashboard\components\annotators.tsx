import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { getDashboardDataAnnonators, AllCooworkerAnoonatorClient } from "./dashboard_api/dashboard_api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";

// Define the type for annotator status based on accountStatus from API
type AnnotatorStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING" | string;

// Define the type for annotator data from API
type Annotator = {
  id: string;
  name: string;
  accountStatus: AnnotatorStatus;
};

// Map status values to colors
const statusColors: Record<string, string> = {
  ACTIVE: "bg-green-500",
  INACTIVE: "bg-red-500",
  SUSPENDED: "bg-red-500",
  PENDING: "bg-blue-500",
};

const AnnotatorList: React.FC = () => {
   const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const navigate = useNavigate();
  const [annotators, setAnnotators] = useState<Annotator[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        setLoading(true);  //AllCooworkerAnoonatorClient
        console.log("Fetching projects from API...");
                       const response = await (user === "CLIENT"
                         ? getDashboardDataAnnonators()
                         : AllCooworkerAnoonatorClient());
                       console.log("API Response:", response);
        if (response && response.data) {
          // Map the API response to our simplified Annotator type
          const mappedAnnotators = response.data.map((item: any) => ({
            id: item.id,
            name: item.name,
            accountStatus: item.accountStatus
          }));
          setAnnotators(mappedAnnotators);
        }
      } catch (error) {
        console.error("Error fetching annotators:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnotators();
  }, []);

  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg p-4 lg:w-full xl:w-full 2xl:w-full lg:h-[13rem] xl:h-[13rem] 2xl:h-[14rem] flex flex-col">
      <div className="flex justify-between items-center mb-3">
        <h2 className="lg:text-base xl:text-lg 2xl:text-xl font-semibold font-poppins">Annotators</h2>
        <button
          onClick={() => navigate("/dashboard/task-details/annotators")}
          className="lg:text-xs xl:text-sm 2xl:text-base font-semibold px-3 py-1 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all"
        >
          View All
        </button>
      </div>

      <ul className="CustomScroll overflow-y-auto pr-1 flex-grow">
        {loading ? (
          <li className="text-center lg:text-sm xl:text-base 2xl:text-lg text-gray-500">Loading...</li>
        ) : annotators.length > 0 ? (
          annotators.map((annotator, index) => (
            <li key={annotator.id || index} className="flex items-center gap-3 mb-3 last:mb-0">
              <img
                src={getAvatarUrl(annotator.name)}
                alt="avatar"
                className="lg:w-6 lg:h-6 xl:w-8 xl:h-8 2xl:w-10 2xl:h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="font-medium lg:text-xs xl:text-sm 2xl:text-base text-[#282828]">
                  {annotator.name.split(" ").slice(0, 1).join(" ")}
                </p>
              </div>
              <span
                className={`text-white lg:text-[10px] xl:text-xs 2xl:text-sm px-2 py-0.5 rounded-full capitalize ${
                  statusColors[annotator.accountStatus] || "bg-gray-500"
                }`}
              >
                {annotator.accountStatus?.toLowerCase() || "unknown"}
              </span>
            </li>
          ))
        ) : (
          <li className="text-center lg:text-sm xl:text-base 2xl:text-lg text-gray-500">No annotators found</li>
        )}
      </ul>
    </div>
  );
};

export default AnnotatorList;
