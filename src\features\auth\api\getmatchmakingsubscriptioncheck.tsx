import { customAxios } from "@/utils/axio-interceptor";

export const GetSubscriptionMatchmaking = async () => {
  try {
    const response = await customAxios.get("/v1/matchmaking/get");
    console.log("✅ GetSubscriptionMatchmaking API response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("❌ Error fetching subscription data:", {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
    });
    throw new Error(
      error.response?.data?.message || "Failed to fetch subscription data"
    );
  }
};