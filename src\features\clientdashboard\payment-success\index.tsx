// src/components/PaymentSuccess.tsx
import { motion, useAnimation, AnimatePresence } from "framer-motion";
import { CheckCir<PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const PaymentSuccess = () => {
  const [countdown, setCountdown] = useState(15);
  const navigate = useNavigate();
  const controls = useAnimation();

  useEffect(() => {
    // Start the main animation sequence
    const animationSequence = async () => {
      await controls.start("visible");
      await controls.start("celebrate");
    };
    animationSequence();

    // Redirect after 15 seconds
    const redirectTimer = setTimeout(() => {
      navigate("/dashboard");
    }, 15000);

    // Countdown timer
    const interval = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => {
      clearTimeout(redirectTimer);
      clearInterval(interval);
    };
  }, [controls, navigate]);

  return (
    <div className="relative overflow-hidden">
      {/* Background floating elements */}
      <AnimatePresence>
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: -50, x: Math.random() * 100 }}
            animate={{
              opacity: [0, 0.3, 0],
              y: [0, window.innerHeight],
              x: [0, Math.random() * 200 - 100],
              rotate: Math.random() * 360,
            }}
            transition={{
              duration: 5 + Math.random() * 10,
              delay: Math.random() * 3,
              repeat: Infinity,
              repeatDelay: Math.random() * 5,
            }}
            className="absolute text-3xl text-blue-200"
            style={{
              left: `${Math.random() * 100}%`,
              top: `-10%`,
            }}
          >
            <Sparkles />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Main content */}
      <div className="flex items-center justify-center p-4 min-h-screen relative z-10">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={controls}
          variants={{
            visible: { scale: 1, opacity: 1 },
            celebrate: { y: [0, -10, 0] },
          }}
          transition={{ duration: 0.5 }}
          className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 max-w-md w-full text-center relative overflow-hidden border border-white/20"
        >
          {/* Animated Checkmark */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{
              scale: 1,
              rotate: [0, 10, -10, 0],
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 15,
              delay: 0.3,
            }}
            className="flex justify-center mb-6"
          >
            <div className="relative">
              <motion.div
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatDelay: 1,
                }}
                className="absolute inset-0 bg-green-100 rounded-full"
              />
              <div className="w-24 h-24 bg-green-50 rounded-full flex items-center justify-center relative z-10">
                <CheckCircle className="text-green-500 text-5xl" />
              </div>
            </div>
          </motion.div>

          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-4xl font-bold text-gray-800 mb-3"
          >
            Payment Successful!
          </motion.h1>

          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="text-gray-600 mb-6 text-lg"
          >
            Thank you for your purchase. Your transaction is complete.
          </motion.p>

          {/* Countdown timer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="text-sm text-gray-500 mb-6"
          >
            Redirecting to dashboard in {countdown} seconds...
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="relative"
          >
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white font-medium py-4 px-8 rounded-xl shadow-lg w-full relative overflow-hidden group"
              onClick={() => navigate("/dashboard")}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  Go to Dashboard
                </motion.span>
              </span>
              <motion.div
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: 15, ease: "linear" }}
                className="absolute bottom-0 left-0 h-1 bg-white/50"
              />
            </motion.button>
          </motion.div>

          {/* Floating particles */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(30)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  y: -10,
                  x: Math.random() * 100 - 50,
                  opacity: 0,
                  rotate: Math.random() * 360,
                }}
                animate={{
                  y: [0, window.innerHeight * 0.5],
                  x: [0, Math.random() * 200 - 100],
                  opacity: [1, 0],
                }}
                transition={{
                  duration: 3 + Math.random() * 5,
                  delay: Math.random() * 2,
                  repeat: Infinity,
                  repeatDelay: Math.random() * 5,
                }}
                className="absolute text-xl"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `-10%`,
                  color: ["#FF577F", "#BF73E6", "#45ADE2", "#4ADE80"][
                    Math.floor(Math.random() * 4)
                  ],
                }}
              >
                {["✦", "✧", "❀", "✺", "✶"][Math.floor(Math.random() * 5)]}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Large background celebration elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: [0, 0.2, 0], scale: [0.5, 1.5, 2] }}
            transition={{
              duration: 4,
              delay: i * 0.5,
              repeat: Infinity,
              repeatDelay: 5,
            }}
            className="absolute rounded-full bg-gradient-to-r from-pink-300 to-purple-300"
            style={{
              width: "300px",
              height: "300px",
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              filter: "blur(60px)",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default PaymentSuccess;
