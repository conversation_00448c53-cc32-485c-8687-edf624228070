import { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, Image, FileText } from "lucide-react"; // Import icons for Photo and PDF
import { fetchChats } from "../apis/api";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";

const LeftChat = ({
  onSelectUser,
}: {
  onSelectUser: (user: {
    id: string;
    name: string;
    avatar: string;
    messages: { text: string; time?: string; fileType?: string }[];
    isGroup?: boolean;
    lastMessageTime?: string;
    conversationId?: string;
    groupId?: string;
    userId?: string;
  }) => void;
}) => {
  const [search, setSearch] = useState("");
  const [users, setUsers] = useState<
    {
      id: string;
      name: string;
      avatar: string;
      messages: { text: string; time?: string; fileType?: string }[];
      isGroup?: boolean;
      lastMessageTime?: string;
      conversationId?: string;
      groupId?: string;
      userId?: string;
    }[]
  >([]);

  const loggedInUser = useSelector(
    (state: RootState) => state.auth as { user: { id: string } }
  );

  useEffect(() => {
    const getChats = async () => {
      try {
        const response = await fetchChats();

        const usersData = response.data
          .map(
            (chat: {
              type: "dm" | "group";
              participants: Array<{ id: string; name: string }>;
              id: string;
              name: string;
              latestMessage?: {
                text: string;
                createdAt?: string;
                fileType?: string;
              };
              updatedAt?: string;
            }) => {
              if (chat.type === "dm") {
                const otherUser = chat.participants.find(
                  (participant: { id: string; name: string }) =>
                    participant.id !== loggedInUser?.user.id
                );

                // Format the last message time
                const lastMessageTime =
                  chat.latestMessage?.createdAt || chat.updatedAt;
                const formattedTime = lastMessageTime
                  ? new Date(lastMessageTime).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  : "";

                return {
                  id: chat.id,
                  name: otherUser?.name || "Unknown",
                  avatar: "", // You can enhance this if avatar is available later
                  messages: chat.latestMessage
                    ? [
                        {
                          text: chat.latestMessage.text || "",
                          time: formattedTime,
                          fileType: chat.latestMessage.fileType, // Include fileType
                        },
                      ]
                    : [],
                  lastMessageTime: formattedTime,
                  isGroup: false, // ✅ Explicitly set for DMs
                  conversationId: chat.id, // ✅ Store conversation ID
                  groupId: null,
                  userId: otherUser?.id, // ✅ Store user ID for reference
                };
              } else if (chat.type === "group") {
                // Format the last message time for groups
                const lastMessageTime =
                  chat.latestMessage?.createdAt || chat.updatedAt;
                const formattedTime = lastMessageTime
                  ? new Date(lastMessageTime).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })
                  : "";

                return {
                  id: chat.id, // ✅ This is correct for groups
                  name: chat.name,
                  avatar: "", // Optional: You can use a group icon here
                  messages: chat.latestMessage
                    ? [
                        {
                          text: chat.latestMessage.text || "",
                          time: formattedTime,
                          fileType: chat.latestMessage.fileType, // Include fileType
                        },
                      ]
                    : [],
                  isGroup: true,
                  lastMessageTime: formattedTime,
                  conversationId: null,
                  groupId: chat.id, // ✅ Store group ID
                  userId: null,
                };
              }

              return null;
            }
          )
          .filter(Boolean); // Remove null entries

        console.log("LeftChat transformed users data:", usersData); // ✅ Add debug log
        setUsers(usersData);
      } catch (error) {
        console.error("Error fetching chats:", error);
      }
    };
    getChats();
  }, [loggedInUser]);

  // Filtering the users based on the search input
  const filteredUsers = users.filter((user) =>
    user.name.toLowerCase().includes(search.toLowerCase())
  );

  // Function to render the last message preview
  const renderLastMessage = (message: { text: string; fileType?: string }) => {
    if (message.fileType) {
      if (message.fileType.toUpperCase() === "IMAGE") {
        return (
          <div className="flex items-center gap-1">
            <Image className="w-4 h-4 text-gray-500" />
            <span>Photo</span>
          </div>
        );
      } else if (message.fileType.toUpperCase() === "PDF") {
        return (
          <div className="flex items-center gap-1">
            <FileText className="w-4 h-4 text-red-500" />
            <span>PDF</span>
          </div>
        );
      }
    }
    return message.text || "";
  };

  return (
    <div className="w-full  h-full p-2">
      <div className="flex items-center gap-4 bg-[#F1F1F1] border rounded-md px-3 mb-4">
        <Search className="text-gray-400 w-6 h-6" />
        <input
          type="text"
          placeholder="Search"
          className="w-full p-2 bg-transparent outline-none placeholder:text-md"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      <ScrollArea className="h-[calc(100vh-242px)] flex-1">
        <div className="pr-2">
          {filteredUsers.map((user) => {
            const lastMsg = user?.messages?.[0];
            return (
              <div
                key={user.id}
                onClick={() => {
                  console.log("Selecting user:", user); // ✅ Add debug log
                  onSelectUser(user);
                }}
                className="flex items-center justify-between p-2 rounded-md hover:bg-gray-100 cursor-pointer"
              >
                <div className="flex gap-2 items-center">
                  {/* Avatar logic using dicebear */}
                  <img
                    src={user.avatar || getAvatarUrl(user.name)}
                    className="w-10 h-10 rounded-full"
                    alt="avatar"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-sm">
                      {user.name.split(" ").slice(0, 2).join(" ")}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {lastMsg ? renderLastMessage(lastMsg) : ""}
                    </p>
                  </div>
                </div>
                {user.lastMessageTime && (
                  <div className="text-xs text-gray-500 ml-2">
                    {user.lastMessageTime}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};

export default LeftChat;
