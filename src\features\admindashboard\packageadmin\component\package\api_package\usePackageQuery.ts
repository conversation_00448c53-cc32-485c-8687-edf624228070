// hooks/usePackageQuery.ts
import { useQuery } from "@tanstack/react-query";
import { getAllPackages } from "./api_package";

export const usePackageList = () => {
  return useQuery({
    queryKey: ["packageList"],
    queryFn: getAllPackages,
    // Optimize for faster refreshes
    staleTime: 0, // Consider data stale immediately
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchInterval: false, // Don't auto-refetch at intervals
  });
};
