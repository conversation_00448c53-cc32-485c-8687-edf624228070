// faqsapi.tsx
import { customAxios } from "@/utils/axio-interceptor"; 
interface CreateFAQProps {
  question: string;
  answer: string;
}

export const createFAQ = async ({ question, answer }: CreateFAQProps) => {
  try {
    const response = await customAxios.post("/v1/faq/create-faq", {
      question,
      answer,
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || "Failed to create FAQ");
  }
};


// ✅ Get all FAQs
export const getAllFAQ = async () => {
    try {
      const response = await customAxios.get("/v1/faq/get-all-faq");
      return response.data;
    } catch (error: any) {
      throw new Error(error?.response?.data?.message || "Failed to fetch FAQs");
    }
  };


  // Update FAQ
export const updateFAQ = async ({
    id,
    question,
    answer,
  }: {
    id: string;
    question: string;
    answer: string;
  }) => {
    try {
      const response = await customAxios.put(`/v1/faq/update-faq/${id}`, {
        question,
        answer,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error?.response?.data?.message || "Failed to update FAQ");
    }
  };


// ✅ Delete FAQ by ID
export const deleteFAQId = async (id: string) => {
    try {
      const response = await customAxios.delete(`/v1/faq/delete-faq/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error?.response?.data?.message || "Failed to delete FAQ");
    }
  };