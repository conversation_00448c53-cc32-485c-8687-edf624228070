import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";
import Logo from "@/assets/darklogo.png";
import { Outlet } from "react-router-dom";

const Layout = () => {
  return (
    <div className=" w-full flex items-center justify-center ">
      <div className="w-full  transition-transform duration-300">
        <div className="flex flex-col md:flex-row  justify-between   w-full">
          <div className="h-screen p-3 ">
            {/* <button
          className="text-pink-500 hover:text-pink-600   text-lg font-medium flex items-center"
        >
          <MoveLeft />
        </button> */}
            <img src={Logo} alt="Logo" className="w-44" />
          </div>

          <div className="flex flex-row w-full gap-14">
            {/* Left Side */}
            <div className="w-[45%] flex flex-col justify-center">
              <Outlet />
            </div>

            {/* Right Side Component */}
            <div className="w-[48%]">
              <AuthCommonComponent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout;
