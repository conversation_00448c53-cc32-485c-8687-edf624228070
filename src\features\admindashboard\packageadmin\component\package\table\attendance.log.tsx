// pages/PackagesAllList.tsx
import { usePackageList } from "../api_package/usePackageQuery";
import { useAdminColumns } from "./AdminColumn"; // you need to define columns
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";

import BrandedGlobalLoader from "@/components/loaders/loaders.one";

import { DataTable } from "@/components/globalfiles/data.table";

const PackagesAllList = () => {
  const navigate = useNavigate();
  const { data, isLoading } = usePackageList();
  const columns = useAdminColumns();

  if (isLoading) return <BrandedGlobalLoader isLoading />;

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex justify-between items-center mx-2">
        <div className="flex gap-4 items-center">
          <FaArrowLeft
            className="text-2xl text-[#FF577F] cursor-pointer"
            onClick={() => navigate(-1)}
          />
          <h1 className="text-[#282828] text-[24px]">Packages List</h1>
        </div>


      </div>

      <DataTable
        title="Packages"
        columns={columns}
        data={data?.data?.packages || []}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default PackagesAllList;
