import { PaginatedResponse } from "@/types/generics";
import { Annotator } from "@/types/onboarding.types";
import { customAxios } from "@/utils/axio-interceptor";
import { QueryFunctionContext } from "@tanstack/react-query";

export const getCoworkerList = async (
  context: QueryFunctionContext
): Promise<PaginatedResponse<Annotator>> => {
  const page = (context.pageParam ?? 1) as number;

  const res = await customAxios.get(`/v1/coworker/get-coworkers`, {
    params: {
      page: page,
    },
  });
  return res.data.data;
};

export const inviteCoworker = async (data: {
  email: string;
  notify: boolean;
  permission: "VIEW" | "EDIT";
}) => {
  const response = await customAxios.post("/v1/coworker/invite-coworker", {
    email: data.email,
    notify: data.notify,
    permissionRole: data.permission,
  });
  return response.data;
};

export const updateCoworkerPermission = async (data: {
  id: string;
  permission: "VIEW" | "EDIT";
}) => {
  const response = await customAxios.patch(
    `/v1/coworker/${data.id}/permission`,
    {
      permissionRole: data.permission,
    }
  );
  return response.data;
};

export const resendCoworkerInvite = async (id: string) => {
  const response = await customAxios.post(`/v1/coworker/${id}/resend-invite`);
  return response.data;
};
