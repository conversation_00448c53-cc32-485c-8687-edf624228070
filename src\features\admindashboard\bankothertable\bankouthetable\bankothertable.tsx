import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./bankcolumn";
import { historyuseAdminColumns } from "../historybankdetails/historybankcolumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useEffect, useState } from "react";
import { AdminGetBanktranferList, AdminGetVerifyedBanktransfer } from "../banktransferapi/bank_transfer_api";
import { BankDetailsType, BankTransferHistoryType } from "./bankdetails.type";

// Add this type for verified history data
// interface VerifiedHistoryType {
//   id: string;
//   name: string;
//   transactionId: string;
//   transactionDate: string;
//   transferedAccNo: string;
//   bankHolderName: string;
//   amount: number;
//   accountNumber: string;
//   status: string;
//   screenshotUrl: string;
//   verifiedBy: string;
//   verifiedAt: string;
// }

const BankOtherTable = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<BankDetailsType[]>([]);
  // Update the state and fetch function for history data
  const [historyData, setHistoryData] = useState<BankTransferHistoryType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);

 const fetchData = async () => {
  try {
    setIsLoading(true);
    const response = await AdminGetBanktranferList();
    const transformedData = response.data.payments.map((payment: any) => ({
      id: payment.id,
      paymentId: payment.paymentId,
      name: payment.user?.name || '',
      transactionId: payment.transactionId,
      transactionDate: payment.createdAt,
      transferedAccNo: payment.transferedAccNo, // Ensure this matches API
      bankHolderName: payment.bankHolderName,
      amount: payment.amount,
      accountNumber: payment.accountNumber,
      status: payment.status,
      screenshotUrl: payment.screenshotUrl,
    })) as BankDetailsType[];
    setData(transformedData);
  } catch (error) {
    console.error("Error fetching bank transfer data:", error);
  } finally {
    setIsLoading(false);
  }
};


  const fetchHistoryData = async () => {
  try {
    setIsHistoryLoading(true);
    const response = await AdminGetVerifyedBanktransfer();

    // Transform the rawPayments data to match our table structure
    const transformedData: BankTransferHistoryType[] = response.data.rawPayments.map((payment: any) => ({
      id: payment.id,
      paymentId: payment.paymentId,
      name: payment.user?.name || '',
      transactionId: payment.transactionId,
      transactionDate: payment.createdAt,
      transferedAccNo: payment.transferedAccNo,
      bankHolderName: payment.bankHolderName,
      amount: payment.amount,
      accountNumber: payment.accountNumber,
      status: payment.status,
      screenshotUrl: payment.screenshotUrl,
      verifiedBy: payment.verifiedBy?.name || "Unknown",
      verifiedAt: payment.verifiedAt || "-",
      adminNotes: payment.adminNotes || null,
    }));

    setHistoryData(transformedData);
  } catch (error) {
    console.error("Error fetching verified bank transfer history:", error);
  } finally {
    setIsHistoryLoading(false);
  }
};

  useEffect(() => {
    fetchData();
    fetchHistoryData();
  }, []);

  return (
    <div className="bg-white h-full space-y-4 p-4">
      <div className="flex flex-wrap gap-3 items-center">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h1 className="text-[#282828] text-[24px] font-bold">
          Bank Transactions
        </h1>
      </div>

      {isLoading ? (
        <BrandedGlobalLoader isLoading={true} />
      ) : (
        <div className="rounded-lg">
          <DataTable
            title="Pending Transactions"
            columns={useAdminColumns(fetchData)}
            data={data}
            loading={false}
             disablePagination
          />
        </div>
      )}

      <div className="flex flex-col justify-start items-start">
        <p className="text-[#282828] text-[24px] font-bold">Bank Transfer History</p>
      </div>

      {isHistoryLoading ? (
        <BrandedGlobalLoader isLoading={true} />
      ) : (
        <div className="rounded-lg shadow-sm">
          <DataTable
            title="Verified Transactions"
            columns={historyuseAdminColumns(fetchHistoryData)}
            data={historyData}
            loading={false}
             disablePagination
          />
        </div>
      )}
    </div>
  );
};

export default BankOtherTable;