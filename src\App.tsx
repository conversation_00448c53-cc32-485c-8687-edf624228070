import { useEffect, useState } from "react";
import "./App.css";
import { AppRoutes } from "./routes";
import { SocketProvider } from "./socket/socket";
import logo from "@/assets/darklogo.png";
import { <PERSON>ptop, Smartphone, Tablet } from "lucide-react";
import { RoleProvider } from "./context/roles.context";
import { useAppSelector } from "./store/hooks/reduxHooks";
import { RootState } from "./store";
import { SelectionProvider } from "./context/matchmaking.context";

function App() {
  const [isAllowedDevice, setIsAllowedDevice] = useState(true);
  const role = useAppSelector(
    (state: RootState) => state.user.profile?.permissions
  );

  useEffect(() => {
    const handleResize = () => {
      // Only allow devices with width >= 1024px (laptops and desktops)
      setIsAllowedDevice(window.innerWidth >= 1024);
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (!isAllowedDevice) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-white to-gray-100 text-center px-6 py-10">
        <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8 border border-gray-200">
          <div className="flex justify-center mb-6">
            <img src={logo} alt="Logo" className="h-12" />
          </div>

          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Desktop Only Platform
          </h2>

          <div className="flex justify-center gap-4 my-6">
            <div className="flex flex-col items-center opacity-40">
              <div className="bg-red-100 p-3 rounded-full">
                <Smartphone className="h-6 w-6 text-red-500" />
              </div>
              <span className="text-xs mt-1 text-gray-500">Mobile</span>
            </div>
            <div className="flex flex-col items-center opacity-40">
              <div className="bg-red-100 p-3 rounded-full">
                <Tablet className="h-6 w-6 text-red-500" />
              </div>
              <span className="text-xs mt-1 text-gray-500">Tablet</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <Laptop className="h-6 w-6 text-green-500" />
              </div>
              <span className="text-xs mt-1 text-gray-500">Desktop</span>
            </div>
          </div>

          <p className="text-gray-600 mb-4">
            This platform is optimized for desktop and laptop devices with screen sizes 1024px and above.
          </p>

          <div className="p-4 bg-blue-50 rounded-lg text-sm text-blue-700">
            Please access this platform from a desktop or laptop device for the best experience.
          </div>
        </div>

        <p className="text-gray-500 text-xs mt-6">
          Supported screen sizes: 1024px, 1440px, and 2560px (4K)
        </p>
      </div>
    );
  }

  return (
    <SocketProvider>
      {/* <AppRoutes /> */}
      <SelectionProvider>
        <RoleProvider role={role}>
          {" "}
          <AppRoutes />{" "}
        </RoleProvider>
      </SelectionProvider>
    </SocketProvider>
  );
}

export default App;
