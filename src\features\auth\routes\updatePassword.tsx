import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { ClientResetPassword } from "../api/client-api";
import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";
import { useResponsive } from "@/hooks/use-responsive";
import { toast } from "react-toastify"; // Import react-toastify
import logo from "@/assets/darklogo.png";
import { BackButton } from "@/_components/common";
import { Eye, EyeOff } from "lucide-react";

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const { isLaptopMd, isLaptopLg } = useResponsive();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState({ password: "", confirmPassword: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Check for required state on component mount
  useEffect(() => {
    const resetToken = sessionStorage.getItem("resetToken");
    const resetEmail = sessionStorage.getItem("resetEmail");

    if ((!state?.resetToken && !resetToken) || (!state?.email && !resetEmail)) {
      navigate("/auth/forget-password", { replace: true });
    }
  }, [navigate, state]);

  const getStyles = () => {
    if (isLaptopLg) {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[20%] flex items-start justify-center pt-8",
        logoSize: "w-[155px] h-[30px]",
        contentContainer: "flex flex-row gap-24 w-[80%] items-center",
        formContainer:
          "w-[35%] p-10 space-y-10 rounded-lg shadow-2xl bg-white flex flex-col",
        backButton:
          "text-pink-500 hover:text-pink-600 text-xl font-medium flex items-center mb-6",
        title: "text-4xl font-bold font-inter text-[#282828]",
        label: "block text-lg font-inter text-gray-700 font-medium mb-3",
        input:
          "w-full p-4 bg-[#F9EFEF] rounded-lg focus:outline-none transition-all",
        inputContainer: "relative",
        errorText: "text-red-500 text-sm mt-2",
        button:
          "w-full p-5 text-lg bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md",
        backgroundContainer: "w-[50%] h-screen",
      };
    } else if (isLaptopMd) {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[15%] flex items-start justify-center pt-6",
        logoSize: "w-[155px] h-[30px]",
        contentContainer: " flex flex-row gap-5 w-[85%] items-center",
        formContainer:
          "w-[45%] p-8 border space-y-8 rounded-lg shadow-2xl bg-white flex flex-col justify-center",
        backButton:
          "text-pink-500 hover:text-pink-600 text-lg font-medium flex items-center mb-5",
        title: "text-3xl font-bold font-inter text-[#282828] mb-6",
        label: "block text-base font-inter text-gray-700 font-medium mb-2.5",
        input:
          "w-full p-3.5 bg-[#F9EFEF] rounded-lg focus:outline-none transition-all",
        inputContainer: "relative",
        errorText: "text-red-500 text-sm mt-1.5",
        button:
          "w-full p-4 text-base bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md",
        backgroundContainer: "w-[48%] h-screen",
      };
    } else {
      return {
        container: "w-full flex flex-row items-center justify-between h-screen",
        logoContainer: "h-screen w-[12%] flex items-start justify-center pt-4",
        logoSize: "w-[155px] h-[30px]",
        contentContainer: "flex flex-row gap-20 w-[88%] items-center",
        formContainer:
          "w-[45%] p-6 space-y-6 rounded-lg shadow-2xl bg-white flex flex-col justify-center",
        backButton:
          "text-pink-500 hover:text-pink-600 text-lg font-medium flex items-center mb-4",
        title: "text-2xl font-bold font-inter text-[#282828] mb-5",
        label: "block text-base font-inter text-gray-700 font-medium mb-2",
        input:
          "w-full p-3 bg-[#F9EFEF] rounded-lg focus:outline-none transition-all",
        inputContainer: "relative",
        errorText: "text-red-500 text-xs mt-1",
        button:
          "w-full p-4 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md",
        backgroundContainer: "w-[45%] h-screen",
      };
    }
  };

  const styles = getStyles();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate inputs
    const errors = { password: "", confirmPassword: "" };
    let isValid = true;

    if (password.length < 8) {
      errors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setError(errors);
    if (!isValid) return;

    // Disable button immediately
    setIsSubmitting(true);

    try {
      const response = await ClientResetPassword(
        state.email,
        state.resetToken,
        password
      );

      // Show success toast
      toast.success("Password reset successfully! Redirecting to login...", {
        position: "top-right",
        autoClose: 2000,
      });

      // Clear any stored tokens
      localStorage.removeItem("token");

      // Redirect after 2 seconds
      setTimeout(() => {
        navigate("/auth/login");
      }, 2000);

      console.log("Password reset response:", response);
    } catch (err: any) {
      console.error("Password reset error:", err);
      // Show error toast
      toast.error(
        err.response?.data?.message ||
          "Failed to reset password. Please try again.",
        {
          position: "bottom-right",
          autoClose: 3000,
        }
      );
    } finally {
      // Re-enable button after response
      setIsSubmitting(false);
    }
  };

  if (!state?.email || !state?.resetToken) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p>Redirecting to forgot password page...</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.logoContainer}>
        <img src={logo} alt="Logo" className={styles.logoSize} />
      </div>

      <div className={styles.contentContainer}>
        <div className={styles.formContainer}>
          <div className="flex flex-row gap-2 items-center justify-start">
            <BackButton />
            <h2 className={styles.title}>Reset Password</h2>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className={styles.label}>New Password</label>
              <div className={styles.inputContainer}>
                <div className="border-gradient rounded-xl">
                  <input
                    type={showPassword ? "text" : "password"}
                    className={`${styles.input} ${
                      error.password ? "border-red-500" : ""
                    } pr-12`}
                    placeholder="Enter new password"
                    autoComplete="new-password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-4"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <Eye className="h-5 w-5 text-gray-500" />
                    ) : (
                      <EyeOff className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                </div>
                {error.password && (
                  <p className={styles.errorText}>{error.password}</p>
                )}
              </div>
            </div>

            <div className="mb-8">
              <label className={styles.label}>Confirm Password</label>
              <div className={styles.inputContainer}>
                <div className="border-gradient rounded-xl">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    className={`${styles.input} ${
                      error.confirmPassword ? "border-red-500" : ""
                    } pr-12`}
                    placeholder="Confirm new password"
                    autoComplete="new-password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-4"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <Eye className="h-5 w-5 text-gray-500" />
                    ) : (
                      <EyeOff className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                </div>
                {error.confirmPassword && (
                  <p className={styles.errorText}>{error.confirmPassword}</p>
                )}
              </div>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`${styles.button} ${
                isSubmitting ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isSubmitting ? "Processing..." : "Reset Password"}
            </button>
          </form>
        </div>

        <div className={styles.backgroundContainer}>
          <AuthCommonComponent />
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;