import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useState, useEffect } from "react";
import { coordinatorChangeShift } from "./coordinatorshift_api/coordinatorshift_api";
import { toast } from "react-toastify";

interface ShiftChangeProps {
    onClose: () => void;
    onSuccess: () => void;
    annotatorId?: string; // Add annotatorId prop
}

export default function ShiftChange({ onClose, onSuccess, annotatorId }: ShiftChangeProps) {
    const [fromTime, setFromTime] = useState("");
    const [toTime, setToTime] = useState("");
    const [note, setNote] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setIsOpen(true);
        // Validate annotatorId when component mounts
        if (!annotatorId) {
            console.error("Error: Annotator ID is missing.");
            setError("Annotator ID is missing. Please select an annotator first.");
        } else {
            setError(null);
        }
    }, [annotatorId]);

    const handleClose = () => {
        setIsOpen(false);
        setTimeout(onClose, 300);
    };

    const generateTimeSlots = () => {
        const times = [];
        for (let hour = 0; hour < 24; hour++) {
            for (let min = 0; min < 60; min += 30) {
                const formattedHour = hour.toString().padStart(2, "0");
                const formattedMin = min.toString().padStart(2, "0");
                times.push(`${formattedHour}:${formattedMin}`);
            }
        }
        return times;
    };

    const timeOptions = generateTimeSlots();

    const validateTimes = () => {
        if (!fromTime || !toTime) {
            return "Please select both From and To time.";
        }

        // Convert times to minutes for easy comparison
        const [fromHour, fromMinute] = fromTime.split(':').map(Number);
        const [toHour, toMinute] = toTime.split(':').map(Number);
        
        const fromMinutes = fromHour * 60 + fromMinute;
        const toMinutes = toHour * 60 + toMinute;
        
        // Check if "to" time is after "from" time
        if (toMinutes <= fromMinutes) {
            return "End time must be after start time.";
        }
        
        // Check if shift is at least 1 hour
        if (toMinutes - fromMinutes < 60) {
            return "Shift must be at least 1 hour long.";
        }
        
        // Check if shift is not too long (e.g., max 12 hours)
        if (toMinutes - fromMinutes > 12 * 60) {
            return "Shift cannot be longer than 12 hours.";
        }
        
        return null;
    };

    const handleSubmit = async () => {
        setError(null);
        
        if (!annotatorId) {
            setError("Annotator ID is missing. Please select an annotator first.");
            return;
        }
        
        const timeError = validateTimes();
        if (timeError) {
            setError(timeError);
            return;
        }

        try {
            setIsSubmitting(true);
            await coordinatorChangeShift({
                annotatorId: annotatorId,
                availableFrom: fromTime,
                availableTo: toTime,
                note: note // Will be used when backend supports it
            });
            
            toast.success("Shift change requested successfully!");
            handleClose();
            setTimeout(() => {
                onSuccess();
            }, 300);
        } catch (error) {
            console.error("Error submitting shift change:", error);
            setError("Failed to submit shift change. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={handleClose}></div>

            <div
                className={`fixed top-0 right-0 h-screen w-[25rem] flex flex-col justify-between bg-white shadow-lg p-6 z-50 transform transition-transform duration-300 ${isOpen ? "translate-x-0" : "translate-x-full"
                    }`}
            >
                <div>
                    <div className="flex flex-row justify-between text-lg font-bold text-gray-800 mb-4 border-b pb-2">
                        <h2 className="">Shift Change</h2>
                        <span onClick={handleClose} className="cursor-pointer hover:text-[#585858] text-[#727272]"><X /></span>
                    </div>
                    
                    {/* Error message */}
                    {error && (
                        <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded-md text-xs">
                            {error}
                        </div>
                    )}
                    
                    <div className="flex flex-row gap-x-6">
                        <div className="mb-4">
                            <label className="block text-sm font-medium">From: <span className="text-red-500">*</span></label>
                            <div className="border-gradient rounded-[8px]">
                                <select
                                    value={fromTime}
                                    onChange={(e) => {
                                        setFromTime(e.target.value);
                                        setError(null); // Clear error when user makes a change
                                    }}
                                    className="w-full focus:outline-none focus:ring-0 bg-[#F9EFEF] border p-2 rounded-md text-[13px] text-[#5E5E5E] mr-14"
                                >
                                    <option value="">Select time</option>
                                    {timeOptions.map((time, index) => (
                                        <option key={index} value={time}>{time}</option>
                                    ))}
                                </select>
                            </div>
                        </div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium">To: <span className="text-red-500">*</span></label>
                            <div className="border-gradient rounded-[8px]">
                                <select
                                    value={toTime}
                                    onChange={(e) => {
                                        setToTime(e.target.value);
                                        setError(null); // Clear error when user makes a change
                                    }}
                                    className="w-full bg-[#F9EFEF] text-[#5E5E5E] focus:outline-none focus:ring-0 border p-2 rounded-md text-[13px] mr-14"
                                >
                                    <option value="">Select time</option>
                                    {timeOptions.map((time, index) => (
                                        <option key={index} value={time}>{time}</option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div className="mb-4 flex flex-col justify-start items-start">
                        <label className="block text-sm font-medium">Note: <span className="text-gray-400">(Optional)</span></label>
                        <div className="border-gradient rounded-[8px] w-full">
                            <div className="bg-[#F9EFEF] rounded-[6px] p-1">
                                <textarea
                                    value={note}
                                    onChange={(e) => setNote(e.target.value)}
                                    className="w-full p-2 placeholder:text-[#5E5E5E] text-[13px] bg-transparent rounded-md h-20 max-h-[10rem] focus:outline-none"
                                    placeholder="Enter note (optional)"
                                ></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex flex-row items-end justify-end gap-4 mt-4">
                    <button 
                        onClick={handleClose} 
                        className="px-6 py-2 border-gradient rounded-md text-gray-600"
                        disabled={isSubmitting}
                    >
                        Cancel
                    </button>
                    <Button
                        onClick={handleSubmit}
                        variant={"gradient"}
                        className="px-6 py-2 to-[#45ADE2] text-white rounded-md"
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? "Submitting..." : "Request"}
                    </Button>
                </div>
            </div>
        </>
    );
}
