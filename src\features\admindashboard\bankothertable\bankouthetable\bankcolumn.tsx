"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { BankDetailsType } from "./bankdetails.type";
import { toast } from "react-toastify";
import { ImageModal } from "../banktransferapi/ImageModal";
import { useState } from "react";
import { VerifyPostBanktranfer } from "../banktransferapi/bank_transfer_api";

const formatDate = (dateStr?: string | null) => {
  if (!dateStr) return "-";
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "-"; // Invalid date check

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = String(date.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error, dateStr);
    return "-";
  }
};

// Format currency with Indian Rupee symbol
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    maximumFractionDigits: 0,
  }).format(amount);
};

// useAdminColumns hook definition
export const useAdminColumns = (refreshData: () => void): ColumnDef<BankDetailsType>[] => {
  return [
    {
      // user.name
      accessorKey: "name", 
      header: () => (
        <div
          className="w-[120px] text-[14px]font-medium cursor-pointer"
          // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Client
        </div>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-normal">
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "transactionId",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
          // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Transaction ID
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">{row.original.transactionId}</div>
      ),
    },
    {
      accessorKey: "transactionDate",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
       //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Transaction Date
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {formatDate(row.original.transactionDate)}
        </div>
      ),
    },
    {
      accessorKey: "transferedAccNo",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
       //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          TransferAccount No.
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.transferedAccNo}
        </div>
      ),
    },
    {
      accessorKey: "bankHolderName",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          BankHolder Name
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.bankHolderName}
        </div>
      ),
    },
    {
      accessorKey: "amount",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Amount
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {formatCurrency(row.original.amount)}
        </div>
      ),
    },
    
    {
      accessorKey: "accountNumber",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Account Number
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center text-[14px] font-normal">
          {row.original.accountNumber}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
       //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.original.status;
        let statusColor = "";

        // Set color based on payment status
        switch (status.toLowerCase()) {
          case "paid":
            statusColor = "text-green-600 bg-green-600";
            break;
          case "pending":
            statusColor = "text-white  bg-yellow-600 py-2 text-center rounded-lg";
            break;
          case "failed":
            statusColor = "text-red-600";
            break;
          default:
            statusColor = "text-gray-600";
        }

        return (
          <div className={`text-center text-[14px] font-normal ${statusColor}`}>
            {status}
          </div>
        );
      },
    },
     {
      accessorKey: "screenshotUrl",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Proof Image
        </Button>
      ),
      cell: ({ row }) => {
        const [isModalOpen, setIsModalOpen] = useState(false);
        const imageUrl = row.original.screenshotUrl;

        return (
          <div className="flex flex-row justify-center items-center text-[14px] font-normal">
            {imageUrl ? (
              <>
                <img
                  src={imageUrl}
                  alt="Proof Thumbnail"
                  className="w-10 h-10 object-cover cursor-pointer rounded"
                  onClick={() => setIsModalOpen(true)}
                />
                <ImageModal
                  isOpen={isModalOpen}
                  imageUrl={imageUrl}
                  onClose={() => setIsModalOpen(false)}
                />
              </>
            ) : (
              "-"
            )}
          </div>
        );
      },
    },
    
   {
      id: "actions",
      header: () => (
        <div className="text-center text-[14px] font-medium">Actions</div>
      ),
      cell: ({ row}) => {
        const handleConfirm = async () => {
          try {
            await VerifyPostBanktranfer(
              row.original.id,
              row.original.paymentId,
              "VERIFIED",
              "Payment confirmed by admin"
            );
            toast.success(`Payment of ${formatCurrency(row.original.amount)} confirmed!`);
            refreshData();
          } catch (error) {
            console.error("Error confirming payment:", error);
            toast.error("Failed to confirm payment");
          }
        };

        const handleNotConfirm = async () => {
          try {
            await VerifyPostBanktranfer(
              row.original.id,
              row.original.paymentId,
              "REJECTED",
              "Payment rejected by admin"
            );
            toast.success(`Payment of ${formatCurrency(row.original.amount)} rejected!`);
            // Trigger table data refresh
          refreshData();
          }
            
            catch (error) {
            console.error("Error rejecting payment:", error);
            toast.error("Failed to reject payment");
          }
        };

        return (
          <div className="flex flex-row gap-2 justify-center">
            <Button
              variant="gradient"
              onClick={handleConfirm}
              className="px-4 py-2 text-[14px] font-normal text-white"
              disabled={row.original.status.toLowerCase() !== "pending"}
            >
              Confirm
            </Button>
            <Button
              variant="outline"
              onClick={handleNotConfirm}
              className="px-4 py-2 border-[#FF577F] text-[14px] font-normal text-[#FF577F] border hover:text-[#e7476c]"
              disabled={row.original.status.toLowerCase() !== "pending"}
            >
              Reject
            </Button>
          </div>
        );
      },
    },
  ];
};
