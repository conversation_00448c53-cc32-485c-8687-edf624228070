import { useEffect, useRef, useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Eye, EyeOff } from "lucide-react";
import { DialogClose } from "@radix-ui/react-dialog";
import { usePackageList } from "../api/query";
import { useAddUserMutation } from "../api/mutation";
import { useQueryClient } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

interface FormValues {
  name: string;
  lastname: string;
  email: string;
  domain: string;
  customDomain: string;
  role: string;
  packageId: string;
  password: string | null;
  passwordType: "auto" | "manual";
}

interface OnboardFormProps {
  userData?: Partial<FormValues>;
  onSuccess?: () => void;
}

// Define allowed TLDs
const allowedTLDs = [".com", ".in", ".co", ".org", ".net"]; //add more for requirement

const domainSchema = z
  .string()
  .min(1, "Domain is required")
  .startsWith("@", "Domain must start with @")
  .refine(
    (val) => {
      const domainPart = val.substring(1); // Remove @
      if (!domainPart.includes(".")) {
        return false; // Must contain at least one dot
      }
      const parts = domainPart.split(".");
      const tld = parts.pop();
      return tld && allowedTLDs.includes(`.${tld.toLowerCase()}`);
    },
    {
      message: `Invalid domain. Must end with one of: ${allowedTLDs.join(
        ", "
      )}`,
    }
  )
  .refine(
    (val) => {
      const domainPart = val.substring(1);
      return /^([a-z0-9-]+\.)+[a-z0-9-]+$/.test(domainPart);
    },
    {
      message: "Invalid domain format (e.g., @example.com)",
    }
  )
  .transform((val) => val.toLowerCase());

// Form validation schema
const formSchema = z.object({
  name: z.string().min(1, "First name is required"),
  lastname: z.string().optional(),
  email: z
    .string()
    .min(1, "Primary name is required")
    .regex(/^[a-zA-Z0-9]+(?:[._-][a-zA-Z0-9]+)*$/, {
      message:
        "Primary name can only contain letters, numbers, dots, underscores or hyphens",
    }),
  domain: z.string().min(1, "Domain selection is required"),
  customDomain: z
    .string()
    .optional()
    .superRefine((val, ctx) => {
      // Get the current domain value from the form
      const formValues = ctx as unknown as {
        _cached: { form: { domain: string } };
      };
      const currentDomain = formValues?._cached?.form?.domain;

      if (currentDomain === "domain3") {
        if (!val) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message:
              "Custom domain is required when selecting custom domain option",
          });
          return;
        }
        const result = domainSchema.safeParse(val);
        if (!result.success) {
          result.error.issues.forEach((issue) => {
            ctx.addIssue(issue);
          });
        }
      }
    }),
  role: z.string().min(1, "Role is required"),
  packageId: z.string().optional(),
  password: z.string().nullable(),
  passwordType: z.enum(["auto", "manual"]),
});

const OnboardForm: React.FC<OnboardFormProps> = ({ userData, onSuccess }) => {
  const { data } = usePackageList();
  const { mutate: addUser } = useAddUserMutation();
  const queryClient = useQueryClient();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      lastname: "",
      email: "",
      domain: "",
      customDomain: "",
      role: "",
      packageId: "",
      password: null,
      passwordType: "auto",
    },
  });

  // Prefill form when editing
  useEffect(() => {
    if (userData) {
      reset({
        name: userData.name || "",
        lastname: userData.lastname || "",
        email: userData.email || "",
        domain: userData.domain || "",
        customDomain: "",
        role: userData.role || "",
        packageId: userData.packageId || "",
        password: userData.password || null,
        passwordType: userData.password ? "manual" : "auto",
      });
    }
  }, [userData, reset]);

  const role = watch("role");
  const passwordType = watch("passwordType");
  const email = watch("email");
  const domain = watch("domain");
  const customDomain = watch("customDomain");

  const generatePassword = () => {
    const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lower = "abcdefghijklmnopqrstuvwxyz";
    const number = "0123456789";
    const special = "!@";
    const allChars = upper + lower + number + special;

    // Ensure at least one of each required character type
    let pwd = [
      upper[Math.floor(Math.random() * upper.length)],
      lower[Math.floor(Math.random() * lower.length)],
      number[Math.floor(Math.random() * number.length)],
      special[Math.floor(Math.random() * special.length)],
    ];

    // Generate additional characters to reach minimum length of 8
    for (let i = pwd.length; i < 8; i++) {
      pwd.push(allChars[Math.floor(Math.random() * allChars.length)]);
    }

    // Shuffle the password array
    pwd = pwd.sort(() => Math.random() - 0.5);

    // Join and set the password with validation
    setValue("password", pwd.join(""), { shouldValidate: true });
  };

  const validatePrimaryName = (value: string) => {
    if (!value) return "Primary name is required";
    if (value.includes("@")) return "Primary name should not contain @ symbol";
    if (!/^[a-zA-Z0-9]+(?:[._-][a-zA-Z0-9]+)*$/.test(value)) {
      return "Primary name can only contain letters, numbers, dots, underscores or hyphens";
    }
    return true;
  };

  const onSubmit = (data: FormValues) => {
    const {
      name,
      lastname,
      email,
      domain,
      customDomain,
      role,
      packageId,
      password,
    } = data;

    // Use customDomain if domain is "domain3" (custom domain)
    let finalDomain;
    if (domain === "domain3") {
      // Ensure the custom domain starts with @
      finalDomain = customDomain.startsWith("@")
        ? customDomain
        : `@${customDomain}`;
    } else if (domain === "domain1") {
      finalDomain = "@macgence.com";
    } else if (domain === "domain2") {
      finalDomain = "@macgence.in";
    } else {
      finalDomain = domain;
    }

    // Combine primary name and domain to create the complete email
    const completeEmail = `${email}${finalDomain}`;

    const payload = {
      name: `${name}`,
      lastname: `${lastname}`,
      email: completeEmail, // Use the combined email
      domain: finalDomain,
      role,
      packageId,
      password: passwordType === "auto" ? null : password,
    };

    setIsSubmitting(true);
    addUser(payload, {
      onSuccess: () => {
        // Manually refresh the onboarding data
        queryClient.invalidateQueries({ queryKey: ["onboarding"] });

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Close the modal
        closeRef.current?.click();
        setIsSubmitting(false);
      },
      onError: () => {
        setIsSubmitting(false);
      },
    });
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full lg-only:max-w-2xl    xl-only:max-w-4xl 2xl-only:max-w-5xl lg-only:p-1 xl-only:p-2 2xl-only:p-8"
    >
      <h2 className="lg-only:text-base xl-only:text-xl 2xl-only:text-2xl font-semibold lg-only:mb-2 xl-only:mb-4 2xl-only:mb-5">
        User Information
      </h2>

      <div className="grid grid-cols-1 lg-only:grid-cols-2 xl-only:grid-cols-2 2xl-only:grid-cols-2 lg-only:gap-2 xl-only:gap-4 2xl-only:gap-5">
        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base  2xl-only:text-lg">
            First Name *
          </Label>
          <div className="border-gradient rounded-xl p-[2px]">
            <div className="bg-[#F9EFEF] rounded-xl">
              <Input
                {...register("name")}
                placeholder="Enter first name"
                className="bg-transparent text-[#5E5E5E] w-full lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
              />
            </div>
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Last Name
          </Label>
          <div className="border-gradient rounded-xl p-[2px]">
            <div className="bg-[#F9EFEF] rounded-xl">
              <Input
                {...register("lastname")}
                placeholder="Enter last name"
                className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
              />
            </div>
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Primary Email *
          </Label>
          <div className="border-gradient rounded-xl p-[2px]">
            <div className="bg-[#F9EFEF] rounded-xl">
              <Input
                {...register("email", {
                  validate: validatePrimaryName,
                  onChange: (e) => {
                    // Remove any @ symbols if user tries to enter them
                    const value = e.target.value.replace(/@/g, "");
                    setValue("email", value);
                  },
                })}
                placeholder="Enter primary email"
                className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
              />
            </div>
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1.5 2xl-only:py-2 h-[5rem]">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Domain *
          </Label>
          {domain === "domain3" ? (
            <div className="lg-only:mt-1 xl-only:mt-1.5 2xl-only:mt-2">
              <div className="border-gradient rounded-lg">
                <Input
                  {...register("customDomain", {
                    required: "Please enter a valid domain",
                    validate: {
                      validFormat: (value) => {
                        if (!value) return "Please enter a domain";
                        if (!value.startsWith("@"))
                          return "Domain must start with @";

                        const domainPart = value.substring(1);
                        if (!domainPart.includes(".")) {
                          return "Domain must contain a period (.)";
                        }

                        const parts = domainPart.split(".");
                        const tld = parts[parts.length - 1];

                        if (!allowedTLDs.includes(`.${tld.toLowerCase()}`)) {
                          return `Domain must end with: ${allowedTLDs.join(
                            ", "
                          )}`;
                        }

                        if (!/^[a-z0-9-]+(\.[a-z0-9-]+)*$/.test(domainPart)) {
                          return "Please enter a correct domain (e.g., @example.com)";
                        }

                        return true;
                      },
                    },
                  })}
                  placeholder="Enter Domain (@example.com)"
                  className="bg-[#F9EFEF] text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
                  onChange={(e) => {
                    let value = e.target.value;

                    // Auto-add @ if missing and no @ present
                    if (
                      value &&
                      !value.startsWith("@") &&
                      !value.includes("@")
                    ) {
                      value = `@${value}`;
                    }

                    // Remove any @ symbols except the first one
                    const atIndex = value.indexOf("@");
                    if (atIndex > 0) {
                      value = `@${value.replace(/@/g, "")}`;
                    }

                    // Convert to lowercase
                    value = value.toLowerCase();

                    setValue("customDomain", value, { shouldValidate: true });
                  }}
                  onBlur={() => {
                    if (customDomain) {
                      setValue("customDomain", customDomain, {
                        shouldValidate: true,
                      });
                    }
                  }}
                />
              </div>
              {errors.customDomain && (
                <p className="text-red-500 lg-only:text-[10px] xl-only:text-[12px] 2xl-only:text-[14px] mt-1">
                  {errors.customDomain.message}
                </p>
              )}
              <div className="flex flex-col justify-between lg-only:mt-0.5 xl-only:mt-1.5 2xl-only:mt-2">
                <span className="lg-only:text-[8px] xl-only:text-[12px] 2xl-only:text-base text-gray-500">
                  Full email: {email}
                  {customDomain?.startsWith("@")
                    ? customDomain
                    : customDomain
                    ? `@${customDomain}`
                    : ""}
                </span>
                <span
                  className="lg-only:text-[10px] xl-only:text-[12px] 2xl-only:text-base text-blue-500 cursor-pointer"
                  onClick={() =>
                    setValue("domain", "", { shouldValidate: true })
                  }
                >
                  Change domain
                </span>
              </div>
            </div>
          ) : (
            <div>
              <Controller
                control={control}
                name="domain"
                render={({ field }) => (
                  <div className="border-gradient rounded-lg">
                    <Select
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Reset customDomain when switching away from custom domain
                        if (value !== "domain3") {
                          setValue("customDomain", "", {
                            shouldValidate: true,
                          });
                        }
                      }}
                    >
                      <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                        <SelectValue placeholder="Select domain" />
                      </SelectTrigger>
                      <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                        <SelectItem value="domain1">@macgence.com</SelectItem>
                        <SelectItem value="domain2">@macgence.in</SelectItem>
                        <SelectItem value="domain3">Custom Domain</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              />
              {(domain === "domain1" || domain === "domain2") && (
                <span className="lg-only:text-[10px] xl-only:text-sm 2xl-only:text-base text-gray-500 lg-only:mt-0.5 xl-only:mt-1.5 2xl-only:mt-2 block">
                  Complete email: {email}
                  {domain === "domain1" ? "@macgence.com" : "@macgence.in"}
                </span>
              )}
            </div>
          )}
        </div>

        <div className=" mt-4">
          <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
            Role
          </Label>
          <Controller
            control={control}
            name="role"
            render={({ field }) => (
              <div className="border-gradient rounded-lg">
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                    <SelectItem value="ANNOTATOR">Annotator</SelectItem>
                    <SelectItem value="PROJECT_COORDINATOR">
                      Coordinator
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          />
        </div>

        {role === "ANNOTATOR" && (
          <div className=" mt-4">
            <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
              Package
            </Label>
            <Controller
              control={control}
              name="packageId"
              render={({ field }) => {
                const packages =
                  data?.pages.flatMap((page) => page.packages) || [];
                return (
                  <div className="border-gradient rounded-lg">
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className="bg-[#F9EFEF] w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5">
                        <SelectValue placeholder="Select package" />
                      </SelectTrigger>
                      <SelectContent className="border-transparent lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
                        {packages.map((pkg: any) => (
                          <SelectItem key={pkg.id} value={pkg.id}>
                            {pkg.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }}
            />
          </div>
        )}
      </div>

      <div className="lg-only:mt-2 xl-only:mt-6 2xl-only:mt-8 ">
        <Label className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg">
          Password
        </Label>
        <Controller
          control={control}
          name="passwordType"
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={field.onChange}
              className="lg-only:mt-1 xl-only:mt-2 2xl-only:mt-3 flex flex-col lg-only:gap-1 xl-only:gap-2 2xl-only:gap-3"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="auto"
                  id="auto"
                  className="border-[#FF577F] lg-only:h-3 lg-only:w-3 xl-only:h-5 xl-only:w-5 2xl-only:h-6 2xl-only:w-6"
                />
                <Label
                  htmlFor="auto"
                  className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
                >
                  Automatic send password link to mail
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="manual"
                  id="manual"
                  className="border-[#FF577F] lg-only:h-3 lg-only:w-3 xl-only:h-5 xl-only:w-5 2xl-only:h-6 2xl-only:w-6"
                />
                <Label
                  htmlFor="manual"
                  className="lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
                >
                  Create password
                </Label>
              </div>
            </RadioGroup>
          )}
        />

        {passwordType === "manual" && (
          <div className="flex lg-only:mt-1 xl-only:mt-3 2xl-only:mt-4 items-center lg-only:gap-1 xl-only:gap-3 2xl-only:gap-4">
            <div className="lg-only:w-[30%] xl-only:w-[35%] 2xl-only:w-[40%] relative border-gradient rounded-lg">
              <Input
                type={showPassword ? "text" : "password"}
                {...register("password")}
                placeholder="Enter password"
                className="w-full bg-[#F9EFEF] text-[#5E5E5E] pr-10 lg-only:text-xs lg-only:h-10 xl-only:text-base 2xl-only:text-lg lg-only:py-1 xl-only:py-2 2xl-only:py-2.5"
                autoComplete="new-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-2 top-1/2 -translate-y-1/2"
              >
                {showPassword ? (
                  <Eye className="lg-only:w-3 lg-only:h-3 xl-only:w-5 xl-only:h-5 2xl-only:w-6 2xl-only:h-6 text-gray-500" />
                ) : (
                  <EyeOff className="lg-only:w-3 lg-only:h-3 xl-only:w-5 xl-only:h-5 2xl-only:w-6 2xl-only:h-6 text-gray-500" />
                )}
              </button>
            </div>

            <Button
              variant="gradient"
              type="button"
              onClick={generatePassword}
              className="lg-only:text-xs lg-only:py-1 lg-only:px-2 xl-only:text-base 2xl-only:text-lg xl-only:py-2 2xl-only:py-2.5"
            >
              Generate Password
            </Button>
          </div>
        )}
      </div>

      <div className="lg-only:mt-2 xl-only:mt-6 2xl-only:mt-8 flex justify-end lg-only:gap-1 xl-only:gap-3 2xl-only:gap-4">
        <Button
          type="submit"
          variant="gradient"
          className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[4rem] xl-only:py-6 2xl-only:px-[5rem] 2xl-only:py-7 lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Processing..." : "Save"}
        </Button>
        <DialogClose asChild>
          <Button
            type="button"
            variant="ghost"
            className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[4rem] xl-only:py-6 2xl-only:px-[5rem] 2xl-only:py-7 border-gradient lg-only:text-xs xl-only:text-base 2xl-only:text-lg"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </DialogClose>
        <DialogClose asChild>
          <button ref={closeRef} className="hidden">
            Close
          </button>
        </DialogClose>
      </div>
    </form>
  );
};

export default OnboardForm;
