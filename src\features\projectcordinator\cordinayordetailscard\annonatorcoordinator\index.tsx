import { useState, useEffect } from "react";
import ShiftChange from "./coordinatorshiftchange";
import CustomToast from "@/_components/common/customtoast";
import { useNavigate } from "react-router-dom";
import { FaRegStar, FaStar } from "react-icons/fa6";
import { getCoordinatorAnnonators } from "../../api/api";
import { useQuery } from "@tanstack/react-query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

// interface DeveloperData {
//   id: string;
//   name: string;
//   email: string;
//   role: string;
//   createdAt: string;
//   updatedAt: string;
//   availableFrom: string | null;
//   availableTo: string | null;
//   packageId: string;
//   Package?: {
//     name: string;
//     id: string;
//   };
//   _count: {
//     annotatorProjects: number;
//   };
//   client?: {
//     _count: {
//       coWorkers: number;
//     };
//   };
// }

interface AnnotatorProps {
  id: string;
  name: string;
  email: string;
  shiftTiming: string;
  projects: string;
  joiningDate: string;
  subscription: string;
  image: string;
  status: string;
  availableFrom: string | null;
  availableTo: string | null;
  packageId: string;
  isStarred?: boolean;
  coworkers: string;
}

export default function ClientAnnotators() {
  const navigate = useNavigate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAnnotator, setSelectedAnnotator] = useState<AnnotatorProps | null>(null);
  const [showToast, setShowToast] = useState(false);
  const [starredAnnotators, setStarredAnnotators] = useState<Record<string, boolean>>({});
  const [processedAnnotators, setProcessedAnnotators] = useState<AnnotatorProps[]>([]);

  const { data: annotatorData, isLoading, error } = useQuery({
    queryKey: ["coordinatorAnnotators"],
    queryFn: getCoordinatorAnnonators,
  });

  useEffect(() => {
    if (!annotatorData) return;

    const extractAnnotatorsArray = () => {
      if (annotatorData.data?.data && Array.isArray(annotatorData.data.data)) {
        return annotatorData.data.data;
      }
      if (Array.isArray(annotatorData.data)) {
        return annotatorData.data;
      }
      if (Array.isArray(annotatorData)) {
        return annotatorData;
      }
      return null;
    };

    const annotatorArray = extractAnnotatorsArray();
    if (!annotatorArray) return;

    const processed = annotatorArray.map((item: any) => {
      const developer = item.developer || item;

      const getAvatarUrl = (name: string): string => {
        if (!name) return "";
        const firstName = name.trim().split(" ")[0];
        const initials = firstName.length >= 2 
          ? firstName.substring(0, 2).toUpperCase() 
          : firstName.charAt(0).toUpperCase();
        return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}`;
      };

      const formatDate = (dateString: string) => {
        if (!dateString) return "0";
        try {
          const date = new Date(dateString);
          return `${String(date.getDate()).padStart(2, "0")}/${String(date.getMonth() + 1).padStart(2, "0")}/${String(date.getFullYear()).slice(-2)}`;
        } catch {
          return "0";
        }
      };

      const formatShiftTiming = (time: string | null) => {
        if (!time) return "0";
        if (time.includes('T')) {
          return time.split('T')[1].substring(0, 5);
        }
        return time;
      };

      const id = developer?.id || `temp-${Math.random().toString(36).substring(2, 9)}`;
      const availableFrom = formatShiftTiming(developer?.availableFrom);
      const availableTo = formatShiftTiming(developer?.availableTo);

      return {
        id,
        name: developer?.name || "Unknown",
        email: developer?.email || "No email",
        shiftTiming: availableFrom !== "0" && availableTo !== "0" 
          ? `${availableFrom} - ${availableTo}` 
          : "0",
        projects: (developer?._count?.annotatorProjects || 0).toString(),
        joiningDate: formatDate(developer?.createdAt || new Date().toISOString()),
        subscription: developer?.Package?.name || "Standard",
        image: getAvatarUrl(developer?.name || ""),
        status: "active",
        availableFrom: developer?.availableFrom,
        availableTo: developer?.availableTo,
        packageId: developer?.packageId || "",
        isStarred: starredAnnotators[id] || false,
        coworkers: (developer?.client?._count?.coWorkers || 0).toString(),
      };
    });

    setProcessedAnnotators(processed);
  }, [annotatorData, starredAnnotators]);

  const toggleStar = (id: string) => {
    setStarredAnnotators(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const openModal = (annotator: AnnotatorProps) => {
    setSelectedAnnotator(annotator);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedAnnotator(null);
  };

  const handleSuccess = () => {
    setShowToast(true);
  };

  if (isLoading) return <BrandedGlobalLoader isLoading={true} />;
  if (error) return <div className="p-4 text-red-500">Error loading annotators: {(error as Error).message}</div>;
  if (!processedAnnotators?.length) return (
    <div className="flex justify-center items-center h-full">
      <div className="text-lg font-medium mt-10 text-gray-500 text-center">
        There are no coordinators available at the moment.
      </div>
    </div>
  );

  return (
    <div className="relative">
      <div className="grid lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5 gap-3 py-2 mx-auto max-w-[98%]">
        {processedAnnotators.map(annotator => (
          <div key={annotator.id} className="border border-[#FF577F] lg-only:p-2 xl-only:p-2.5 2xl-only:p-3 rounded-lg shadow-md bg-white">
            <div className="flex flex-row items-center lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3">
              <img
                src={annotator.image}
                alt={annotator.name}
                className="lg-only:w-10 lg-only:h-10 xl-only:w-11 xl-only:h-11 2xl-only:w-12 2xl-only:h-12 rounded-full"
              />
              <div className="flex flex-col w-full">
                <div className="flex flex-row justify-between items-center w-full">
                  <div className="flex lg-only:gap-0.5 xl-only:gap-1 2xl-only:gap-1 items-center">
                    <h3 className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base font-semibold">
                      {annotator.name.split(" ").slice(0, 2).join(" ")}
                    </h3>
                    {annotator.isStarred ? (
                      <FaStar className="text-[#FFC107] lg-only:text-base xl-only:text-lg 2xl-only:text-xl cursor-pointer" 
                        onClick={() => toggleStar(annotator.id)} />
                    ) : (
                      <FaRegStar className="text-[#FFC107] lg-only:text-base xl-only:text-lg 2xl-only:text-xl cursor-pointer" 
                        onClick={() => toggleStar(annotator.id)} />
                    )}
                  </div>
                  <button className="bg-[#5AB24A] hover:bg-[#7ece70] text-white lg-only:px-1.5 lg-only:py-[1px] xl-only:px-2 xl-only:py-[2px] 2xl-only:px-2.5 2xl-only:py-1 lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] rounded-2xl">
                    {annotator.status}
                  </button>
                </div>
                <p className="lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-xs text-gray-500">
                  {annotator.email}
                </p>
              </div>
            </div>

            <div className="lg-only:mt-1.5 xl-only:mt-2 2xl-only:mt-2.5 flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2 lg-only:px-2 xl-only:px-3 2xl-only:px-4 lg-only:text-[12px] xl-only:text-[12px] 2xl-only:text-[12px]">
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Shift Timing:</strong>
                <span>{annotator.shiftTiming}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Projects:</strong>
                <span>{annotator.projects}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Joined:</strong>
                <span>{annotator.joiningDate}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Subscription:</strong>
                <span>{annotator.subscription}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Coworkers:</strong>
                <span>{annotator.coworkers}</span>
              </p>
            </div>

            <div className="flex flex-row justify-center items-center lg-only:gap-x-0.5 xl-only:gap-x-1 2xl-only:gap-x-1 lg-only:mt-1.5 xl-only:mt-2 2xl-only:mt-2.5 lg-only:px-1.5 xl-only:px-2 2xl-only:px-2 w-full">
              <button
                onClick={() => navigate(`/coordinator/coordinatorannotatorprojects?id=${annotator.id}&name=${encodeURIComponent(annotator.name)}`)}
                className="border border-[#FF577F] text-[#FF577F] hover:text-[#f14870] lg-only:px-3 lg-only:py-0.5 xl-only:px-4 xl-only:py-1 2xl-only:px-5 2xl-only:py-1 lg-only:text-[13px] xl-only:text-[13px] 2xl-only:text-xs rounded-lg w-full"
              >
                Projects
              </button>
            </div>

            <div className="flex flex-row justify-between items-center lg-only:gap-x-1 xl-only:gap-x-1 2xl-only:gap-x-3 lg-only:mt-1.5 xl-only:mt-2 2xl-only:mt-2.5 lg-only:px-1.5 xl-only:px-2 2xl-only:px-2">
              <button
                onClick={() => openModal(annotator)}
                className="w-full border border-[#FF577F] text-[#FF577F] hover:text-[#FF577F] lg-only:px-4 lg-only:py-1 xl-only:px-5 xl-only:py-1.5 2xl-only:px-7 2xl-only:py-2 lg-only:text-[12px] xl-only:text-[12px] 2xl-only:text-[10px] rounded-lg"
              >
                Change Shift
              </button>
              <button
                onClick={() => navigate(`/coordinator/attendance?id=${annotator.id}&name=${encodeURIComponent(annotator.name)}`)}
                className="w-full bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] lg-only:px-3 lg-only:py-1 xl-only:px-4 xl-only:py-1.5 2xl-only:px-5 2xl-only:py-2 text-white lg-only:text-[12px] xl-only:text-[12px] 2xl-only:text-[13px] rounded-lg"
              >
                Attendance Log
              </button>
            </div>
          </div>
        ))}
      </div>

      {isModalOpen && selectedAnnotator && (
        <ShiftChange
          onClose={closeModal}
          onSuccess={handleSuccess}
          annotatorId={selectedAnnotator.id}
        />
      )}

      {showToast && (
        <div className="fixed top-5 right-5 z-[100]">
          <CustomToast
            title="Shift Change Requested"
            message="Your shift change request was submitted successfully."
            type="success"
            duration={4000}
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}