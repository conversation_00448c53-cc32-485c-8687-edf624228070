// togglesection.tsx

import { BackButton } from '@/_components/common';
import React from 'react';

type ToggleSectionProps = {
  selected: 'ongoing' | 'new';
  setSelected: React.Dispatch<React.SetStateAction<'ongoing' | 'new'>>;
};

const ToggleSection: React.FC<ToggleSectionProps> = ({ selected, setSelected }) => {
  return (
    <div className="mb-6 lg:mb-8 xl:mb-10 2xl:mb-12">
      <div className='flex items-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 mb-4 lg:mb-5 xl:mb-6 2xl:mb-8'>
        <BackButton />
        <h2 className="text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold text-gray-800 font-inter">
          Hiring annotator for?
        </h2>
      </div>
      <div className="flex flex-col lg:flex-row gap-4 lg:gap-6 xl:gap-8 2xl:gap-10">
        {['ongoing', 'new'].map((type) => (
          <div
            key={type}
            className={`border-2 rounded-md px-4 py-3 lg:px-6 lg:py-4 xl:px-8 xl:py-5 2xl:px-10 2xl:py-6 w-full lg:w-1/2 cursor-pointer transition-all ${
              selected === type
                ? 'border-pink-500 bg-red-50 text-pink-600'
                : 'border-gray-200 bg-white text-gray-600'
            }`}
            onClick={() => setSelected(type as 'ongoing' | 'new')}
          >
            <label className="flex items-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 font-bold text-base lg:text-lg xl:text-xl 2xl:text-2xl font-inter cursor-pointer">
              <input
                type="radio"
                name="projectType"
                value={type}
                checked={selected === type}
                onChange={() => setSelected(type as 'ongoing' | 'new')}
                className="accent-pink-500 w-4 h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7"
              />
              {type === 'ongoing' ? 'Ongoing Project' : 'New Project'}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ToggleSection;
