// addon.tsx

import React, { useState, useEffect } from "react";
import { featureLabels } from "./commonpackageplan/featureLabels"; // Import feature labels
import ToggleSection from "./commonpackageplan/Togglesection"; // Import ToggleSection
import AddOnPlanTable from "./commonpackageplan/addonpackageplan"; 
import { getAllPackages } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package.tsx";

// Define the Plan type
interface Plan {
  name: string;
  price: number;
  features: boolean[];
  id: string;
  isActive: boolean;
  description?: string;
  billingType?: string;
}

const AddOn: React.FC = () => {
  const [selected, setSelected] = useState<"ongoing" | "new">("ongoing"); // State for the selected project type
  const [packages, setPackages] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch packages from the API
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const response = await getAllPackages();

        if (
          response &&
          response.data &&
          Array.isArray(response.data.packages)
        ) {
          // Filter only active packages
          const activePackages = response.data.packages
            .filter((pkg: any) => pkg.isActive === true)
            .map((pkg: any) => ({
              id: pkg.id,
              name: pkg.name,
              price: pkg.price,
              description: pkg.description,
              isActive: pkg.isActive,
              billingType: pkg.billingType,
              // Generate random features for now - in a real app, these would come from the API
              features: featureLabels.map(() => Math.random() > 0.3),
            }));

          setPackages(activePackages);
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  return (
    <div className="p-4 lg:p-6 xl:p-8 2xl:p-10 ">
      {/* Toggle Section */}
      <ToggleSection selected={selected} setSelected={setSelected} />

      {/* Annotator Package Table Section */}
      {loading ? (
        <div className="flex justify-center items-center h-64 lg:h-80 xl:h-96 2xl:h-[30rem]">
          <div className="animate-spin rounded-full h-10 w-10 lg:h-12 lg:w-12 xl:h-14 xl:w-14 2xl:h-16 2xl:w-16 border-t-2 border-b-2 border-[#FF577F]"></div>
        </div>
      ) : (
        <div className="overflow-x-auto CustomScroll">
          <div className="min-w-max lg:min-w-0">
           <AddOnPlanTable plans={packages} featureLabels={featureLabels} />
          </div>
        </div>
      )}
    </div>
  );
};

export default AddOn;
