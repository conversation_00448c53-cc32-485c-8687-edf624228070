import { FaArrowLeft } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

interface BackButtonProps {
  className?: string;
  onClick?: () => void;
}

/**
 * BackButton component that navigates back one page when clicked
 * 
 * @param className - Optional CSS class for styling
 * @param onClick - Optional callback function to execute when clicked (in addition to going back)
 * @returns A button with a left arrow icon that navigates back when clicked
 */
const BackButton = ({ className = "", onClick }: BackButtonProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    // If custom onClick is provided, execute it
    if (onClick) {
      onClick();
    }
    
    // Navigate back one page
    navigate(-1);
  };

  return (
    <button
      onClick={handleClick}
      className={`flex items-center justify-center p-2 rounded-full hover:border-gray-600 transition-colors ${className}`}
      aria-label="Go back"
      title="Go back to previous page"
    >
      <FaArrowLeft className="text-[#FF577F] text-2xl" />
    </button>
  );
};

export default BackButton;
