# Zoho Desk API Integration

This document outlines how to implement the backend API endpoints for Zoho Desk integration.

## Required Endpoints

### 1. Get Departments

**Endpoint:** `/api/zoho-desk/departments`
**Method:** GET

This endpoint should fetch all available departments from Zoho Desk.

#### Implementation Steps:

1. Authenticate with Zoho Desk using OAuth2.0 or API key
2. Make a GET request to Zoho Desk API: `https://desk.zoho.com/api/v1/departments`
3. Return the departments in the following format:

```json
{
  "success": true,
  "data": [
    {
      "id": "123456000000123456",
      "name": "IT Support",
      "description": "Technical support department",
      "isEnabled": true
    },
    {
      "id": "123456000000123457",
      "name": "Billing",
      "description": "Billing and payment support",
      "isEnabled": true
    }
  ]
}
```

### 2. Create Ticket

**Endpoint:** `/api/zoho-desk/tickets`
**Method:** POST

This endpoint should create a new ticket in Zoho Desk.

#### Request Body:

```json
{
  "subject": "Issue with login",
  "description": "I'm unable to login to my account",
  "departmentId": "123456000000123456",
  "category": "technical",
  "status": "open",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

#### Implementation Steps:

1. Authenticate with Zoho Desk using OAuth2.0 or API key
2. Map the request body to Zoho Desk ticket format
3. Make a POST request to Zoho Desk API: `https://desk.zoho.com/api/v1/tickets`
4. Return the created ticket in the following format:

```json
{
  "success": true,
  "data": {
    "id": "123456000000123458",
    "ticketNumber": "1234",
    "subject": "Issue with login",
    "status": "Open",
    "departmentId": "123456000000123456",
    "createdTime": "2023-08-01T12:00:00.000Z"
  }
}
```

## Zoho Desk API Authentication

To authenticate with Zoho Desk API:

1. Register your application in Zoho Developer Console
2. Generate OAuth2.0 credentials (client ID and client secret)
3. Implement OAuth2.0 flow to get access token
4. Include the access token in all API requests as a Bearer token in the Authorization header

```
Authorization: Bearer {access_token}
```

## Error Handling

All API endpoints should handle errors gracefully and return appropriate error responses:

```json
{
  "success": false,
  "error": {
    "code": "INVALID_DEPARTMENT",
    "message": "The specified department does not exist"
  }
}
```

Common error scenarios to handle:
- Authentication failures
- Invalid department ID
- Required fields missing
- Rate limiting
- Network errors

## References

- [Zoho Desk API Documentation](https://desk.zoho.com/DeskAPIDocument)
- [Zoho OAuth2.0 Documentation](https://www.zoho.com/desk/developer-guide/api/v1/oauth-overview.html)