import { customAxios } from "@/utils/axio-interceptor";

// Types for Zoho Desk API
export interface ZohoDepartment {
  id: string;
  name: string;
  description?: string;
  isEnabled: boolean;
}

export interface ZohoTicket {
  subject: string;
  description: string;
  departmentId: string;
  category: string;
  status: string;
  contact: {
    email: string;
    firstName: string;
    lastName: string;
  };
}

export interface ZohoTicketResponse {
  id: string;
  ticketNumber: string;
  subject: string;
  status: string;
  departmentId: string;
  createdTime: string;
}

// Get all departments from Zoho Desk
export const getZohoDepartments = async (): Promise<ZohoDepartment[]> => {
  try {
    const response = await customAxios.get("/v1/zoho-desk/departments");
    console.log(response.data.data, "api");
    return response.data.data.data;
  } catch (error) {
    console.error("Error fetching Zoho departments:", error);
    throw error;
  }
};

// Create a new ticket in Zoho Desk
export const createZohoTicket = async (
  ticketData: ZohoTicket
): Promise<ZohoTicketResponse> => {
  try {
    const response = await customAxios.post(
      "/v1/zoho-desk/tickets",
      ticketData
    );
    return response.data.data;
  } catch (error) {
    console.error("Error creating Zoho ticket:", error);
    throw error;
  }
};
