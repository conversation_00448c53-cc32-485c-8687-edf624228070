export type TaskType = {
  id: string;
  title: string;
  columnId: string;
  createdAt: string;
  updatedAt: string;
};

export type ColumnType = {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
};

export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  dueDate?: string;
  status?: string;
  color?: string;
  endDate: string;
  // Additional fields from API response
  assignedToId?: string;
  createdById?: string;
  startDate?: string;
  createdAt?: string;
  updatedAt?: string;
};


