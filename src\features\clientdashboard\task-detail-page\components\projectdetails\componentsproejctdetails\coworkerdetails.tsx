import React, { useState, useEffect } from "react";
import { X, ExternalLink } from "lucide-react";
import { getCoworkers } from "../projectdetails_api/projectdetails_api";

// Define types based on API response
type CoworkerStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING" | string;
type CoworkerPermission = "VIEW" | "EDIT" | null;

type Coworker = {
  id: string;
  name: string;
  email: string;
  accountStatus: CoworkerStatus;
  coworkerPermission: CoworkerPermission;
  availableFrom: string | null;
  availableTo: string | null;
  createdAt: string;
};

// Generate avatar URL using dicebear with first 2 letters of name
const getAvatarUrl = (name: string): string => {
  if (!name) return "";

  // Get the first name (first word)
  const firstName = name.trim().split(" ")[0];

  // Take the first two letters of the first name
  const initials = firstName.length >= 2
    ? firstName.substring(0, 2).toUpperCase()
    : firstName.charAt(0).toUpperCase();

  return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(initials)}`;
};

const CoworkerDetails: React.FC = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [selectedCoworker, setSelectedCoworker] = useState<Coworker | null>(null);
  const [coworkers, setCoworkers] = useState<Coworker[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch coworkers from API
  useEffect(() => {
    const fetchCoworkers = async () => {
      try {
        setLoading(true);
        const response = await getCoworkers();
        console.log("Coworkers API response:", response);

        if (response && response.data) {
          setCoworkers(response.data.items || []);
        } else {
          setCoworkers([]);
        }
      } catch (err) {
        console.error("Error fetching coworkers:", err);
        setError("Failed to load coworkers");
      } finally {
        setLoading(false);
      }
    };

    fetchCoworkers();
  }, []);

  const togglePopup = (coworker: Coworker) => {
    setSelectedCoworker(coworker);
    setShowPopup((prev) => !prev);
  };

  const closePopup = () => {
    setShowPopup(false);
    setSelectedCoworker(null);
  };

  return (
    <div className="CustomScroll bg-[#F3F3F3] shadow-md rounded-lg p-2 lg:p-3 xl:p-4 2xl:p-5 w-full h-full min-h-[120px] lg:min-h-[130px] xl:min-h-[140px] 2xl:min-h-[150px] flex flex-col relative">
      <div className="flex justify-between items-center mb-2 lg:mb-3 xl:mb-4 2xl:mb-5">
        <h2 className="text-base lg:text-lg xl:text-xl 2xl:text-2xl font-semibold font-poppins">Co-Workers</h2>
      </div>

      <ul className="CustomScroll overflow-y-auto pr-1 flex-grow">
        {loading ? (
          <li className="text-center text-xs lg:text-sm xl:text-base 2xl:text-lg text-gray-500">Loading...</li>
        ) : error ? (
          <li className="text-center text-xs lg:text-sm xl:text-base 2xl:text-lg text-red-500">{error}</li>
        ) : coworkers.length > 0 ? (
          coworkers.map((coworker) => (
            <li key={coworker.id} className="flex items-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 mb-2 lg:mb-3 xl:mb-4 2xl:mb-5 last:mb-0">
              <img
                src={getAvatarUrl(coworker.name)}
                alt="avatar"
                className="w-5 h-5 lg:w-6 lg:h-6 xl:w-8 xl:h-8 2xl:w-10 2xl:h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex gap-1 lg:gap-2 xl:gap-3 2xl:gap-4 items-center">
                  <p className="font-medium text-xs lg:text-sm xl:text-base 2xl:text-lg text-[#282828]">
                    {coworker.name}
                  </p>
                  <button
                    onClick={() => togglePopup(coworker)}
                    className="flex items-center justify-center w-6 h-4 lg:w-7 lg:h-5 xl:w-8 xl:h-6 2xl:w-9 2xl:h-7 rounded-full bg-gray-200 hover:bg-gray-300"
                  >
                    <div className="flex space-x-1">
                      <span className="w-1 h-1 lg:w-1.5 lg:h-1.5 xl:w-2 xl:h-2 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                      <span className="w-1 h-1 lg:w-1.5 lg:h-1.5 xl:w-2 xl:h-2 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                      <span className="w-1 h-1 lg:w-1.5 lg:h-1.5 xl:w-2 xl:h-2 2xl:w-2.5 2xl:h-2.5 bg-black rounded-full"></span>
                    </div>
                  </button>
                </div>
                <p className="text-[10px] lg:text-xs xl:text-sm 2xl:text-base text-[#727272]">
                  {coworker.coworkerPermission || "No permission"}
                </p>
              </div>
            </li>
          ))
        ) : (
          <li className="text-center text-xs lg:text-sm xl:text-base 2xl:text-lg text-gray-500">Not coworkers assign</li>
        )}
      </ul>

      {/* Popup */}
      {showPopup && selectedCoworker && (
        <div className="w-full lg:w-[280px] xl:w-[320px] 2xl:w-[360px] rounded-xl border border-pink-300 p-3 lg:p-4 xl:p-5 2xl:p-6 shadow-lg bg-white absolute top-10 left-0 z-10">
          {/* Top Icons */}
          <div className="absolute top-2 right-2 flex space-x-2">
            <ExternalLink className="w-4 h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7 cursor-pointer" />
            <X
              className="w-4 h-4 lg:w-5 lg:h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7 cursor-pointer"
              onClick={closePopup}
            />
          </div>

          {/* Avatar and Name */}
          <div className="flex gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 items-center mt-4 lg:mt-6 xl:mt-8 2xl:mt-10">
            <img
              src={getAvatarUrl(selectedCoworker.name)}
              alt="avatar"
              className="w-12 h-12 lg:w-16 lg:h-16 xl:w-20 xl:h-20 2xl:w-24 2xl:h-24 rounded-full object-cover"
            />
            <div className="flex flex-col">
              <h2 className="font-medium text-sm lg:text-base xl:text-lg 2xl:text-xl mt-1 lg:mt-2 xl:mt-3 2xl:mt-4">{selectedCoworker.name}</h2>
              <p className="text-xs lg:text-sm xl:text-base 2xl:text-lg text-gray-500">{selectedCoworker.email}</p>
            </div>
          </div>

          {/* Permission */}
          <div className="flex justify-between mt-3 lg:mt-4 xl:mt-5 2xl:mt-6 px-2 text-xs lg:text-sm xl:text-base 2xl:text-lg">
            <span className="text-gray-600">Permission:</span>
            <span className="font-normal">
              {selectedCoworker.coworkerPermission || "No permission"}
            </span>
          </div>

          {/* Buttons */}
          <div className="flex justify-center gap-2 lg:gap-3 xl:gap-4 2xl:gap-5 mt-3 lg:mt-4 xl:mt-5 2xl:mt-6">
            <button className="w-full border border-pink-500 text-pink-500 rounded-lg px-2 lg:px-3 xl:px-4 2xl:px-5 py-1 lg:py-1.5 xl:py-2 2xl:py-2.5 text-xs lg:text-sm xl:text-base 2xl:text-lg font-semibold hover:bg-pink-50">
              Group Chat
            </button>
            <button className="w-full bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg px-2 lg:px-3 xl:px-4 2xl:px-5 py-1 lg:py-1.5 xl:py-2 2xl:py-2.5 text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium hover:opacity-90">
              Chat
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CoworkerDetails;
