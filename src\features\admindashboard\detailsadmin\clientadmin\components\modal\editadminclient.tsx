// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";

// interface EditAdminClientProps {
//   client: {
//     id: string;
//     name: string;
//     email: string;
//   };
//   open?: boolean;
//   onOpenChange?: (open: boolean) => void;
// }

// export default function EditAdminClient({ client, open, onOpenChange }: EditAdminClientProps) {
//   const [name, setName] = useState(client.name);
//   const [email, setEmail] = useState(client.email);

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();
//     // Add your edit logic here
//     console.log("Editing client:", { id: client.id, name, email });
//     if (onOpenChange) onOpenChange(false);
//   };

//   return (
//     <Dialog open={open} onOpenChange={onOpenChange}>
//       <DialogContent className="sm:max-w-[425px]">
//         <DialogHeader>
//           <DialogTitle>Edit Client</DialogTitle>
//         </DialogHeader>
//         <form onSubmit={handleSubmit} className="grid gap-4 py-4">
//           <div className="grid grid-cols-4 items-center gap-4">
//             <Label htmlFor="name" className="text-right">
//               Name
//             </Label>
//             <Input
//               id="name"
//               value={name}
//               onChange={(e) => setName(e.target.value)}
//               className="col-span-3"
//               required
//             />
//           </div>
//           <div className="grid grid-cols-4 items-center gap-4">
//             <Label htmlFor="email" className="text-right">
//               Email
//             </Label>
//             <Input
//               id="email"
//               type="email"
//               value={email}
//               onChange={(e) => setEmail(e.target.value)}
//               className="col-span-3"
//               required
//             />
//           </div>
//           <div className="flex justify-end gap-2">
//             <Button type="button" variant="outline" onClick={() => onOpenChange?.(false)}>
//               Cancel
//             </Button>
//             <Button type="submit">Save Changes</Button>
//           </div>
//         </form>
//       </DialogContent>
//     </Dialog>
//   );
// }