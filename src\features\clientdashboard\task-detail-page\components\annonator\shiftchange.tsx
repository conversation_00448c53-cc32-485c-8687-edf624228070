import { X } from "lucide-react";
import { useState, useEffect } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { clientChangeShift } from "./clientannonator_api/clientannonator_api";

interface ShiftChangeProps {
    onClose: () => void;
    onSuccess: () => void;
    annotatorId?: string;
}

export default function ShiftChange({ onClose, onSuccess, annotatorId }: ShiftChangeProps) {
    const [fromTime, setFromTime] = useState("");
    const [toTime, setToTime] = useState("");
    const [note, setNote] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        setIsOpen(true);
        // Validate annotatorId when component mounts
        if (!annotatorId) {
            console.error("Error: Annotator ID is missing.");
            setError("Annotator ID is missing. Please select an annotator first.");
        } else {
            setError(null);
        }
    }, [annotatorId]);

    const handleClose = () => {
        setIsOpen(false);
        setTimeout(onClose, 300);
    };

    const generateTimeSlots = () => {
        const times = [];
        for (let hour = 0; hour < 24; hour++) {
            for (let min = 0; min < 60; min += 30) {
                const formattedHour = hour.toString().padStart(2, "0");
                const formattedMin = min.toString().padStart(2, "0");
                times.push(`${formattedHour}:${formattedMin}`);
            }
        }
        return times;
    };

    const timeOptions = generateTimeSlots();

    const validateTimes = () => {
        if (!fromTime || !toTime) {
            return "Please select both From and To time.";
        }

        // Convert times to minutes for easy comparison
        const [fromHour, fromMinute] = fromTime.split(':').map(Number);
        const [toHour, toMinute] = toTime.split(':').map(Number);
        
        const fromMinutes = fromHour * 60 + fromMinute;
        const toMinutes = toHour * 60 + toMinute;
        
        // Check if "to" time is after "from" time
        if (toMinutes <= fromMinutes) {
            return "End time must be after start time.";
        }
        
        // Check if shift is at least 1 hour
        if (toMinutes - fromMinutes < 60) {
            return "Shift must be at least 1 hour long.";
        }
        
        // Check if shift is not too long (e.g., max 12 hours)
        if (toMinutes - fromMinutes > 12 * 60) {
            return "Shift cannot be longer than 12 hours.";
        }
        
        return null;
    };

    const handleSubmit = async () => {
        setError(null);
        
        if (!annotatorId) {
            setError("Annotator ID is missing. Please select an annotator first.");
            return;
        }
        
        const timeError = validateTimes();
        if (timeError) {
            setError(timeError);
            return;
        }

        try {
            setIsSubmitting(true);
            await clientChangeShift({
                annotatorId: annotatorId,
                availableFrom: fromTime,
                availableTo: toTime,
                // note: note // Will be added later when backend supports it
            });
            
            handleClose();
            setTimeout(() => {
                onSuccess();
            }, 300);
        } catch (error) {
            console.error("Error submitting shift change:", error);
            setError("Failed to submit shift change. Please try again.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Sheet open={isOpen} onOpenChange={(open) => {
            if (!open) handleClose();
        }}>
            <SheetContent side="right" className="p-0 w-[25rem] sm:max-w-none">
                <div className="flex flex-col h-full p-4">
                    {/* Header */}
                    <div className="flex justify-between border-b pb-2 mb-4">
                        <h2 className="font-bold text-gray-800 text-lg">Shift Change</h2>
                        <X 
                            onClick={handleClose} 
                            className="cursor-pointer text-[#727272] hover:text-[#585858] h-5 w-5" 
                        />
                    </div>
                    
                    {/* Error message */}
                    {error && (
                        <div className="mb-4 p-2 bg-red-100 border border-red-400 text-red-700 rounded-md text-xs">
                            {error}
                        </div>
                    )}
                    
                    {/* Form */}
                    <div className="flex flex-col justify-between flex-1">
                        <div>
                            {/* Time Selection Row */}
                            <div className="flex gap-x-2 mb-2">
                                {/* From Time */}
                                <div className="mb-3 w-1/2">
                                    <label className="block font-medium text-sm mb-1">From <span className="text-red-500">*</span></label>
                                   <div className="border-gradient rounded-md">
                                     <select 
                                        value={fromTime}
                                        onChange={(e) => {
                                            setFromTime(e.target.value);
                                            setError(null); // Clear error when user makes a change
                                        }}
                                        className="w-full bg-[#F9EFEF] border rounded-md p-2 text-xs text-[#5E5E5E] focus:outline-none"
                                    >
                                        <option value="">Select time</option>
                                        {timeOptions.map((time, index) => (
                                            <option key={index} value={time}>{time}</option>
                                        ))}
                                    </select>
                                   </div>
                                </div>
                                
                                {/* To Time */}
                                <div className="mb-3 w-1/2">
                                    <label className="block font-medium text-sm mb-1">To <span className="text-red-500">*</span></label>
                                   <div className="border-gradient rounded-md">
                                     <select 
                                        value={toTime}
                                        onChange={(e) => {
                                            setToTime(e.target.value);
                                            setError(null); // Clear error when user makes a change
                                        }}
                                        className="w-full bg-[#F9EFEF] rounded-md p-2 text-xs text-[#5E5E5E] focus:outline-none"
                                    >
                                        <option value="">Select time</option>
                                        {timeOptions.map((time, index) => (
                                            <option key={index} value={time}>{time}</option>
                                        ))}
                                    </select>
                                   </div>
                                </div>
                            </div>
                            
                            {/* Note */}
                            <div className="mb-3">
                                <label className="block font-medium text-sm mb-1">Note <span className="text-gray-400">(Optional)</span></label>
                                <div className="bg-[#F9EFEF] rounded-md p-1.5 border-gradient">
                                    <textarea 
                                        value={note}
                                        onChange={(e) => setNote(e.target.value)}
                                        placeholder="Add a note..."
                                        className="w-full bg-transparent rounded-md p-2 text-xs resize-none h-[10rem] focus:outline-none"
                                    />
                                </div>
                            </div>
                        </div>
                        
                        {/* Buttons */}
                        <div className="flex justify-end gap-3 mt-3">
                            <button 
                                onClick={handleClose}
                                className="border-gradient rounded-md text-gray-600 px-6 py-1.5 text-xs"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleSubmit}
                                className="bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] text-white rounded-md px-6 py-1.5 text-xs"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? "Submitting..." : "Submit"}
                            </button>
                        </div>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    );
}
