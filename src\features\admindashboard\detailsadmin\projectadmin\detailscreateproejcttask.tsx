import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown } from "lucide-react";
import { IoIosArrowDown } from "react-icons/io";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  createProjectTaskApi,
  fetchAllClientsannotator,
  getProjectDetails,
} from "./project_api/project_api";

type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  priority: string;
  annotator: string;
  startDate: string;
  endDate: string;
  color: string;
  columnId: string;
};

type Annotator = {
  id: string;
  name: string;
};

type Props = {
  columnId: string;
  onAddTask: (task: Task) => void;
};

type FormData = {
  title: string;
  description: string;
  level: string;
  priority: string;
  annotator: string[]; // Changed from string to string[]
  startDate: string;
  endDate: string;
};

const colorOptions = [
  { name: "Orange", color: "#ff5805" },
  { name: "Red", color: "#F87171" },
  { name: "Green", color: "#07fc0b" },
  { name: "Purple", color: "#bc02c9" },
  { name: "Blue", color: "#60A5FA" },
];

const initialForm: FormData = {
  title: "",
  description: "",
  level: "",
  priority: "",
  annotator: [], // Changed to array
  startDate: "",
  endDate: "",
};

const AdminCreateProjectTask: React.FC<Props> = ({ columnId, onAddTask }) => {
  const location = useLocation();
  const [projectId, setProjectId] = useState<string>("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [clientannotator, setClientAnnotator] = useState<Annotator[]>([]);
  const [formData, setFormData] = useState<FormData>(initialForm);
  const [errors, setErrors] = useState({
    title: false,
    description: false,
    priority: false,
    annotator: false,
    startDate: false,
    endDate: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedColor, setSelectedColor] = useState(colorOptions[0].color);
  const [showColorDropdown, setShowColorDropdown] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: false }));
  };

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setShowColorDropdown(false);
  };

  const validateForm = () => {
    const newErrors = {
      title: !formData.title.trim(),
      description: !formData.description.trim(),
      priority: !formData.priority,
      annotator: formData.annotator.length === 0, // Check for empty array
      startDate: !formData.startDate,
      endDate: !formData.endDate,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  // Extract project ID from URL when component mounts
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const id = queryParams.get("id");
    if (id) {
      setProjectId(id);
    } else {
      console.error("No project ID found in URL");
    }
  }, [location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || isSubmitting) return;

    if (!projectId) {
      toast.error("Project ID not found. Please refresh the page.");
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare the task data for the API
      const taskData = {
        name: formData.title,
        description: formData.description,
        priority: formData.priority.toUpperCase(),
        color: selectedColor,
        annotators: formData.annotator, // Now it's already an array
        startDate: formData.startDate,
        dueDate: formData.endDate,
      };

      // Call the API with projectId in URL and taskData in body
      const response = await createProjectTaskApi(projectId, taskData);
      console.log("API Response:", response);
      toast.success("Task created successfully!");

      // Find the annotator name from the selected ID (using first annotator for display)
      const selectedAnnotator = clientannotator.find(
        (annotator) => annotator.id === formData.annotator[0]
      );

      // Create the local task object
      const newTask: Task = {
        id: response.data.id,
        title: response.data.name,
        description: response.data.description,
        level: "",
        priority: response.data.priority.toLowerCase(),
        annotator: selectedAnnotator?.name || "Unknown",
        startDate: response.data.startDate,
        endDate: response.data.dueDate,
        color: response.data.color,
        columnId,
      };

      // Update local state
      onAddTask(newTask);
      handleClose();
    } catch (error) {
      console.error("Error creating task:", error);
      toast.error("Failed to create task. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsDialogOpen(false);
    setFormData(initialForm);
    setErrors({
      title: false,
      description: false,
      priority: false,
      annotator: false,
      startDate: false,
      endDate: false,
    });
    setSelectedColor("#60A5FA");
    setShowColorDropdown(false);
  };

  const fetchClientAnnotators = async () => {
    try {
      const clientannotators = await fetchAllClientsannotator();
      const annotators = clientannotators.map((annotator: any) => ({
        id: annotator.id,
        name: annotator.name,
      }));
      setClientAnnotator(annotators);
    } catch (error) {
      console.error("Error fetching client annotators:", error);
      toast.error("Failed to fetch annotators. Please try again.");
    }
  };

  useEffect(() => {
    fetchClientAnnotators();
  }, []);

  useEffect(() => {
    const fetchProjectAnnotator = async () => {
      try {
        if (!projectId) return;

        const response = await getProjectDetails(projectId);
        console.log("Project details response:", response);

        if (response?.data?.annotator) {
          // If the project has a default annotator, set it in the form
          setFormData((prev) => ({
            ...prev,
            annotator: [response.data.annotator.id], // Set as array
          }));
        }
      } catch (err) {
        console.error("Error fetching project details:", err);
      }
    };

    if (isDialogOpen && projectId) {
      fetchProjectAnnotator();
    }
  }, [projectId, isDialogOpen]);

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <div className="flex items-center justify-center gap-3 cursor-pointer">
          <Plus className="text-white text-xs" />
          <p className="text-white text-xs"> Add Project Task</p>
        </div>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-[#282828] font-poppins font-semibold text-xl">
            Create Task
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title + Color */}
          <div>
            <label className="text-sm font-medium text-[#282828]">
              Task Name *
            </label>
            <div className="flex items-center gap-2 mt-2">
              <div className="border-gradient w-full rounded-md">
                <input
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  placeholder="Enter task name"
                  className={`w-full rounded-md px-4 py-2 bg-red-50 text-gray-700 placeholder-gray-500 focus:outline-none ${
                    errors.title ? "border border-red-500" : ""
                  }`}
                />
              </div>
              <div className="relative">
                <div
                  className="cursor-pointer"
                  onClick={() => setShowColorDropdown((prev) => !prev)}
                >
                  <div className="w-[54px] h-[40px] bg-red-50 flex justify-center items-center gap-2 rounded-md border border-gradient">
                    <div
                      className="w-5 h-5 rounded-full border"
                      style={{ backgroundColor: selectedColor }}
                    />
                    <IoIosArrowDown />
                  </div>
                </div>

                {showColorDropdown && (
                  <div className="absolute top-[110%] right-0 bg-white border rounded-md shadow-md p-2 z-10 flex flex-col gap-2">
                    {colorOptions.map((c) => (
                      <div
                        key={c.name}
                        className="w-5 h-5 rounded-full cursor-pointer border hover:scale-110 transition"
                        style={{ backgroundColor: c.color }}
                        onClick={() => handleColorSelect(c.color)}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">Title is required</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="text-sm font-medium text-[#282828]">
              Description *
            </label>
            <div className="border-gradient rounded-lg">
              <Textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className={`w-full p-3  max-h-[6rem] h-[6rem]  bg-[#F9EFEF]  text-sm rounded-md focus:outline-none ${
                  errors.description ? "border border-red-500" : ""
                }`}
                placeholder="Type your message here."
              />
            </div>
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">
                Description is required
              </p>
            )}
          </div>

          {/* Priority & Admin */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Priority *
              </label>
              <Select
                value={formData.priority}
                onValueChange={(value) => {
                  setFormData((prev) => ({ ...prev, priority: value }));
                  setErrors((prev) => ({ ...prev, priority: false }));
                }}
              >
                <SelectTrigger
                  className={`w-full text-[#5E5E5E] py-3 focus:outline-none text-xs font-normal bg-[#F9EFEF] rounded-md ${
                    errors.priority
                      ? "border border-red-500"
                      : "border-gradient"
                  }`}
                >
                  <SelectValue placeholder="Select a priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Select a priority</SelectLabel>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="LOW">Low</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
              {errors.priority && (
                <p className="text-red-500 text-sm mt-1">
                  Priority is required
                </p>
              )}
            </div>

            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Annotator *
              </label>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-between bg-[#F9EFEF] text-xs font-normal text-[#5E5E5E] py-3 px-4 rounded-md ${
                      errors.annotator
                        ? "border border-red-500"
                        : "border-gradient"
                    }`}
                  >
                    {formData.annotator.length > 0
                      ? clientannotator
                          .filter((a) => formData.annotator.includes(a.id))
                          .map((a) => a.name)
                          .join(", ")
                      : "Select Annotator"}
                    <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>

                <PopoverContent className="w-[250px] max-h-60 overflow-y-auto p-2 rounded-md bg-white shadow-md">
                  {clientannotator.map((annotator) => (
                    <div
                      key={annotator.id}
                      className="flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 rounded"
                    >
                      <Checkbox
                        id={`annotator-${annotator.id}`}
                        checked={formData.annotator.includes(annotator.id)}
                        onCheckedChange={(checked) => {
                          setFormData((prev) => ({
                            ...prev,
                            annotator: checked
                              ? [...prev.annotator, annotator.id]
                              : prev.annotator.filter(
                                  (id) => id !== annotator.id
                                ),
                          }));
                          setErrors((prev) => ({ ...prev, annotator: false }));
                        }}
                      />
                      <label
                        htmlFor={`annotator-${annotator.id}`}
                        className="text-xs text-[#5E5E5E] cursor-pointer"
                      >
                        {annotator.name}
                      </label>
                    </div>
                  ))}
                </PopoverContent>
              </Popover>

              {errors.annotator && (
                <p className="text-red-500 text-sm mt-1">
                  At least one annotator is required
                </p>
              )}
            </div>
          </div>

          {/* Deadline */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Start Date *
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                min={new Date().toISOString().split("T")[0]}
                className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              />
              {errors.startDate && (
                <p className="text-red-500 text-sm mt-1">
                  Start Date is required
                </p>
              )}
            </div>
            <div className="flex-1">
              <label className="text-sm font-medium text-[#282828]">
                Due Date *
              </label>
              <input
                type="date"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                min={new Date().toISOString().split("T")[0]}
                className="w-full mt-2 p-2 bg-[#F9EFEF] text-[#5E5E5E] text-xs font-normal rounded-md border-gradient"
              />
              {errors.endDate && (
                <p className="text-red-500 text-sm mt-1">
                  Due Date is required
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-4 justify-end items-center">
            <button
              onClick={handleClose}
              type="reset"
              className="w-32 px-3 py-2 font-bold text-base rounded-md border-gradient border text-black transition"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <Button
              variant={"gradient"}
              type="submit"
              className="w-32 px-3 py-2 font-bold text-base rounded-md  text-white transition"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Creating..." : "Create Task"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AdminCreateProjectTask;
