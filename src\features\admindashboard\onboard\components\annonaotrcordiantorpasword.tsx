import React, { useState, useEffect } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { ClientResetPassword } from "@/features/auth/api/client-api";
import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";
import { toast } from "react-toastify";
import logo from "@/assets/darklogo.png";
import { Eye, EyeOff } from "lucide-react";
import { BackButton } from "@/_components/common";

const SetPassword: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { token: pathToken } = useParams<{ token: string }>();

  // Get token and email from query parameters
  const queryParams = new URLSearchParams(location.search);
  const queryToken = queryParams.get("token") || "";
  const queryEmail = queryParams.get("email") || "";

  // Determine token source: path parameter (reset password) or query parameter (admin invite)
  const token = pathToken || queryToken;
  const email = queryEmail || sessionStorage.getItem("resetEmail") || "";

  // Debug logging
  console.log("URL Search:", location.search);
  console.log("Path Token:", pathToken);
  console.log("Query Token:", queryToken);
  console.log("Final Token:", token);
  console.log("Query Email:", queryEmail);
  console.log("SessionStorage Email:", sessionStorage.getItem("resetEmail"));
  console.log("Final Email:", email);

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState({ password: "", confirmPassword: "" });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Validate that we have token (email is optional for reset password flow)
  useEffect(() => {
    console.log("Validation check - Token:", !!token, "Email:", !!email);

    if (!token) {
      toast.error("Invalid password reset link. Missing token.", {
        position: "top-right",
        autoClose: 5000,
      });

      console.log("Redirecting to login due to missing token");
      setTimeout(() => {
        navigate("/auth/login");
      }, 2000);
      return;
    }

    // For admin invite flow (query parameters), email is required
    if (queryToken && !queryEmail) {
      toast.error("Invalid invitation link. Missing email.", {
        position: "top-right",
        autoClose: 5000,
      });

      console.log("Redirecting to login due to missing email in admin invite");
      setTimeout(() => {
        navigate("/auth/login");
      }, 2000);
    }
  }, [token, email, queryToken, queryEmail, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors = { password: "", confirmPassword: "" };
    let isValid = true;

    if (password.length < 8) {
      errors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    if (password !== confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setError(errors);
    if (!isValid) return;

    setIsSubmitting(true);

    try {
      if (!token) {
        toast.error("Missing token. Please try again.", {
          position: "top-right",
          autoClose: 3000,
        });
        return;
      }

      // For admin invite flow, email is required
      if (queryToken && !email) {
        toast.error("Missing email. Please try again.", {
          position: "top-right",
          autoClose: 3000,
        });
        return;
      }

      // For reset password flow, email might come from sessionStorage
      const finalEmail = email || sessionStorage.getItem("resetEmail") || "";

      if (!finalEmail) {
        toast.error("Missing email. Please try again.", {
          position: "top-right",
          autoClose: 3000,
        });
        return;
      }

      await ClientResetPassword(finalEmail, token, password);

      toast.success("Password set successfully! Redirecting to login...", {
        position: "top-right",
        autoClose: 2000,
      });

      setTimeout(() => {
        navigate("/auth/login");
      }, 2000);
    } catch (err: any) {
      toast.error(
        err.response?.data?.message || "Failed to set password. Try again.",
        {
          position: "bottom-right",
          autoClose: 3000,
        }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex lg-only:flex-row xl-only:flex-row 2xl-only:flex-row items-center justify-between w-full h-screen bg-white font-poppins">
      <div className="hidden lg-only:flex xl-only:flex 2xl-only:flex items-start justify-center pt-6 h-screen w-[15%]">
        <img src={logo} alt="Logo" className="w-[155px] h-[30px]" />
      </div>

      <div className="flex lg-only:flex-row xl-only:flex-row 2xl-only:flex-row gap-10 lg-only:w-[85%] xl-only:w-[85%] 2xl-only:w-[85%] items-center justify-between px-10">
        <div className="w-full max-w-lg bg-white shadow-2xl rounded-lg p-8 lg-only:p-8 xl-only:p-10 2xl-only:p-12">
          <div className="flex flex-row gap-2 items-center mb-6">
            <BackButton />
            <h2 className="text-3xl font-bold text-[#282828]">Set Your Password</h2>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-base text-gray-700 font-medium mb-2.5">New Password</label>
              <div className="relative border-gradient rounded-xl">
                <input
                  type={showPassword ? "text" : "password"}
                  className="w-full p-3.5 pr-12 bg-[#F9EFEF] rounded-lg focus:outline-none"
                  placeholder="Enter new password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-4"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <Eye className="h-5 w-5 text-gray-500" /> : <EyeOff className="h-5 w-5 text-gray-500" />}
                </button>
              </div>
              {error.password && <p className="text-red-500 text-sm mt-1.5">{error.password}</p>}
            </div>

            <div>
              <label className="block text-base text-gray-700 font-medium mb-2.5">Confirm Password</label>
              <div className="relative border-gradient rounded-xl">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  className="w-full p-3.5 pr-12 bg-[#F9EFEF] rounded-lg focus:outline-none"
                  placeholder="Confirm new password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-4"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <Eye className="h-5 w-5 text-gray-500" /> : <EyeOff className="h-5 w-5 text-gray-500" />}
                </button>
              </div>
              {error.confirmPassword && <p className="text-red-500 text-sm mt-1.5">{error.confirmPassword}</p>}
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full p-4 text-base bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Processing..." : "Set Password"}
            </button>
          </form>
        </div>

        <div className="hidden lg-only:block xl-only:block 2xl-only:block w-[48%] h-screen">
          <AuthCommonComponent />
        </div>
      </div>
    </div>
  );
};

export default SetPassword;
