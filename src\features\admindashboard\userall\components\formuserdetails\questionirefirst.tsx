// @ts-ignore
import React, { useEffect, useState } from 'react';
import { FirstSubscriptionApi } from './firstqustioniresubscription_api/firstsubscriptiondetails_api'; // Adjust the import path

const QuestionireFirst = ({ userId }: { userId: string }) => {
    const [subscriptionData, setSubscriptionData] = useState<any>(null);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        const fetchSubscription = async () => {
            try {
                setLoading(true);
                const data = await FirstSubscriptionApi(userId);
                setSubscriptionData(data);
            } catch (err) {
                // Log the error for debugging, but don't set an error state
                console.error('Failed to load subscription data:', err);
                // Set subscriptionData to null or an empty object to ensure "No data" shows
                setSubscriptionData(null);
            } finally {
                setLoading(false);
            }
        };

        if (userId) {
            fetchSubscription();
        }
    }, [userId]);

    if (loading) {
        return <div className="p-4">Loading subscription data...</div>;
    }

    return (
        <div className="flex flex-col w-full mt-8">
            <div className="flex flex-row items-center mb-2">
                <h1 className="text-[20px] font-medium">Questionire form</h1>
            </div>

            <div>
                <div className="mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Package Name</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.packageName || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Shift Timing</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.shiftTiming || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Timezone</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.timezone || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Industry</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.industry || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Category</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.category || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Start On</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.startOn || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient rounded-lg">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Description</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA] overflow-y-auto h-full">
                                <p>{subscriptionData?.description || 'No data'}</p>
                            </div>
                        </div>

                        <div className="border-gradient h-[6rem] rounded-lg overflow-hidden">
                            <div className="bg-[#FFF5F7] p-3">
                                <h3 className="text-md font-medium">Created At</h3>
                            </div>
                            <div className="p-3 bg-[#FFF9FA]">
                                <p>{subscriptionData?.createdAt || 'No data'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default QuestionireFirst;