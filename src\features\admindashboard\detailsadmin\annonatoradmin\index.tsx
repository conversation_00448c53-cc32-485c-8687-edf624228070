import { useState, useEffect } from "react";
import Shift<PERSON>hange from "./adminshiftchange";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import AdminAnnotatorCard from "./components/AdminAnnotatorCard";
import { getAdminAnnotators } from "../admindetails_api/admindetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

// Interface matching the API response structure
interface AnnotatorProps {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  packageId: string;
  availableFrom: string | null;
  availableTo: string | null;
  _count: {
    annotatorProjects: number;
  };
  // Additional props for UI display
  image?: string;
  status?: string;
}

export default function Annotators() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAnnotator, setSelectedAnnotator] = useState<AnnotatorProps | null>(null);
  const [annotators, setAnnotators] = useState<AnnotatorProps[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch annotators data from API
  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        setIsLoading(true);
        const response = await getAdminAnnotators();

        if (response && response.data && Array.isArray(response.data.data)) {
          setAnnotators(response.data.data);
        } else {
          setError("Invalid response format");
        }
      } catch (err) {
        console.error("Error fetching annotators:", err);
        setError("Failed to fetch annotators");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnnotators();
  }, []);

  const openModal = (annotator: AnnotatorProps) => {
    setSelectedAnnotator(annotator);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedAnnotator(null);
  };

  const handleSuccess = async () => {
    toast.success("Your shift change request was submitted successfully.", {
      position: 'top-right',
      autoClose: 4000,
    });
    try {
      setIsLoading(true);
      const response = await getAdminAnnotators();
      if (response && response.data && Array.isArray(response.data.data)) {
        setAnnotators(response.data.data);
      } else {
        setError("Invalid response format after shift change");
      }
    } catch (err) {
      console.error("Error refetching annotators:", err);
      setError("Failed to refresh annotators after shift change");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <BrandedGlobalLoader isLoading={true} />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // Show empty state
  if (annotators.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-center">
          <div className="text-lg font-medium mt-10 text-gray-500 flex items-center justify-center text-center">
            <span>There are no annotators available at the moment.</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="grid gap-4 w-full mx-auto max-w-[98%] lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5">
        {annotators.map((annotator, index) => (
          <AdminAnnotatorCard
            key={annotator.id || index}
            annotator={annotator}
            openModal={openModal}
          />
        ))}

        {isModalOpen && selectedAnnotator && (
          <ShiftChange
            annotatorId={selectedAnnotator.id}
            onClose={closeModal}
            onSuccess={handleSuccess}
          />
        )}
      </div>
    </div>
  );
}