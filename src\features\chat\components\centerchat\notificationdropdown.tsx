// components/centerchat/notificationdropdown.tsx
import React, { RefObject } from "react";

interface ChatNotification {
  id: string;
  type: string;
  message: string;
  metadata: {
    groupId?: string;
    conversationId?: string;
    messageId?: string;
    senderName?: string;
    groupName?: string;
    messageText?: string;
    isMention?: boolean;
  };
  isRead: boolean;
  createdAt: string;
}

interface Props {
  dropdownRef: RefObject<HTMLDivElement>;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  notifications: ChatNotification[];
  notificationClick: (notification: ChatNotification) => void;
  loadMore?: () => void;
}

const NotificationDropdown: React.FC<Props> = ({
  dropdownRef,
  open,
  setOpen,
  notifications,
  notificationClick,
  loadMore,
}) => {
  return (
    <div className="relative">
      {open && (
        <div
          ref={dropdownRef}
          className="absolute -right-1 mt-4 w-[15rem] border bg-white overflow-y-scroll h-96 shadow-lg rounded-md p-2 z-[70]"
        >
          <p className="font-semibold text-sm mb-2 border-b">Notifications</p>
          {notifications.length === 0 ? (
            <p className="text-gray-400 text-sm">No notifications</p>
          ) : (
            <>
              {notifications.map((note) => (
                <div
                  key={note.id}
                  className={`p-2 text-sm cursor-pointer rounded ${
                    !note.isRead ? "bg-blue-100" : "bg-gray-100"
                  }`}
                  onClick={() => {
                    notificationClick(note);
                    setOpen(false); // Close dropdown on click
                  }}
                >
                  <p className="font-medium">
                    {note.metadata.isMention && note.metadata.senderName
                      ? `@${note.metadata.senderName}`
                      : note.metadata.senderName || "Unknown"}
                  </p>
                  <p className="text-xs text-gray-600 truncate">
                    {note.metadata.messageText || note.message}
                  </p>
                  <p className="text-xs text-gray-500">
                    {note.metadata.groupName || "No group"}
                  </p>
                </div>
              ))}
              {loadMore && (
                <button
                  className="text-xs text-blue-500 mt-2 w-full text-center"
                  onClick={loadMore}
                >
                  Load More
                </button>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
