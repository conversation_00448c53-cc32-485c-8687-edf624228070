"use client";

import React from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "lucide-react";
import { useResetPasswordMutation } from "../../api/mutation";

type FormValues = {
  method: "auto" | "manual";
  password: string | null;
};

const ResetPasswordModal = ({ userId }: { userId: string }) => {
  const { mutate: resetPassword } = useResetPasswordMutation();
  const { register, handleSubmit, watch, setValue } = useForm<FormValues>({
    defaultValues: {
      method: "auto",
      password: "",
    },
  });

  const method = watch("method");
  const [showPassword, setShowPassword] = React.useState(false);
  const [open, setOpen] = React.useState(false); // Add state for dialog open/close

  const generatePassword = () => {
    const newPassword = Math.random().toString(36).slice(-10);
    setValue("password", newPassword);
  };

  const onSubmit = (data: FormValues) => {
    console.log("Reset Password Data:", data);
    resetPassword({
      id: userId,
      password: data.method === "manual" ? data.password : null,
    });
    setOpen(false); // Close the dialog after submission
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Reset Password
        </Button>
      </DialogTrigger>

      <DialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className=" flex flex-col justify-center items-start gap-3">
            <Label className="text-xl">Password</Label>

            <RadioGroup
              defaultValue="auto"
              className="mt-2 flex flex-col gap-2"
              onValueChange={(value) =>
                setValue("method", value as "auto" | "manual")
              }
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="auto"
                  id="auto"
                  className="border-[#FF577F]"
                />
                <Label htmlFor="auto" className="text-md">
                  Automatic send password link to mail
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="manual"
                  id="manual"
                  className="border-[#FF577F]"
                />
                <Label htmlFor="manual" className="text-md">
                  Create password
                </Label>
              </div>
            </RadioGroup>

            {method === "manual" && (
              <div className="flex mt-3 items-center gap-2">
                <div className="w-[60%] relative border-gradient rounded-lg">
                  <Input
                    type={showPassword ? "text" : "password"}
                    {...register("password")}
                    placeholder="Enter password"
                    className="w-full bg-[#F9EFEF] text-[#5E5E5E] py-6"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2 top-1/2 -translate-y-1/2"
                  >
                    {showPassword ? (
                      <Eye className="w-5 h-5 text-gray-500" />
                    ) : (
                      <EyeOff className="w-6 h-5 text-gray-500" />
                    )}
                  </button>
                </div>

                <Button
                  type="button"
                  variant="gradient"
                  onClick={generatePassword}
                  className="px-6 py-6"
                >
                  Generate Password
                </Button>
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-center mt-6">
            <DialogClose asChild>
              <Button
                type="button"
                className="border-gradient bg-white hover:bg-[#faf5f5] text-black px-14 py-6"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" variant="gradient" className="px-10 py-6">
              Reset Password
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordModal;