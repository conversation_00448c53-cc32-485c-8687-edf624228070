import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive annotator card styles
export const useAnnotatorCardStyles = () => {
  const { isLaptopMd } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Common styles for all screen sizes
  const commonStyles = {
    card: "border border-[#FF577F] rounded-lg shadow-md bg-white flex flex-col h-full",
    profileSection: "flex flex-col relative",
    statusContainer: "flex justify-end items-center absolute top-0 right-0",
    profileContainer: "flex items-center gap-x-1",
    profileImage: "rounded-full",
    nameContainer: "flex flex-col flex-1",
    name: "font-semibold",
    starIcon: "text-[#FFC107]",
    email: "text-gray-500",
    infoContainer: "flex-grow",
    infoRow: "flex items-center justify-between",
    infoLabel: "text-gray-600 font-medium",
    infoValue: "text-gray-800",
    actionContainer: "flex justify-between mt-auto",
  };

  // Get styles based on screen size
  const getStyles = () => {
    // 1024px
    if (windowWidth <= 1024) {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-3`,
        profileSection: `${commonStyles.profileSection} mb-2 pt-5`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-1`,
        profileContainer: `${commonStyles.profileContainer}`,
        profileImage: `${commonStyles.profileImage} w-10 h-10`,
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        email: `${commonStyles.email} text-[11px]`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-2 py-0.5 text-[10px]",
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-2.5`,
        infoRow: `${commonStyles.infoRow} text-[11px] py-0.5`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1 text-[10px] rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-2 py-1 text-white text-[10px] rounded-lg flex-1 text-center"
      };
    }
    // 1440px
    else if (isLaptopMd) {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-4`,
        profileSection: `${commonStyles.profileSection} mb-3 pt-6`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-2`,
        profileContainer: `${commonStyles.profileContainer}`,
        profileImage: `${commonStyles.profileImage} w-10 h-10`,
        name: `${commonStyles.name} text-base`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        email: `${commonStyles.email} text-xs`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-2 py-0.5 text-[10px]",
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-3`,
        infoRow: `${commonStyles.infoRow} text-sm py-0.5`,
        infoLabel: `${commonStyles.infoLabel} w-[100px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-xs rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-2 py-1.5 text-white text-xs rounded-lg flex-1 text-center"
      };
    }
    // 2560px (4K)
    else {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-5`,
        profileSection: `${commonStyles.profileSection} mb-4 pt-7`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-3`,
        profileContainer: `${commonStyles.profileContainer}`,
        profileImage: `${commonStyles.profileImage} w-12 h-12`,
        name: `${commonStyles.name} text-lg`,
        starIcon: `${commonStyles.starIcon} text-xl`,
        email: `${commonStyles.email} text-sm`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-3 py-1 text-xs",
        infoContainer: `${commonStyles.infoContainer} mt-3 space-y-4`,
        infoRow: `${commonStyles.infoRow} text-base py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[120px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-3 mt-4`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-3 py-2 text-sm rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-3 py-2 text-white text-sm rounded-lg flex-1 text-center"
      };
    }
  };

  return getStyles();
};
