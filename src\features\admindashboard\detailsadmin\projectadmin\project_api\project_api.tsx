// proejct_api/project_api.tsx
import { customAxios } from "@/utils/axio-interceptor";
import { baseUrl } from "@/globalurl/baseurl";

export const getAllProjects = async () => {
  const response = await customAxios.get("/v1/projects/get-all-projects");
  return response.data;
};

export const fetchData = async () => {
  try {
    const response = await customAxios.post(
      `${baseUrl}/v1/projects/create-project`
    );

    if (response.status === 200) {
      console.log(response.data);
    }
  } catch (err) {
    console.error("Error fetching order data:", err);
  }
};

export const EnrolledAnnonator = async () => {
  try {
    console.log("Fetching enrolled annotators...");
    const response = await customAxios.get("/v1/projects/annotator-projects");
    console.log("Enrolled annotators API response:", response);

    // Check if we have data in the expected format
    if (response.data && response.data.data) {
      console.log("Projects data:", response.data.data);
    } else {
      console.log("No projects data found in response");
    }

    return response;
  } catch (error) {
    console.error("Error in EnrolledAnnonator:", error);
    throw error;
  }
};

export const fetchAllClientsannotator = async () => {
  try {
    const response = await customAxios.get(
      `/v1/annotator/get-all-annotators`
    );
    if (response.data && response.data.data) {
      return response.data.data.data; // Access the nested data array
    } else {
      return [];
    }
  } catch (error) {
    console.error("Error fetching annotators:", error);
    return [];
  }
};

// Create Task API
export const createProjectTaskApi = async (
  projectId: string,
  taskData: {
    name: string;
    description: string;
    priority: string;
    color: string;
    annotators: string[];
    startDate: string;
    dueDate: string;
  }
) => {
  try {
    const response = await customAxios.post(
      `/v1/tasks/create-task/${projectId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getAllProjectTasks = async (projectId: string) => {
  try {
    const response = await customAxios.get(`/v1/tasks/get-all-tasks`, {
      params: {
        projectId: projectId,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getProjectTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(`/v1/tasks/task-by-id/${taskId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateProjectTaskStatus = async (
  taskId: string,
  taskData: any
) => {
  try {
    const response = await customAxios.put(
      `/v1/tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteProjectTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};

export const getProjectDetails = async (Id: string) => {
  try {
    console.log("API call: getProjectDetails with ID:", Id);
    const response = await customAxios.get(`/v1/projects/project-by-id/${Id}`);
    console.log("API response from getProjectDetails:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching project details:", error);
    throw error;
  }
};

// src/types.ts
export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  status: string;
  createdAt?: string;
  priority?: string;
  name?: string; // Some APIs might return name instead of title
};

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};
