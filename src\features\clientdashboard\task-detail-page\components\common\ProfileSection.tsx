import React from 'react';

interface ProfileSectionProps {
  image: string;
  name: string;
  email: string;
  styles: {
    profileContainer: string;
    profileImage: string;
    nameContainer: string;
    name: string;
    email: string;
  };
}

const ProfileSection: React.FC<ProfileSectionProps> = ({
  image,
  name,
  email,
  styles
}) => {
  return (
    <div className={styles.profileContainer}>
      <img
        src={image}
        alt={name}
        className={styles.profileImage}
      />
      <div className={styles.nameContainer}>
        <h3 className={styles.name}>{name}</h3>
        <p className={styles.email}>{email}</p>
      </div>
    </div>
  );
};

export default ProfileSection;
