// import React, { createContext, useContext, useState, ReactNode } from "react";

// type SelectionContextType = {
//   selectedAnnotators: Record<string, string>;
//   setSelectedAnnotators: React.Dispatch<
//     React.SetStateAction<Record<string, string>>
//   >;
//   selectedCoordinators: Record<string, string>;
//   setSelectedCoordinators: React.Dispatch<
//     React.SetStateAction<Record<string, string>>
//   >;
// };

// const SelectionContext = createContext<SelectionContextType | undefined>(
//   undefined
// );

// export const SelectionProvider = ({ children }: { children: ReactNode }) => {
//   const [selectedAnnotators, setSelectedAnnotators] = useState<
//     Record<string, string>
//   >({});
//   const [selectedCoordinators, setSelectedCoordinators] = useState<
//     Record<string, string>
//   >({});

//   return (
//     <SelectionContext.Provider
//       value={{
//         selectedAnnotators,
//         setSelectedAnnotators,
//         selectedCoordinators,
//         setSelectedCoordinators,
//       }}
//     >
//       {children}
//     </SelectionContext.Provider>
//   );
// };

// export const useSelection = () => {
//   const context = useContext(SelectionContext);
//   if (!context) {
//     throw new Error("useSelection must be used within a SelectionProvider");
//   }
//   return context;
// };

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";

type SelectionContextType = {
  selectedAnnotators: Record<string, string>;
  setSelectedAnnotators: React.Dispatch<
    React.SetStateAction<Record<string, string>>
  >;
  selectedCoordinators: Record<string, string>;
  setSelectedCoordinators: React.Dispatch<
    React.SetStateAction<Record<string, string>>
  >;
};

const SelectionContext = createContext<SelectionContextType | undefined>(
  undefined
);

export const SelectionProvider = ({ children }: { children: ReactNode }) => {
  // Initialize state with localStorage values if they exist
  const [selectedAnnotators, setSelectedAnnotators] = useState<
    Record<string, string>
  >(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("selectedAnnotators");
      return saved ? JSON.parse(saved) : {};
    }
    return {};
  });

  const [selectedCoordinators, setSelectedCoordinators] = useState<
    Record<string, string>
  >(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("selectedCoordinators");
      return saved ? JSON.parse(saved) : {};
    }
    return {};
  });

  // Persist to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem(
      "selectedAnnotators",
      JSON.stringify(selectedAnnotators)
    );
  }, [selectedAnnotators]);

  useEffect(() => {
    localStorage.setItem(
      "selectedCoordinators",
      JSON.stringify(selectedCoordinators)
    );
  }, [selectedCoordinators]);

  // Create wrapped setters that can handle both function and direct value updates
  const wrappedSetAnnotators: React.Dispatch<
    React.SetStateAction<Record<string, string>>
  > = (value) => {
    setSelectedAnnotators((prev) => {
      const newValue = typeof value === "function" ? value(prev) : value;
      return newValue;
    });
  };

  const wrappedSetCoordinators: React.Dispatch<
    React.SetStateAction<Record<string, string>>
  > = (value) => {
    setSelectedCoordinators((prev) => {
      const newValue = typeof value === "function" ? value(prev) : value;
      return newValue;
    });
  };

  return (
    <SelectionContext.Provider
      value={{
        selectedAnnotators,
        setSelectedAnnotators: wrappedSetAnnotators,
        selectedCoordinators,
        setSelectedCoordinators: wrappedSetCoordinators,
      }}
    >
      {children}
    </SelectionContext.Provider>
  );
};

export const useSelection = () => {
  const context = useContext(SelectionContext);
  if (!context) {
    throw new Error("useSelection must be used within a SelectionProvider");
  }
  return context;
};
