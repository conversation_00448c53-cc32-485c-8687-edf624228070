// // projectdata.ts
// export interface Project {
//     id: number;
//     title: string;
//     description: string;
//     startDate: string;
//     duration: string;
//     postedBy: string;
//     status: "Ongoing" | "Completed" | "Upcoming";
//     level: "low" | "medium" | "hard";
//   }
  
//   const projects: Project[] = [
//     {
//       id: 1,
//       title: "AI Data Annotator",
//       description:
//         "We are seeking a highly skilled and motivated individual with expertise in technical drawing.",
//       startDate: "10/02/25",
//       duration: "3 weeks",
//       postedBy: "<PERSON>",
//       status: "Ongoing",
//       level: "medium",
//     },
//     {
//       id: 2,
//       title: "NLP Model Trainer",
//       description:
//         "Join us to build and train NLP models for multilingual datasets in healthcare domain goimg.",
//       startDate: "15/03/25",
//       duration: "4 weeks",
//       postedBy: "Sarah L",
//       status: "Upcoming",
//       level: "hard",
//     },
//     {
//       id: 3,
//       title: "UX Improvement ",
//       description:
//         "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//       startDate: "01/03/25",
//       duration: "2 weeks",
//       postedBy: "Mark D",
//       status: "Completed",
//       level: "low",
//     },

//     {
//         id: 4,
//         title: "UX Improvement ",
//         description:
//           "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//         startDate: "01/03/25",
//         duration: "2 weeks",
//         postedBy: "Mark D",
//         status: "Completed",
//         level: "medium",
//       },
//       {
//         id: 5,
//         title: "UX Improvement ",
//         description:
//           "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//         startDate: "01/03/25",
//         duration: "2 weeks",
//         postedBy: "Mark D",
//         status: "Completed",
//         level: "hard",
//       },
//       {
//         id: 6,
//         title: "UX Improvement ",
//         description:
//           "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//         startDate: "01/03/25",
//         duration: "2 weeks",
//         postedBy: "Mark D",
//         status: "Completed",
//         level: "low",
//       },
//       {
//         id: 7,
//         title: "UX Improvement ",
//         description:
//           "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//         startDate: "01/03/25",
//         duration: "2 weeks",
//         postedBy: "Mark D",
//         status: "Completed",
//         level: "hard",
//       },
//       {
//         id: 8,
//         title: "UX Improvement ",
//         description:
//           "Analyze user behavior and deliver UX improvement suggestions backed by research.",
//         startDate: "01/03/25",
//         duration: "2 weeks",
//         postedBy: "Mark D",
//         status: "Completed",
//         level: "low",
//       },
//   ];
  
//   export default projects;
  