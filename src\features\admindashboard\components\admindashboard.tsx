// /pages/DashboardPage.tsx
import React, { useEffect, useState } from "react";
import { FiUsers } from "react-icons/fi";
import { GiSandsOfTime } from "react-icons/gi";
import { BsBagDash } from "react-icons/bs";
import { MdOutlineAddToPhotos } from "react-icons/md";
import AdminKanbanBoard from "./adminkanban";
import { getAdminTopDashboardData } from "../api/api";
import { Link } from "react-router-dom";

type DashboardData = {
  totalProjects: number;
  activeProjects: number;
  coordinatorCount: number;
  clientCount: number;
  annotatorCount: number;
  activeAnnotators: number;
};

const AdminDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    totalProjects: 0,
    activeProjects: 0,
    coordinatorCount: 0,
    clientCount: 0,
    annotatorCount: 0,
    activeAnnotators: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await getAdminTopDashboardData();
        if (response && response.data) {
          setDashboardData(response.data);
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <div className="w-full h-full p-6 lg-only:px-6 xl-only:px-8 2xl-only:px-10">
      {/* top section */}
      <div className="grid gap-5 mb-6 lg-only:grid-cols-2 xl:grid-cols-4">
        <Link to="/admin/admindetails/projects">
          <div className="bg-white border-gradient rounded-lg shadow-sm p-4 flex flex-col justify-between lg-only:h-[90px] xl-only:h-[100px] 2xl-only:h-[120px]">
            <span className="flex items-center gap-3">
              <BsBagDash className="text-primary lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl" />
              <p className="text-gray-500 font-medium lg-only:text-base xl-only:text-lg 2xl-only:text-xl">
                Acitve Projects
              </p>
            </span>
            <p className="font-semibold lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl">
              {loading ? "..." : dashboardData.totalProjects}
            </p>
          </div>
        </Link>

        <Link to="/admin/admindetails/annotators">
          <div className="bg-white border-gradient rounded-lg shadow-sm p-4 flex flex-col justify-between lg-only:h-[90px] xl-only:h-[100px] 2xl-only:h-[120px]">
            <span className="flex items-center gap-3">
              <FiUsers className="text-primary lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl" />
              <p className="text-gray-500 font-medium lg-only:text-base xl-only:text-lg 2xl-only:text-xl">
                Total Annotators
              </p>
            </span>
            <p className="font-semibold lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl">
              {loading ? "..." : dashboardData.annotatorCount}
            </p>
          </div>
        </Link>

        <Link to="/admin/admindetails/clients">
          <div className="bg-white border-gradient rounded-lg shadow-sm p-4 flex flex-col justify-between lg-only:h-[90px] xl-only:h-[100px] 2xl-only:h-[120px]">
            <span className="flex items-center gap-3">
              <GiSandsOfTime className="text-primary lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl" />

              <p className="text-gray-500 font-medium lg-only:text-base xl-only:text-lg 2xl-only:text-xl">
                Total Clients
              </p>
            </span>
            <p className="font-semibold lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl">
              {loading ? "..." : dashboardData.clientCount}
            </p>
          </div>
        </Link>

        <Link to="/admin/admindetails/coordinators">
          <div className="bg-white border-gradient rounded-lg shadow-sm p-4 flex flex-col justify-between lg-only:h-[90px] xl-only:h-[100px] 2xl-only:h-[120px]">
            <span className="flex items-center gap-3">
              <MdOutlineAddToPhotos className="text-primary lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl" />
              <p className="text-gray-500 font-medium lg-only:text-base xl-only:text-lg 2xl-only:text-xl">
                Total Coordinators
              </p>
            </span>
            <p className="font-semibold lg-only:text-xl xl-only:text-2xl 2xl-only:text-3xl">
              {loading ? "..." : dashboardData.coordinatorCount}
            </p>
          </div>
        </Link>
      </div>

      <div className="flex lg-only:flex-col xl:flex-row gap-6 lg-only:h-[calc(100vh-250px)] xl-only:h-[calc(100vh-220px)] 2xl-only:h-[calc(100vh-250px)]">
        <div className="w-full h-full">
          <AdminKanbanBoard />
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
