"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";

import { useNavigate } from "react-router-dom"; // ✅ React Router import

// Helper function to format date to dd/mm/yy
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit'
  });
};

// Helper function to calculate duration between two dates
const calculateDuration = (startDate: string, dueDate: string) => {
  if (!startDate || !dueDate) return "-";

  const start = new Date(startDate);
  const end = new Date(dueDate);

  // Calculate difference in days
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Format as weeks if more than 7 days
  if (diffDays >= 7) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} ${weeks === 1 ? 'Week' : 'Weeks'}`;
  }

  return `${diffDays} ${diffDays === 1 ? 'Day' : 'Days'}`;
};

export const useAdminColumns = (): ColumnDef<any>[] => {
  const navigate = useNavigate(); // ✅ Initialize navigation hook

  return [
    {
      accessorKey: "name",
      header: () => (
        <div
          className="w-[200px] font-medium cursor-pointer"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Project Name
        </div>
      ),
      cell: ({ row }) => <div className="pl-4 text-[14px] font-medium">{row.getValue("name")}</div>,
    },
    {
      accessorKey: "startDate",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Started on
        </Button>
      ),
      cell: ({ row }) => <div className="pl-4 text-[14px] font-medium">{formatDate(row.getValue("startDate"))}</div>,
    },
    {
      accessorKey: "duration",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Duration
        </Button>
      ),
      cell: ({ row }) => {
        const project = row.original;
        return <div className="pl-4 text-[14px] font-medium">{calculateDuration(project.startDate, project.dueDate)}</div>;
      },
    },
    {
      accessorKey: "priority",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Priority
        </Button>
      ),
      cell: ({ row }) => {
        const priority = row.getValue("priority") as string;
        let bgColor = "bg-[#2525AB]";

        // Set different background colors based on priority
        if (priority === "HIGH") bgColor = "bg-[#E91C24]";
        if (priority === "MEDIUM") bgColor = "bg-[#E96B1C] ";
        if (priority === "LOW") bgColor = "bg-[#2525AB]";

        return (
          <div className={`${bgColor} w-[5rem] h-6  rounded-3xl text-white flex items-center justify-center `}>
            <span className={`  rounded-3xl text-white`}>
              {priority}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "postedBy",
      header: () => (
        <Button
          variant="ghost"
       //   onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Posted by
        </Button>
      ),
      cell: ({ row }) => {
        const project = row.original;
        return <div className="pl-4 text-[14px] font-medium">{project.createdBy?.name || "-"}</div>;
      },
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        let borderColor = "border-[#E96B1C]";
        let textColor = "text-[#E96B1C]";

        // Set different colors based on status
        if (status === "COMPLETED") {
          borderColor = "border-[#28A745]";
          textColor = "text-[#28A745]";
        } else if (status === "IN_PROGRESS") {
          borderColor = "border-[#2525AB]";
          textColor = "text-[#2525AB]";
        }

        return (
          <div className="pl-4">
            <span className={`border ${borderColor} px-4 py-2 rounded-3xl ${textColor}`}>
              {status}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        const project = row.original;
        return (
          <div className="pl-4">
            <Button
              variant={"gradient"}
              className="px-7 rounded-xl"
              onClick={() => navigate(`/admin/adminproject-details?id=${project.id}`)} // Pass project ID in URL
            >
              View Details
            </Button>
          </div>
        );
      },
    },
  ];
};
