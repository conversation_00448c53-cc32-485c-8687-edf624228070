import { customAxios } from "@/utils/axio-interceptor";

export const ClientAnnonatorAll = async (user: string | undefined) => {
  const response = await customAxios.get(
    user === "CLIENT"
      ? "/v1/dashboard/client-annotators"
      : "/v1/dashboard/cooworker/client-annotators"
  );
  return response.data;
};

//swift changes api using postman endpoint api
export const clientChangeShift = async (data: any) => {
  const response = await customAxios.post(`/v1/shift/create-shift-change`, {
    annotatorId: data.annotatorId,
    newFrom: data.availableFrom,
    newTo: data.availableTo,
    // description: data.note // Will be added later when backend supports it
  });
  return response.data;
};
