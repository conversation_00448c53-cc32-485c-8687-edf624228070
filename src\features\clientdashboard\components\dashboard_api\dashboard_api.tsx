import { customAxios } from "@/utils/axio-interceptor"; // ✅ Import customAxios from the interceptor file

export const getDashboardDataAnnonators = async () => {
  const response = await customAxios.get("/v1/dashboard/client-annotators");
  return response.data;
};

export const AllCooworkerAnoonatorClient = async () => {
  const response = await customAxios.get("/v1/dashboard/cooworker/client-annotators");
  return response.data;
};

export const getDashboardDataProjects = async () => {
  const response = await customAxios.get("/v1/projects/client-projects");
  return response.data;
};

export const getAllClientCooworkerProjects = async () => {
  const response = await customAxios.get("/v1/projects/cooworker/client-projects");
  return response.data;
};