import { Annotator } from "@/types/onboarding.types";
import { PaginatedResponse } from "@/types/generics";
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import {
  getAnnotatorList,
  getCoordinatorList,
  getMatchMakingList,
  matchMakingList,
} from "./matchmaking.api";
import { ClientData } from "@/types/matchmaking.types";

export const useGetMatchMakingListQuery = ({
  page = 1,
  limit = 50,
}: {
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ["matchmaking", { page, limit }],
    queryFn: () =>
      matchMakingList({
        page,
        limit,
      }),
    // staleTime: 1000 * 60 * 5, // 5 minutes
    // cacheTime: 1000 * 60 * 10, // 10 minutes
    // refetchOnWindowFocus: false,
    // refetchOnMount: false,
    // refetchInterval: 1000 * 60, // every minute
  });
};

// export const usePackageList = () => {
//   return useInfiniteQuery({
//     queryKey: ["packages"],
//     queryFn: ({ pageParam = 1 }) => getPackageList({ pageParam }),
//     initialPageParam: 1, // ✅ must pass this
//     getNextPageParam: (lastPage) => {
//       if (lastPage.currentPage < lastPage.totalPages) {
//         return lastPage.currentPage + 1;
//       }
//       return undefined;
//     },
//   });
// };

export const useInfiniteMatchMakingList = () => {
  return useInfiniteQuery<PaginatedResponse<ClientData>, Error>({
    queryKey: ["matchmaking"],
    queryFn: getMatchMakingList,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.nextPage : undefined,
    initialPageParam: 1, // default is 0, you can override it here
  });
};

export const useInfiniteUsers = () => {
  return useInfiniteQuery<PaginatedResponse<Annotator>, Error>({
    queryKey: ["users"],
    queryFn: getAnnotatorList,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.nextPage : undefined,
    initialPageParam: 1, // default is 0, you can override it here
  });
};

export const useInfiniteCoordinators = () => {
  return useInfiniteQuery<PaginatedResponse<Annotator>, Error>({
    queryKey: ["coordinators"],
    queryFn: getCoordinatorList,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.nextPage : undefined,
    initialPageParam: 1, // default is 0, you can override it here
  });
};
