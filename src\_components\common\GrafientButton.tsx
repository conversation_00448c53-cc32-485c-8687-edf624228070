import React from "react";

interface GradientButtonProps {
  text: string;
  onClick: () => void;
  className?: string;
}

const GradientButton: React.FC<GradientButtonProps> = ({
  text,
  onClick,
  className,
}) => {
  return (
    <button
      onClick={onClick}
      className={`px-5 py-2 text-white text-lg font-bold rounded-lg transition-all
        bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] 
        
        ${className}`}
    >
      {text}
    </button>
  );
};

export default GradientButton;
