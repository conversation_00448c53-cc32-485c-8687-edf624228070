
// import { BankDetailsType } from "./bankdetails.type";

// export const dummyData: BankDetailsType[] = [
//   {
//     client: "<PERSON><PERSON><PERSON>",
//     id: "BNK001",
//     startDate: "2023-01-15",
//     packageName: "Premium Package",
//     amount: 12500,
//     paymentStatus: "Paid",
//     paymentoption: "Bank Transfer",
//   },
//   {
//     client: "Kunal Verma",
//     id: "BNK002",
//     startDate: "2023-02-10",
//     packageName: "Standard Package",
//     amount: 8750,
//     paymentStatus: "Paid",
//     paymentoption: "Credit Card",
//   },
//   {
//     client: "Amit <PERSON>",
//     id: "BNK003",
//     startDate: "2023-03-05",
//     packageName: "Premium Package",
//     amount: 15000,
//     paymentStatus: "Paid",
//     paymentoption: "PayPal",
//   },
//   {
//     client: "<PERSON><PERSON>",
//     id: "BNK004",
//     startDate: "2023-03-22",
//     packageName: "Basic Package",
//     amount: 5000,
//     paymentStatus: "Paid",
//     paymentoption: "Bank Transfer",
//   },
//   {
//     client: "Rahul Gupta",
//     id: "BNK005",
//     startDate: "2023-04-10",
//     packageName: "Standard Package",
//     amount: 9200,
//     paymentStatus: "Paid",
//     paymentoption: "Credit Card",
//   },
//   {
//     client: "Neha Patel",
//     id: "BNK006",
//     startDate: "2023-05-18",
//     packageName: "Premium Package",
//     amount: 18000,
//     paymentStatus: "Paid",
//     paymentoption: "PayPal",
//   },
//   {
//     client: "Vikram Malhotra",
//     id: "BNK007",
//     startDate: "2023-06-05",
//     packageName: "Basic Package",
//     amount: 4500,
//     paymentStatus: "Paid",
//     paymentoption: "Bank Transfer",
//   },
//   {
//     client: "Ananya Desai",
//     id: "BNK008",
//     startDate: "2023-07-12",
//     packageName: "Standard Package",
//     amount: 7800,
//     paymentStatus: "Paid",
//     paymentoption: "Credit Card",
//   }
// ];
