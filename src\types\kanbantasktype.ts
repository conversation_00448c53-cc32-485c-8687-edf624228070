export type TaskType = {
    id: string;
    title: string;
    columnId: string;
    createdAt: string;
    updatedAt: string;
};

export type ColumnType = {
    id: string;
    title: string;
    createdAt: string;
    updatedAt: string;
};

export type DetailsTask = {
    id: string;
    title: string;
    description: string;
    level: string;
    priority: string;
    dueDate?: string | null;
    status?: string;
    color?: string;
    endDate: string;
    // Additional fields from API response
    assignedToId?: string;
    createdById?: string;
    startDate?: string;
    createdAt?: string;
    updatedAt?: string;
};


