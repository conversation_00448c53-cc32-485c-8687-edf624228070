import { customAxios } from "@/utils/axio-interceptor";
import { AttendanceRecord } from "../types/attendence.types";
import { AttendanceResponse } from "../types/attendencelog.types";

export const EnrolledAnnonator = async () => {
  try {
    console.log("Fetching enrolled annotators...");
    const response = await customAxios.get("/v1/projects/annotator-projects");
    console.log("Enrolled annotators API response:", response);

    // Check if we have data in the expected format
    if (response.data && response.data.data) {
      console.log("Projects data:", response.data.data);
    } else {
      console.log("No projects data found in response");
    }

    return response;
  } catch (error) {
    console.error("Error in EnrolledAnnonator:", error);
    throw error;
  }
};

// Create Task API
export const createTaskApi = async (taskData: any) => {
  try {
    const response = await customAxios.post(
      "/v1/self-tasks/create-task",
      taskData
    ); // Replace with your actual endpoint
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getTasks = async () => {
  try {
    const response = await customAxios.get("/v1/self-tasks/get-all-task");
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(
      `/v1/self-tasks/get-single-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateTaskStatus = async (taskId: string, taskData: any) => {
  try {
    const response = await customAxios.patch(
      `/v1/self-tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/self-tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};

// src/types.ts
export type Task = {
  id: string;
  title: string;
  description: string;
  level: string;
  status: string;
  createdAt?: string;
  priority?: string;
  name?: string; // Some APIs might return name instead of title
};

export type Column = {
  id: string;
  title: string;
  tasks: Task[];
};

//annonator assign projects
export const getAnnonatorProjects = async () => {
  try {
    const response = await customAxios.get("/v1/projects/annotator-projects");
    return response.data;
  } catch (error) {
    console.error("Error fetching projects:", error);
    return [];
  }
};
//annonator project details by id
export const getAnnonatorProjectDetails = async (Id: string) => {
  try {
    console.log("API call: getProjectDetails with ID:", Id);
    const response = await customAxios.get(`/v1/projects/project-by-id/${Id}`);
    console.log("API response from getProjectDetails:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching project details:", error);
    throw error;
  }
};
// Attendence
export const getAttendenceLog = async (): Promise<AttendanceRecord> => {
  const response = await customAxios.get(`/v1/attendance/today-log`);
  return response.data.data;
};

export const clockIn = async () => {
  const response = await customAxios.post(`/v1/attendance/clock-in`);
  return response;
};

export const clockOut = async () => {
  const response = await customAxios.post(`/v1/attendance/clock-out`);
  return response;
};

export const takeBreak = async () => {
  const response = await customAxios.post(`/v1/attendance/start-break`);
  return response;
};

export const undoBreak = async () => {
  const response = await customAxios.post(`/v1/attendance/end-break`);
  return response;
};

export const getMyAttendenceLogs = async ({
  page = 1,
  limit = 10,
  from,
  to,
}: {
  page: number;
  limit: number;
  from?: string;
  to?: string;
}): Promise<AttendanceResponse> => {
  const response = await customAxios.get(
    `/v1/attendance/my-attendance-history`,
    {
      params: {
        page,
        limit,
        from: from ? from : undefined,
        to: to ? to : undefined,
      },
    }
  );
  return response.data;
};

// userprofile for pick name real data from backend
export const userprofileannonator = async () => {
  const response = await customAxios.get(`/v1/users/profile`);
  return response.data;
};

// annontor descrition samll description where create and update use only post method
export const AnnonatordescriptionTitle = async (data: {
  description: string;
}) => {
  try {
    const response = await customAxios.post(
      `/v1/annotator/update-description`,
      data
    );
    return response.data;
  } catch (error) {
    console.error("Error updating description:", error);
    throw error;
  }
};

export const createProjectTaskApi = async (
  projectId: string,
  taskData: {
    name: string;
    description: string;
    priority: string;
    color: string;
    annotators: string[];
    startDate: string;
    dueDate: string;
  }
) => {
  try {
    const response = await customAxios.post(
      `/v1/tasks/create-task/${projectId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error creating task", error);
    throw error;
  }
};

export const getAllProjectTasks = async (projectId: string) => {
  try {
    const response = await customAxios.get(`/v1/tasks/get-all-tasks`, {
      params: {
        projectId: projectId,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return [];
  }
};

export const getProjectTaskById = async (taskId: string) => {
  try {
    const response = await customAxios.get(`/v1/tasks/task-by-id/${taskId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching task:", error);
    return null;
  }
};

export const updateProjectTaskStatus = async (
  taskId: string,
  taskData: any
) => {
  try {
    const response = await customAxios.put(
      `/v1/tasks/update-task/${taskId}`,
      taskData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating task:", error);
    throw error;
  }
};

export const deleteProjectTask = async (taskId: string) => {
  try {
    const response = await customAxios.delete(
      `/v1/tasks/delete-task/${taskId}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting task:", error);
    throw error;
  }
};
