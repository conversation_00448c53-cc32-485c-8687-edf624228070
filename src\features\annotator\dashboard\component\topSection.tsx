import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useGetAttendenceLog } from "../../annonator_api/annotator.query";
import Break from "./common/breakandleave";
import TimeInOut from "./common/TimeIn";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
import { Link } from "react-router-dom";
import { AttendanceRecord } from "@/features/annotator/types/attendence.types";

const TopSection: React.FC = () => {
  const { data, isLoading } = useGetAttendenceLog();
  const [projectCount, setProjectCount] = useState<number>(0);
  const [loadingProjects, setLoadingProjects] = useState<boolean>(true);

  const fetchAnnotatorProjectCount = async () => {
    try {
      setLoadingProjects(true);
      const response = await customAxios.get<{ count: number }>("/v1/projects/annotator-projects");
      setProjectCount(response.data.count || 0);
    } catch (error) {
      console.error(error);
      setProjectCount(0);
    } finally {
      setLoadingProjects(false);
    }
  };

  useEffect(() => {
    fetchAnnotatorProjectCount();
  }, []);

  if (isLoading || loadingProjects) {
    return <BrandedGlobalLoader isLoading />;
  }

  return (
    <div className="mt-4 font-poppins w-full">
      {/* Responsive grid: 1x3 row for all breakpoints with justify-between for xl and 2xl */}
      <div className="grid grid-cols-3 gap-4 lg-only:gap-6 lg-only:px-4 xl:gap-8 xl:justify-between 2xl:gap-10 2xl:justify-between">
        {/* Active Projects */}
        <Link to='/annotator/annotatordetail'>
        <div
          className="flex flex-col justify-center border-gradient rounded-xl
          lg-only:p-4 lg-only:min-h-[140px] lg-only:max-w-[30vw]
          xl:p-5 xl:min-h-[160px] xl:max-w-[28vw]
          2xl:p-6 2xl:min-h-[180px] 2xl:max-w-[26vw]
          bg-white shadow-md"
        >
          <span className="flex  items-start gap-2">
            <svg
              className="text-[#D53148] lg-only:w-5 lg-only:h-5 xl:w-6 xl:h-6 2xl:w-7 2xl:h-7"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
            </svg>
            <p className="text-[#545454] font-medium lg-only:text-base xl:text-lg 2xl:text-xl">
              Active Projects
            </p>
          </span>
          <p className="text-[#545454] font-semibold lg-only:text-xl xl:text-2xl 2xl:text-3xl mt-2">
            {projectCount}
          </p>
        </div>
            </Link>

        <Break data={data as AttendanceRecord | undefined} />

        <TimeInOut Tittle="Time In-Out" data={data as AttendanceRecord | undefined} />
      </div>
    </div>
  );
};

export default TopSection;