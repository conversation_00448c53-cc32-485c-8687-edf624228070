// import { Route, Routes } from "react-router-dom";

// // import AddOn from "@/features/add-on";
// import SettingFile from "@/features/settings/setting-file";
// import FAQs from "@/features/settings/faqs";
// import Subscription from "../../billing/subscription";

// export const DashboardPage = () => {
//   return (
//     <Routes>
//       {/* <Route path="/" element={<Dashboard />} /> */}

//       {/* billing children ka code dropdown ke  */}
//       <Route path="/billing/subscription" element={<Subscription />} />

//       {/* setting support faqs */}
//       <Route path="/settings" element={<SettingFile />} />
//       <Route path="/faqs" element={<FAQs />} />
//     </Routes>
//   );
// };
