import { customAxios } from "@/utils/axio-interceptor";

interface BankTransferData {
  packageId: string;
  amount: string;
  currency: string;
  transactionId: string;
  bankHolderName: string;
  accountNumber?: string | null;
  ifscCode?: string;
  bankName: string;
  screenshot: File;
  country: string;
  city: string;
  zipcode: string;
  state: string;
  street: string;
  transactionDate: string;
  transferedAccNo: string;
  availableFrom: string;
  availableTo: string;
  timezone: string;
  industry: string;
  category: string;
  startOn: string;
  description: string;
}

export const PostBankTransfer = async (data: BankTransferData) => {
  const formData = new FormData();
  
  // Append all fields to formData
  formData.append('packageId', data.packageId);
  formData.append('amount', data.amount);
  formData.append('currency', data.currency || 'USD'); // Default currency
  formData.append('transactionId', data.transactionId);
  formData.append('bankHolderName', data.bankHolderName);
  if (data.accountNumber) formData.append('accountNumber', data.accountNumber);
  if (data.ifscCode) formData.append('ifscCode', data.ifscCode);
  formData.append('bankName', data.bankName);
  formData.append('screenshot', data.screenshot);
  formData.append('country', data.country);
  formData.append('city', data.city);
  formData.append('zipcode', data.zipcode);
  formData.append('state', data.state);
  formData.append('street', data.street);
  formData.append('transactionDate', data.transactionDate);
  formData.append('transferedAccNo', data.transferedAccNo);
  formData.append('availableFrom', data.availableFrom);
  formData.append('availableTo', data.availableTo);
  formData.append('timezone', data.timezone);
  formData.append('industry', data.industry);
  formData.append('category', data.category);
  formData.append('startOn', data.startOn);
  formData.append('description', data.description);

  try {
    const response = await customAxios.post('/v1/bank-transfer/submit', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};