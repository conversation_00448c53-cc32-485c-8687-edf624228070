import React, { useEffect, useState } from "react";
import { IoMdClose } from "react-icons/io";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { createFAQ, updateFAQ } from "../faq_setting_api/faq_api/faqsapi";

interface AddFAQModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: { id: string; question: string; answer: string };
  isEditMode?: boolean;
  onSuccess?: () => void;
}

const AddFAQModal: React.FC<AddFAQModalProps> = ({
  isOpen,
  onClose,
  initialData,
  isEditMode = false,
  onSuccess,
}) => {
  const [question, setQuestion] = useState("");
  const [answer, setAnswer] = useState("");

  useEffect(() => {
    if (isOpen && initialData) {
      setQuestion(initialData.question || "");
      setAnswer(initialData.answer || "");
    } else {
      setQuestion("");
      setAnswer("");
    }
  }, [isOpen, initialData]);

  const handleSave = async () => {
    if (!question.trim() || !answer.trim()) {
      toast.error("Question and Answer are required", {
        position: 'top-right',
        autoClose: 3000,
      });
      return;
    }

    try {
      if (isEditMode && initialData?.id) {
        await updateFAQ({ id: initialData.id, question, answer });
        toast.success("Your FAQ has been updated successfully.", {
          position: 'top-right',
          autoClose: 3000,
        });
      } else {
        await createFAQ({ question, answer });
        toast.success("Your FAQ has been added successfully.", {
          position: 'top-right',
          autoClose: 3000,
        });
        setQuestion("");
        setAnswer("");
      }

      if (onSuccess) onSuccess();
      onClose(); // Close the modal on successful save/update
    } catch (error: any) {
      const errorMessage = error.message || "Something went wrong.";
      toast.error(errorMessage, {
        position: 'top-right',
        autoClose: 3000,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-white rounded-xl p-6">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            {isEditMode ? "Edit FAQ" : "Add New FAQ"}
          </DialogTitle>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-2xl text-gray-600 hover:text-[#D53148]"
          >
            <IoMdClose />
          </button>
        </DialogHeader>

        <div className="mb-4">
          <label className="block mb-1 text-gray-700 font-medium">Question</label>
          <div className="border-gradient rounded-[8px] w-full">
            <Input
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              className="w-full bg-[#F9EFEF] outline-none"
              placeholder="Enter question"
            />
          </div>
        </div>

        <div className="mb-6">
          <label className="block mb-1 text-gray-700 font-medium">Answer</label>
          <div className="border-gradient rounded-[8px] w-full">
            <Textarea
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              className="w-full h-[5rem] max-h-[9rem] bg-[#F9EFEF] outline-none"
              placeholder="Enter answer"
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            className="bg-white text-gray-700 hover:bg-gray-300 px-10 border-gradient"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            className="text-white px-10"
            onClick={handleSave}
          >
            {isEditMode ? "Update" : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddFAQModal;