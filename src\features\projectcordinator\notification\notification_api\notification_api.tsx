import { customAxios } from "@/utils/axio-interceptor";



export const getNotificationShift = async () => {
    try {
      const response = await customAxios.get("/v1/shift/pending-shift-requests");
      return response.data;
    } catch (error) {
      console.error("Error fetching notification:", error);
      return [];
    }
  };
  


  export const acceptShiftRequest = async (id: string) => {
    try {
      const response = await customAxios.post(`/v1/shift/approve-shift-change/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error approving shift request:", error);
      throw error;
    }
  };
  
 
  
// Get all shift notifications
export const GetAllShiftNotification = async () => {
  try {
    const response = await customAxios.get("/v1/shift/all-shift-requests");
    return response.data;
  } catch (error) {
    console.error("Error fetching all shift requests:", error);
    throw error;
  }
};


// Get all shift notifications
export const GetClientCordinatorShiftNotification = async () => {
  try {
    const response = await customAxios.get("/v1/shift/shift-requests");
    return response.data;
  } catch (error) {
    console.error("Error fetching all shift requests:", error);
    throw error;
  }
};



