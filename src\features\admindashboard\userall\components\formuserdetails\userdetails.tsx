// @ts-ignore
import React, { useEffect, useState } from "react";
import { BackButton } from "@/_components/common";
import OtpFailed from "./otpfailed";
import { useParams } from "react-router-dom";
import { getUserProfile } from "../../userall_api/userapi";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import QuestionireFirst from "./questionirefirst";

const UserDetails = () => {
  const { id } = useParams();
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        // Fetch user data
        const userResponse = await getUserProfile(id);
        console.log("User data fetched:", userResponse);

        if (userResponse && userResponse.data) {
          setUserData(userResponse.data);
        } else {
          setError("Failed to load user data");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("An error occurred while fetching data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  if (error) {
    return <div className="p-4 text-red-500">{error}</div>;
  }

  return (
    <div className="p-2">
      <div>
        <div className="flex gap-1 mb-3 items-center">
          <BackButton />
          <div>
            <h2 className="text-xl font-semibold">
              {userData?.user?.name || "User"} Details
            </h2>
          </div>
        </div>

        {/* User details boxed details */}
        <div className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">User ID</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.user?.id || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Username</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.user?.name || "No data"}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Company Name</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.companyName || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Phone Number</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.phoneNumber || "No data"}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Website</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.website || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Address</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.address || "No data"}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Postal Code</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.postalCode || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Country</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.country || "No data"}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">State/Province</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.profile?.stateProvince || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Joined On</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>
                  {userData?.profile?.createdAt
                    ? new Date(userData.profile.createdAt).toLocaleDateString(
                        "en-US",
                        {
                          day: "numeric",
                          month: "long",
                          year: "numeric",
                        }
                      )
                    : "No data"}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Role</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.user?.role || "No data"}</p>
              </div>
            </div>

            <div className="border-gradient rounded-lg overflow-hidden">
              <div className="bg-[#FFF5F7] p-3">
                <h3 className="text-md font-medium">Timezone</h3>
              </div>
              <div className="p-3 bg-[#FFF9FA]">
                <p>{userData?.user?.timezone || "No data"}</p>
              </div>
            </div>
          </div>
        </div>

        {/* OTP failed component */}
        <OtpFailed
          emailVerified={userData?.user?.emailVerified}
          email={userData?.user?.email}
        />

        <div>
          <QuestionireFirst userId={id || ""} />
        </div>
      </div>
    </div>
  );
};

export default UserDetails;
