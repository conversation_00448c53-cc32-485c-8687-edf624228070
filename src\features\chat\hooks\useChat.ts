import { useEffect } from 'react';
import { Socket } from 'socket.io-client';
import { Message, formatMessage } from '../utils/messageUtils';

// Hook for joining a direct message room
export interface UseDMRoomProps {
  socket: Socket | null;
  userId: string;
  otherUserId: string;
  onJoinedDm?: (data: any) => void;
  onError?: (error: any) => void;
}

export function useJoinDMRoom({
  socket,
  userId,
  otherUserId,
  onJoinedDm,
  onError,
}: UseDMRoomProps) {
  useEffect(() => {
    if (!socket || !userId || !otherUserId) return;

    // Create a consistent room name (sorted to avoid duplicates)
    const roomId =
      userId < otherUserId
        ? `dm:${userId}-${otherUserId}`
        : `dm:${otherUserId}-${userId}`;

    // Join the DM room
    socket.emit("join_dm", { userId, otherUserId });

    // Set up event listeners if callbacks are provided
    if (onJoinedDm) {
      socket.on("joined_dm", onJoinedDm);
    }
    
    if (onError) {
      socket.on("error", onError);
    }

    return () => {
      socket.emit("leave_dm_room", { roomId });
      if (onJoinedDm) socket.off("joined_dm", onJoinedDm);
      if (onError) socket.off("error", onError);
    };
  }, [socket, userId, otherUserId, onJoinedDm, onError]);
}

// Hook for joining a group chat room
export interface UseGroupRoomProps {
  socket: Socket | null;
  userId: string;
  groupId: string;
  onJoinedGroup?: (data: any) => void;
  onError?: (error: any) => void;
}

export function useJoinGroupRoom({
  socket,
  userId,
  groupId,
  onJoinedGroup,
  onError,
}: UseGroupRoomProps) {
  useEffect(() => {
    if (!socket || !userId || !groupId) return;

    // Join the group room
    socket.emit("join_group", { userId, groupId });
    
    // Set up event listeners if callbacks are provided
    if (onJoinedGroup) {
      socket.on("joined_group", onJoinedGroup);
    }
    
    if (onError) {
      socket.on("error", onError);
    }

    return () => {
      console.log(`Leaving group room: group:${groupId}`);
      socket.emit("leave_group", { userId, groupId });
      if (onJoinedGroup) socket.off("joined_group", onJoinedGroup);
      if (onError) socket.off("error", onError);
    };
  }, [socket, userId, groupId, onJoinedGroup, onError]);
}

// Hook for listening to new messages
export function useMessageListener(
  socket: Socket | null,
  userId: string | undefined,
  messages: Message[],
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
  scrollRef: React.RefObject<HTMLDivElement>,
  scrollToBottomFn: (ref: React.RefObject<HTMLDivElement>) => void
) {
  useEffect(() => {
    if (!socket) return;

    const handleNewMessage = (message: any) => {
      console.log("Received new message:", message);

      // Check if we already have this message (prevent duplicates)
      if (messages.some((msg) => msg.id === message.id)) {
        console.log("Message already exists, skipping");
        return;
      }

      const formattedMessage = formatMessage(message, userId);

      setMessages((prev) => {
        const newMessages = [...prev, formattedMessage];
        // Ensure we scroll after the state update is complete
        setTimeout(() => scrollToBottomFn(scrollRef), 50);
        return newMessages;
      });
    };

    // Handle new group messages
    const handleNewGroupMessage = (message: any) => {
      console.log("Received new group message:", message);
      handleNewMessage(message); // Reuse the same handler
    };

    // Handle acknowledgment when message is sent
    const handleMessageSent = (data: any) => {
      console.log("Message sent successfully:", data);
      // You can add visual confirmation here if needed
    };

    socket.on("new_message", handleNewMessage);
    socket.on("new_group_message", handleNewGroupMessage);
    socket.on("message_sent", handleMessageSent);

    return () => {
      socket.off("new_message", handleNewMessage);
      socket.off("new_group_message", handleNewGroupMessage);
      socket.off("message_sent", handleMessageSent);
    };
  }, [socket, userId, messages, setMessages, scrollRef, scrollToBottomFn]);
}
