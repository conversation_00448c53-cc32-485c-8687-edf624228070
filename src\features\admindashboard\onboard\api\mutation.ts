import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify"; // if you are using react-toastify
// wherever you handle errors
import { createUser, resetPassword, suspendUser } from "./onboarding.api";
import { errorMessage } from "@/utils/errorHandler";

interface CreateUserInput {
  name: string;
  email: string;
  domain: string;
  role: string;
  packageId: string;
  password?: string | null;
}

export function useAddUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: CreateUserInput) => createUser(content),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("User added successfully");
        // Invalidate both packages and onboarding queries to refresh data
        await queryClient.invalidateQueries({ queryKey: ["packages"] });
        await queryClient.invalidateQueries({ queryKey: ["onboarding"] });
      }
    },
  });
}

export function useResetPasswordMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: { id: string; password: string | null }) =>
      resetPassword(content),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Password reset successfully");
        // Invalidate both packages and onboarding queries to refresh data
        await queryClient.invalidateQueries({ queryKey: ["packages"] });
        await queryClient.invalidateQueries({ queryKey: ["onboarding"] });
      }
    },
  });
}

export function useSuspendUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (content: { id: string; suspendedUntil: string }) =>
      suspendUser(content),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("User suspended successfully");
        // Invalidate both packages and onboarding queries to refresh data
        await queryClient.invalidateQueries({ queryKey: ["packages"] });
        await queryClient.invalidateQueries({ queryKey: ["onboarding"] });
      }
    },
  });
}

