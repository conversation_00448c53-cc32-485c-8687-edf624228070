import { DataTable } from "@/components/globalfiles/data.table";
import { columns } from "./column";
import { useState, useEffect } from "react";
import { usePagination } from "@/components/globalfiles/usePagination";
import { ArrowLeft, ChevronDown } from "lucide-react";
import { AttendanceType } from "./attendace.type";
import { MdOutlineDownloadForOffline } from "react-icons/md";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation } from "react-router-dom";
import { useGetAnnonatorHistory } from "./annonatorhistory/annonatorquery";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useDebounce } from "@/components/globalfiles/debounce";
import { generateAttendancePDF } from "./annonatorhistory/pdfeditordownloadhistory";

const Attendacelog = () => {
  const { onPaginationChange, pagination } = usePagination();
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const navigate = useNavigate();
  const location = useLocation();

  // Get annotator ID from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const annotatorId = queryParams.get('id') || "";
  const annotatorName = queryParams.get('name') || "Annotator";

  // Create debounced versions of the date values to avoid too many API calls
  const debouncedFromDate = useDebounce(fromDate, 500);
  const debouncedToDate = useDebounce(toDate, 500);

  // Fetch attendance history data with date filters
  const { data: apiData, isLoading, error } = useGetAnnonatorHistory(
    annotatorId,
    debouncedFromDate,
    debouncedToDate
  );

  // Validate date range when dates change
  useEffect(() => {
    if (fromDate && toDate) {
      const fromDateObj = new Date(fromDate);
      const toDateObj = new Date(toDate);

      if (fromDateObj > toDateObj) {
        // If from date is after to date, reset to date
        setToDate("");
      }
    }
  }, [fromDate, toDate]);

  // Format the API data to match the AttendanceType interface
  const formatAttendanceData = (): AttendanceType[] => {
    if (!apiData || !apiData.data) return [];

    return apiData.data.map((item: any) => {
      // Format date from ISO string to dd-mm-yyyy
      const dateObj = new Date(item.date);
      const formattedDate = `${dateObj.getDate().toString().padStart(2, '0')}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getFullYear()}`;

      // Format time from ISO string to hh:mm AM/PM
      const timeInObj = new Date(item.timeIn);
      const timeInHours = timeInObj.getHours();
      const timeInMinutes = timeInObj.getMinutes().toString().padStart(2, '0');
      const timeInAmPm = timeInHours >= 12 ? 'PM' : 'AM';
      const timeInHour12 = timeInHours % 12 || 12;
      const formattedTimeIn = `${timeInHour12}:${timeInMinutes} ${timeInAmPm}`;

      // Format timeout (handle null case)
      let formattedTimeOut = "Not clocked out";
      if (item.timeOut) {
        const timeOutObj = new Date(item.timeOut);
        const timeOutHours = timeOutObj.getHours();
        const timeOutMinutes = timeOutObj.getMinutes().toString().padStart(2, '0');
        const timeOutAmPm = timeOutHours >= 12 ? 'PM' : 'AM';
        const timeOutHour12 = timeOutHours % 12 || 12;
        formattedTimeOut = `${timeOutHour12}:${timeOutMinutes} ${timeOutAmPm}`;
      }

      // Format break hours
      const breakHours = Math.floor(item.breakMinutes / 60);
      const breakMinutesRemainder = item.breakMinutes % 60;
      const formattedBreakHours = breakHours > 0
        ? `${breakHours} ${breakHours === 1 ? 'Hr' : 'Hrs'} ${breakMinutesRemainder > 0 ? `${breakMinutesRemainder} Mins` : ''}`
        : `${breakMinutesRemainder} Mins`;

      // Format working hours
      const workingHours = Math.floor(item.workingMinutes / 60);
      const workingMinutesRemainder = item.workingMinutes % 60;
      const formattedWorkingHours = workingHours > 0
        ? `${workingHours} ${workingHours === 1 ? 'Hr' : 'Hrs'} ${workingMinutesRemainder > 0 ? `${workingMinutesRemainder} Mins` : ''}`
        : `${workingMinutesRemainder} Mins`;

      // Format arrival status
      let formattedArrival = "On time";
      if (item.arrivalStatus === "LATE") {
        formattedArrival = "Late";
      } else if (item.arrivalStatus === "HOURS_LATE") {
        formattedArrival = "Hours Late";
      }

      return {
        date: formattedDate,
        timein: formattedTimeIn,
        timeout: formattedTimeOut,
        arrival: formattedArrival,
        breakhours: formattedBreakHours || "0 Mins",
        workinghours: formattedWorkingHours || "0 Mins"
      };
    });
  };

  const attendanceData = formatAttendanceData();

  // 👇 download PDF function
  const downloadPDF = () => {
    // Use the enhanced PDF generator function
    generateAttendancePDF(attendanceData, annotatorName);
  };

  return (
    <div className="p-3 bg-white h-full space-y-3">
      <div className="flex flex-row items-center justify-between space-x-2 text-sm">
        {/* username attendance */}
        <div className="flex flex-row items-center space-x-2">
          <ArrowLeft
            className="text-[#FF577F] cursor-pointer"
            onClick={() => navigate(-1)}
          />
          <p className="text-[20px] font-medium text-[#282828]">
            {annotatorName}'s Attendance
          </p>
        </div>

        {/* Date filter */}
        <div className="flex items-center space-x-4">
          <p className="font-medium font-poppins text-[15px] text-[#1F4062]">Show Entries</p>
          <div className="flex items-center space-x-2 border border-[#FF577F] rounded-md px-1 py-1">
            <span className="text-slate-800">From:</span>
            <div className="relative">
              <input
                type="date"
                className="px-2 py-1 w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
                placeholder="dd/mm/yyyy"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
              />
              <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
            </div>
            <span className="text-slate-800">To:</span>
            <div className="relative">
              <input
                type="date"
                className="px-2 py-1 w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
                placeholder="dd/mm/yyyy"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
              />
              <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* download attendance pdf */}
        <div className="font-poppins">
          <Button variant={"gradient"} onClick={downloadPDF}>
            <p>Download Report</p>
            <MdOutlineDownloadForOffline className="text-[#ffffff]" />
          </Button>
        </div>
      </div>

      {/* Show loading state */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <BrandedGlobalLoader isLoading={true} />
        </div>
      )}

      {/* Show error state */}
      {error && (
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">Error loading attendance data. Please try again.</p>
        </div>
      )}

      {/* Show empty state */}
      {!isLoading && !error && attendanceData.length === 0 && (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">No attendance records found for this annotator.</p>
        </div>
      )}

      {/* Table */}
      {!isLoading && !error && attendanceData.length > 0 && (
        <DataTable
          title="Forms"
          columns={columns}
          data={attendanceData}
          onPaginationChange={onPaginationChange}
          loading={false}
          pagination={pagination}
          pageCount={Math.ceil(attendanceData.length / pagination.pageSize)}
        />
      )}
    </div>
  );
};

export default Attendacelog;
