


type Notification = {
    id: number;
    title: string;
    description: string;
  };


const Notifications: Notification[] = [
    {
      id: 1,
      title: '@Co-worker has accepted requested for joining the projects',
      description: 'Your request for joining the project has been accepted.',
    },
    {
      id: 2,
      title: '@Person has mentioned you in @Project/Task Title',
      description: 'Your request for joining the project has been accepted.',
    },
    {
      id: 3,
      title: '@Client name has assigned you a task in @Project/Task Title',
      description: 'Your request for joining the project has been accepted.',
    },
    {
      id: 4,
      title: '@coardinator has accepted your leave on @Date',
      description: 'Your request for joining the project has been accepted.',
    },
  ];
  
  export default Notifications;