// hooks/useCoordinatorClientsQuery.ts
import { useQuery } from "@tanstack/react-query";
import { getAdminCoordinatorById } from "../../admindetails_api/admindetails_api";

export const useCoordinatorClientsList = (coordinatorId: string) => {
  return useQuery({
    queryKey: ["coordinatorClients", coordinatorId],
    queryFn: () => getAdminCoordinatorById(coordinatorId),
    enabled: !!coordinatorId, // Only run the query if coordinatorId is provided
  });
};
