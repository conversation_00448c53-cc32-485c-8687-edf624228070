import React, { useState } from 'react';
import AnnotatorCard from './AnnotatorCard';

interface Annotator {
  id: string;
  name: string;
  email: string;
  image: string;
  status: string;
  shiftTiming: string;
  projects: string;
  joiningDate: string;
  subscription: string;
  packageId?: string;
  availableFrom?: string | null;
  availableTo?: string | null;
}

interface AnnotatorListProps {
  annotators: Annotator[];
  openModal: (annotator: Annotator) => void;
}

const AnnotatorList: React.FC<AnnotatorListProps> = ({ annotators, openModal }) => {
  const [starredAnnotators, setStarredAnnotators] = useState<{[key: number]: boolean}>({});

  const toggleStar = (index: number) => {
    setStarredAnnotators(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  return (
    <div className="w-full px-4 lg:px-6 xl:px-8 2xl:px-10">
      <div className="grid grid-cols-1 gap-4 py-2 lg:grid-cols-3 lg:gap-5 lg:py-2 xl:grid-cols-4 xl:gap-4 xl:py-2.5 2xl:grid-cols-5 2xl:gap-5 2xl:py-3">
        {annotators.map((annotator, index) => (
          <AnnotatorCard
            key={annotator.id}
            annotator={annotator}
            index={index}
            isStarred={starredAnnotators[index] || false}
            toggleStar={toggleStar}
            openModal={openModal}
          />
        ))}
      </div>
    </div>
  );
};

export default AnnotatorList;
