// src/components/AdminFeatures.tsx
import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import { FeaturedType, PackageType } from "./attendancetype";
import { FeatureResponse, useGetFeatureDataList, useGetPackageList } from "../../../api/api";
import { useEffect, useMemo, useRef } from "react";

const AdminFeatures = () => {
  const navigate = useNavigate();

  const {
    data: featureData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: loadingFeatures,
    error: featureError,
  } = useGetFeatureDataList(10);

  const { data: packagesData, isLoading: loadingPackages, error: packageError } = useGetPackageList();

  const observerRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const mappedData: FeaturedType[] = useMemo(
    () =>
      featureData?.pages
        .flatMap((page) => page.data || [])
        .map((feature: FeatureResponse) => {
          const packageIds =
            feature.packages?.map((pkg) => pkg.packageId) || [];

          const matchedPackages: PackageType[] =
            packagesData?.filter((pkg) => packageIds.includes(pkg.id)) || [];

          const packageNames =
            matchedPackages.map((pkg) => pkg.name).join(", ") || "N/A";

          return {
            id: feature.id,
            featureId: feature.id,
            rule: feature.rule,
            createdAt: feature.createdAt,
            updatedAt: feature.updatedAt,
            packageIds,
            Package: { name: packageNames },
            features: feature.features || feature.rule, // Use rule as fallback
            categories: feature.categories || packageIds, // Use packageIds as fallback
            packageId: packageIds[0] || undefined,
          };
        }) || [],
    [featureData, packagesData]
  );

  if (featureError) {
    return <p className="text-red-500 text-center">Error loading features: {featureError.message}</p>;
  }
  if (packageError) {
    return <p className="text-red-500 text-center">Error loading packages: {packageError.message}</p>;
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h1 className="text-[#282828] text-[24px]">Features</h1>
      </div>

      <div className="max-h-[calc(100vh-150px)] overflow-y-auto">
        <DataTable
          title="Features"
          columns={useAdminColumns()}
          data={mappedData}
          loading={loadingFeatures || loadingPackages}
          disablePagination
        />
      </div>

      {isFetchingNextPage && <p className="text-center py-2">Loading more...</p>}
      <div ref={observerRef} style={{ height: "20px" }} />
      {!hasNextPage && mappedData.length > 0 && (
        <p className="text-center py-2">No more features to load.</p>
      )}
    </div>
  );
};

export default AdminFeatures;