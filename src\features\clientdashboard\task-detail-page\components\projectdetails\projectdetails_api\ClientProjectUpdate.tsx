// components/ClientProjectUpdate.tsx
import { <PERSON><PERSON>elp, Upload } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { UpdateProjectDetailsApi } from "../projectdetails_api/projectdetails_api";
import { useProjectCreateStyles } from "../../project/styles/ProjectCreateStyles";
import {
  AllAnoonatorClient,
  AllCooworkerAnoonatorClient,
} from "../../project/project_api/Project_api";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";
import { toast } from "react-toastify"; // Import toast only

interface Annotator {
  id: string;
  name: string;
  email: string;
}

interface ProjectData {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  annotators?: Annotator[];
}

interface ClientProjectUpdateProps {
  project: ProjectData;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ClientProjectUpdate({
  project,
  onClose,
  onSuccess,
}: ClientProjectUpdateProps) {
  const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const [formData, setFormData] = useState({
    name: project.name || "",
    description: project.description || "",
    priority: project.priority || "",
    annotatorIds: project.annotators?.map(a => a.id) || [],
    attachment: null as File | null,
    startDate: project.startDate ? project.startDate.split("T")[0] : "",
    dueDate: project.dueDate ? project.dueDate.split("T")[0] : "",
  });

  const [errors, setErrors] = useState({
    name: false,
    description: false,
    priority: false,
    annotatorIds: false,
    startDate: false,
    dueDate: false,
  });

  const [apiError, setApiError] = useState("");
  const [annotators, setAnnotators] = useState<Annotator[]>([]);
  const [loadingAnnotators, setLoadingAnnotators] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileError, setFileError] = useState("");

  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        setLoadingAnnotators(true);
        const response = await (user === "CLIENT"
          ? AllAnoonatorClient()
          : AllCooworkerAnoonatorClient());
        
        if (response?.data) {
          const data = Array.isArray(response.data) 
            ? response.data 
            : response.data.data || [];
          setAnnotators(data);
        }
      } catch (error) {
        console.error("Error fetching annotators:", error);
        setApiError("Failed to load annotators. Please try again.");
        toast.error("Failed to load annotators. Please try again.");
      } finally {
        setLoadingAnnotators(false);
      }
    };

    fetchAnnotators();
  }, [user]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setErrors(prev => ({ ...prev, [name]: false }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFileError("");

    if (file) {
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        setFileError(`File size exceeds 10MB limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
        toast.warning(`File size exceeds 10MB limit (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
        return;
      }
    }

    setFormData(prev => ({ ...prev, attachment: file }));
  };

  const handleAnnotatorToggle = (annotatorId: string) => {
    setFormData(prev => {
      const newAnnotatorIds = prev.annotatorIds.includes(annotatorId)
        ? prev.annotatorIds.filter(id => id !== annotatorId)
        : [...prev.annotatorIds, annotatorId];
      setErrors(prev => ({ ...prev, annotatorIds: newAnnotatorIds.length === 0 }));
      return { ...prev, annotatorIds: newAnnotatorIds };
    });
  };

  const validateForm = () => {
    setApiError("");
    const newErrors = {
      name: !formData.name,
      description: !formData.description,
      priority: !formData.priority,
      annotatorIds: formData.annotatorIds.length === 0,
      startDate: !formData.startDate,
      dueDate: !formData.dueDate,
    };

    if (formData.startDate && formData.dueDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.dueDate);
      if (end < start) {
        newErrors.dueDate = true;
        setApiError("Due date cannot be earlier than start date");
        toast.warning("Due date cannot be earlier than start date");
      }
    }

    if (fileError) {
      setApiError(fileError);
      toast.error(fileError);
      return false;
    }

    setErrors(newErrors);
    const isValid = !Object.values(newErrors).some(Boolean);
    if (!isValid) {
      toast.warning("Please fill out all required fields correctly");
    }
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    

    const formDataToSend = new FormData();
    formDataToSend.append("name", formData.name);
    formDataToSend.append("description", formData.description);
    formDataToSend.append("priority", formData.priority);
    formDataToSend.append("annotatorIds", JSON.stringify(formData.annotatorIds));

    if (formData.startDate) {
      const startDate = new Date(formData.startDate);
      startDate.setHours(9, 0, 0, 0); // Set to 9 AM
      formDataToSend.append("startDate", startDate.toISOString());
    }

    if (formData.dueDate) {
      const dueDate = new Date(formData.dueDate);
      dueDate.setHours(18, 0, 0, 0); // Set to 6 PM
      formDataToSend.append("dueDate", dueDate.toISOString());
    }

    if (formData.attachment) {
      formDataToSend.append("attachment", formData.attachment);
    }

    try {
      const response = await UpdateProjectDetailsApi(project.id, formDataToSend);
      
      toast.success("Project updated successfully!");

      console.log("respone line no 201 client projet update", response)
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error updating project:", error);
      const errorMessage = error.response?.data?.message || "Failed to update project. Please try again.";
      setApiError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDateWithFullMonth = (dateStr: string): string => {
    if (!dateStr) return "";
    try {
      const date = new Date(dateStr);
      const day = String(date.getDate()).padStart(2, "0");
      const month = date.toLocaleString("default", { month: "long" });
      const year = date.getFullYear();
      return `${day} ${month} ${year}`;
    } catch (error) {
      return "";
    }
  };

  const styles = useProjectCreateStyles();

  return (
    <div className={styles.modalContainer}>
      <div className={styles.scrollContainer}>
        <div className={styles.formContainer}>
          <h2 className={styles.heading}>Update Project</h2>
          
          {apiError && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md mb-4">
              {apiError}
            </div>
          )}

          <form onSubmit={handleSubmit} className={styles.formSpacing}>
            {/* Title */}
            <div>
              <label className={styles.label}>Title *</label>
              <div className="border-gradient w-full rounded-md">
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`${styles.input} ${errors.name ? "border-red-500" : ""}`}
                  placeholder="Enter project title"
                  disabled={isSubmitting}
                />
              </div>
              {errors.name && <p className="text-red-500 text-sm">Title is required</p>}
            </div>

            {/* Description */}
            <div>
              <label className={styles.label}>Description *</label>
              <div className="border-gradient w-full h-20 rounded-md">
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className={`${styles.textarea} ${errors.description ? "border-red-500" : ""}`}
                  placeholder="Enter description"
                  disabled={isSubmitting}
                />
              </div>
              {errors.description && (
                <p className="text-red-500 text-sm">Description is required</p>
              )}
            </div>

            {/* Priority and Annotators */}
            <div className={styles.flexContainer}>
              {/* Priority */}
              <div className="flex-1">
                <label className={styles.label}>Priority *</label>
                <div className="border-gradient w-full rounded-md">
                  <select
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className={`${styles.select} ${errors.priority ? "ring-red-500" : ""}`}
                    disabled={isSubmitting}
                  >
                    <option value="">Select Priority</option>
                    <option value="HIGH">HIGH</option>
                    <option value="MEDIUM">MEDIUM</option>
                    <option value="LOW">LOW</option>
                  </select>
                </div>
                {errors.priority && (
                  <p className="text-red-500 text-sm">Priority is required</p>
                )}
              </div>

              {/* Annotators */}
              <div className="flex-1">
                <label className={styles.label}>
                  Annotators * {loadingAnnotators && <span className="text-blue-500 text-xs">(Loading...)</span>}
                </label>
                <div className="border-gradient w-full rounded-md">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full text-[13px] font-normal bg-[#F9EFEF] hover:bg-[#f3dddd] text-left h-10 ${
                          errors.annotatorIds ? "border-red-500" : ""
                        }`}
                        disabled={loadingAnnotators || isSubmitting}
                      >
                        {formData.annotatorIds.length > 0
                          ? annotators
                              .filter(a => formData.annotatorIds.includes(a.id))
                              .map(a => a.name)
                              .join(", ")
                          : "Select Annotators"}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-[280px] bg-[#F9EFEF] max-h-60 overflow-y-auto">
                      {annotators.map(annotator => (
                        <DropdownMenuItem
                          key={annotator.id}
                          onSelect={(e) => e.preventDefault()}
                          className="flex items-center space-x-2 py-2"
                        >
                          <Checkbox
                            id={annotator.id}
                            checked={formData.annotatorIds.includes(annotator.id)}
                            onCheckedChange={() => handleAnnotatorToggle(annotator.id)}
                            disabled={isSubmitting}
                          />
                          <Label htmlFor={annotator.id} className="text-[13px] font-normal">
                            {annotator.name}
                          </Label>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {errors.annotatorIds && (
                  <p className="text-red-500 text-sm">At least one annotator is required</p>
                )}
              </div>
            </div>

            {/* Dates */}
            <div className={styles.flexContainer}>
              {/* Start Date */}
              <div className="flex-1">
                <label className={styles.label}>Start Date *</label>
                <div className="border-gradient w-full rounded-md">
                  <input
                    type="date"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className={`${styles.dateInput} ${errors.startDate ? "border-red-500" : ""}`}
                    min={new Date().toISOString().split("T")[0]}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.startDate && (
                  <p className="text-red-500 text-sm">Start date is required</p>
                )}
                {formData.startDate && (
                  <p className="text-gray-500 text-xs mt-1">
                    {formatDateWithFullMonth(formData.startDate)}
                  </p>
                )}
              </div>

              {/* Due Date */}
              <div className="flex-1">
                <label className={styles.label}>Due Date *</label>
                <div className="border-gradient w-full rounded-md">
                  <input
                    type="date"
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleChange}
                    className={`${styles.dateInput} ${errors.dueDate ? "border-red-500" : ""}`}
                    min={formData.startDate || new Date().toISOString().split("T")[0]}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.dueDate && (
                  <p className="text-red-500 text-sm">Due date is required</p>
                )}
                {formData.dueDate && (
                  <p className="text-gray-500 text-xs mt-1">
                    {formatDateWithFullMonth(formData.dueDate)}
                  </p>
                )}
              </div>
            </div>

            {/* Attachment */}
            <div>
              <label className={styles.label}>Attachment (Optional)</label>
              <div className="relative w-full mt-2 rounded-lg border-gradient">
                <div className={styles.uploadContainer}>
                  <Upload className={styles.uploadIcon} />
                  <span className={styles.uploadText}>
                    {formData.attachment
                      ? formData.attachment.name
                      : project.attachment?.length > 0
                        ? project.attachment[0].split("/").pop()
                        : "Choose file (PDF, DOCS)"}
                  </span>
                  <input
                    type="file"
                    onChange={handleFileChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
              {fileError && <p className="text-red-500 text-xs mt-1">{fileError}</p>}
            </div>

            <div className={styles.helpText}>
              <CircleHelp className={styles.helpIcon} />
              <p>Required fields are marked with *</p>
            </div>

            <div className={styles.buttonContainer}>
              <Button
                variant="ghost"
                type="button"
                onClick={onClose}
                className="px-10 py-2.5 text-sm border-gradient"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="gradient"
                type="submit"
                className="px-10 py-2.5 text-sm text-white"
                disabled={isSubmitting || loadingAnnotators}
              >
                {isSubmitting ? "Updating..." : "Update"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}