import { Button } from "@/components/ui/button";
import {
  useClockINMutation,
  useClockOUTMutation,
} from "@/features/annotator/annonator_api/annotator.mutation";
import { AttendanceRecord } from "@/features/annotator/types/attendence.types";
import { RootState } from "@/store";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import React from "react";
import ClockInOutModal from "../modal/clockinout";

interface TimeInOutProps {
  Tittle: string;
  data: AttendanceRecord | undefined;
}

const TimeInOut: React.FC<TimeInOutProps> = ({ Tittle, data }) => {
  const { mutate: clockIn, isPending: isClockInPending } = useClockINMutation();
  const { mutate: clockOut, isPending: isClockOutPending } = useClockOUTMutation();
  const [isModalOpen, setIsModalOpen] = React.useState<boolean>(false);
  const [pendingAction, setPendingAction] = React.useState<"clockIn" | "clockOut" | null>("clockIn");

  const profile = useAppSelector((state: RootState) => state.user.profile);
  const availableFrom = profile?.availableFrom ?? "00:00";
  const availableTo = profile?.availableTo ?? "00:00";

  const handleClockToggle = () => {
    if (!data?.timeIn) {
      setPendingAction("clockIn");
    } else if (data?.timeIn && !data?.timeOut) {
      setPendingAction("clockOut");
    }
    setIsModalOpen(true);
  };

  const handleConfirm = () => {
    if (pendingAction === "clockIn") {
      clockIn(undefined, {
        onSuccess: () => {
          setPendingAction("clockOut");
          console.log("Clocked in successfully");
        },
        onError: () => {
          console.error("Clock in failed");
        },
      });
    } else if (pendingAction === "clockOut") {
      clockOut(undefined, {
        onSuccess: () => {
          setPendingAction("clockIn");
          console.log("Clocked out successfully");
        },
        onError: () => {
          console.error("Clock out failed");
        },
      });
    }
    setIsModalOpen(false);
  };

  const renderTimeInfo = (): string => {
    if (!data?.timeIn) return "00:00:00";

    const timeIn = new Date(data.timeIn);
    const timeInStr = timeIn.toLocaleTimeString();

    if (data?.timeOut) {
      const timeOut = new Date(data.timeOut);
      const timeOutStr = timeOut.toLocaleTimeString();
      return `${timeInStr} - ${timeOutStr}`;
    }

    return timeInStr;
  };

  const getActionText = (): string => {
    if (!data?.timeIn) return "Clock In";
    if (data?.timeIn && !data?.timeOut) return "Clock Out";
    return "Clock In";
  };

  return (
    <>
      <div
        className="flex flex-col justify-between border-gradient rounded-xl
        lg-only:p-4 lg-only:min-h-[140px] lg-only:max-w-[48vw]
        xl:p-5 xl:min-h-[160px] xl:max-w-[30vw]
        2xl:p-6 2xl:min-h-[180px] 2xl:max-w-[28vw]
        bg-white shadow-md mt-4 lg-only:mt-0 xl:mt-0 2xl:mt-0"
      >
        <div>
          <p className="text-[#000] font-semibold lg-only:text-sm xl:text-base 2xl:text-lg">{Tittle}</p>
          <div className="flex flex-col lg-only:flex-col xl:flex-row 2xl:flex-row justify-center items-start lg-only:items-center xl:items-center 2xl:items-center gap-2 lg-only:gap-2 xl:gap-3 2xl:gap-3 mt-2">
            <p className="border border-[#4B4B4B] px-3 lg-only:px-3 xl:px-3 2xl:px-3 py-2 rounded-md lg-only:text-xs xl:text-sm 2xl:text-sm">
              {renderTimeInfo()}
            </p>
            <Button
              variant="ghost"
              disabled={isClockInPending || isClockOutPending}
              onClick={handleClockToggle}
              className={`px-3 lg-only:px-5 xl:px-5 2xl:px-5 py-2 rounded-md lg-only:text-xs xl:text-sm 2xl:text-sm font-semibold transition-all duration-300
                ${
                  data?.timeIn && data?.timeOut
                    ? "border border-[#FF577F] text-[#FF577F] bg-white"
                    : "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] text-white hover:text-white"
                }`}
            >
              {isClockInPending || isClockOutPending ? "Processing..." : getActionText()}
            </Button>
          </div>
        </div>
        <div className="lg-only:text-xs xl:text-sm 2xl:text-sm mt-2 text-right flex flex-col lg-only:flex-row xl:flex-row 2xl:flex-row justify-between text-gray-700">
          <div>
            <p className="font-semibold text-black">
              {data?.date ? new Date(data.date).toLocaleDateString() : "00:00:00"}
            </p>
          </div>
          <div className="space-y-1 text-right mt-2 lg-only:mt-0 xl:mt-0 2xl:mt-0">
            <p>Log In Time: {availableFrom}</p>
            <p>Log Out Time: {availableTo}</p>
          </div>
        </div>
      </div>

      <ClockInOutModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleConfirm}
        actionType={pendingAction}
      />
    </>
  );
};

export default TimeInOut;