// @ts-ignore
import React from "react";
import img from "@/assets/darklogo.png";
import Questioniare from "@/features/clientdashboard/add-on/component/commonpackageplan/questioniare";

const AuthQuestionFormClient = () => {
  // Debug log to check if component is mounting
  React.useEffect(() => {
    console.log("📋 AuthQuestionFormClient component mounted");
    console.log("🔍 Current URL:", window.location.href);
    console.log("🔍 localStorage token:", !!localStorage.getItem("token"));
    console.log("🔍 localStorage user:", !!localStorage.getItem("user"));
  }, []);

  return (
    <div className="flex flex-col ">
      <div className="px-6 border  py-5 ">
        <img
          src={img}
          alt="dATAannonator logo"
          className="w-[155px] h-[32px] mb-1"
        />
      </div>
      <div className="mt-4">
        <Questioniare />
      </div>
    </div>
  );
};

export default AuthQuestionFormClient;
