import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
} from "@dnd-kit/core";
import { CheckCircle2, Circle, Clock } from "lucide-react";
import * as React from "react";
import { <PERSON>, <PERSON>Header, CardTitle } from "@/components/ui/card";
// import { Task } from "@/types/adminkanbantype";
import {
  deleteProjectTask,
  getAllProjectTasks,
  updateProjectTaskStatus,
} from "./api/api";
import { DetailsTask } from "@/types/kanbantasktype";
import { AnnotatorColumncontainerDetailstask } from "./annotator_details_contain";

export type Column = {
  id: string;
  title: string;
  tasks: DetailsTask[];
};

const emptyColumns: Column[] = [
  { id: "todo", title: "To Do", tasks: [] },
  { id: "in-progress", title: "Progress", tasks: [] },
  { id: "done", title: "Completed", tasks: [] },
];

const KANBAN_STORAGE_KEY = "kanbanColumns";

const getColumnIdFromStatus = (status: string): string => {
  switch (status) {
    case "PENDING":
      return "todo";
    case "IN_PROGRESS":
      return "in-progress";
    case "COMPLETED":
      return "done";
    default:
      return "todo";
  }
};

const getStatusFromColumnId = (columnId: string): string => {
  switch (columnId) {
    case "todo":
      return "PENDING";
    case "in-progress":
      return "IN_PROGRESS";
    case "done":
      return "COMPLETED";
    default:
      return "PENDING";
  }
};

interface ProjectKanbanProps {
  projectId: string;
}

export default function AnnotatorProjectDetailsKanban({
  projectId,
}: ProjectKanbanProps) {
  const [columns, setColumns] = React.useState<Column[]>(emptyColumns);
  const [activeTask, setActiveTask] = React.useState<DetailsTask | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        await fetchTasks(true);
      } catch (error) {
        console.error("Error loading data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [projectId]);

  const fetchTasks = async (saveToLocalStorage: boolean) => {
    try {
      const response = await getAllProjectTasks(projectId);
      console.log("API Response:", response); // Debug
      if (response?.status === 1 && response.data) {
        const apiTasks = response.data.map((task: any) => ({
          id: task.id,
          title: task.name || "Untitled Task",
          description: task.description || "",
          level:
            task.priority === "HIGH"
              ? "H"
              : task.priority === "MEDIUM"
              ? "M"
              : task.priority === "LOW"
              ? "L"
              : "L",
          dueDate: task.dueDate ? new Date(task.dueDate).toISOString() : null,
          status: task.status || "PENDING",
          color: task.color || "#60A5FA",
          assignedToId: task.assignedToId,
          createdById: task.createdById,
          startDate: task.startDate,
          createdAt: task.createdAt,
          updatedAt: task.updatedAt,
        }));

        const newColumns = emptyColumns.map((column) => ({
          ...column,
          tasks: apiTasks.filter(
            (task: DetailsTask) =>
              getColumnIdFromStatus(task.status || "PENDING") === column.id
          ),
        }));

        setColumns(newColumns);
        console.log("Updated Columns:", newColumns); // Debug

        if (saveToLocalStorage) {
          localStorage.setItem(KANBAN_STORAGE_KEY, JSON.stringify(newColumns));
        }
      }
    } catch (error) {
      console.error("Error fetching tasks:", error);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = columns
      .flatMap((col) => col.tasks)
      .find((t) => t.id === active.id);
    if (task) setActiveTask(task);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const activeTask = active.data.current?.task as DetailsTask;
    const activeColumn = active.data.current?.column as Column;
    const overColumn = columns.find((col) => col.id === over.id);

    if (!activeTask || !activeColumn || !overColumn) return;

    if (activeColumn.id !== overColumn.id) {
      const newStatus = getStatusFromColumnId(overColumn.id);

      const updatedColumns = columns.map((col) => {
        if (col.id === activeColumn.id) {
          return {
            ...col,
            tasks: col.tasks.filter((t) => t.id !== activeTask.id),
          };
        } else if (col.id === overColumn.id) {
          return {
            ...col,
            tasks: [...col.tasks, { ...activeTask, status: newStatus }],
          };
        }
        return col;
      });

      setColumns(updatedColumns);

      try {
        await updateProjectTaskStatus(activeTask.id, {
          status: newStatus,
          projectId: projectId,
        });

        localStorage.setItem(
          KANBAN_STORAGE_KEY,
          JSON.stringify(updatedColumns)
        );
      } catch (error) {
        console.error("Failed to update task status:", error);
        setColumns(columns);
      }
    }

    setActiveTask(null);
  };

  const handleDeleteTask = async (taskToDelete: DetailsTask) => {
    try {
      const newColumns = columns.map((col) => ({
        ...col,
        tasks: col.tasks.filter((task) => task.id !== taskToDelete.id),
      }));
      setColumns(newColumns);

      await deleteProjectTask(taskToDelete.id);
    } catch (error) {
      console.error("Failed to delete task:", error);
      setColumns(columns);
    }
  };

  const handleAddTask = async (newTask: DetailsTask, columnId: string) => {
    try {
      console.log("New Task:", newTask); // Debug
      const formattedTask: DetailsTask = {
        ...newTask,
        level: newTask.priority
          ? newTask.priority === "HIGH"
            ? "H"
            : newTask.priority === "MEDIUM"
            ? "M"
            : "L"
          : newTask.level || "L",
        dueDate: newTask.dueDate || null,
        status: newTask.status || getStatusFromColumnId(columnId),
      };

      const updatedColumns = columns.map((column) =>
        column.id === columnId
          ? { ...column, tasks: [...column.tasks, formattedTask] }
          : column
      );

      setColumns(updatedColumns);

      localStorage.setItem(KANBAN_STORAGE_KEY, JSON.stringify(updatedColumns));

      await fetchTasks(true);
    } catch (error) {
      console.error("Failed to add task:", error);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-full w-full flex flex-col">
      <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full flex-grow">
          {columns.map((column) => (
            <AnnotatorColumncontainerDetailstask
              key={column.id}
              column={column}
              tasks={column.tasks}
              onDeleteTask={handleDeleteTask}
              onAddTask={handleAddTask}
              fetchTasksProp={fetchTasks}
            />
          ))}
        </div>
        <DragOverlay>
          {activeTask ? (
            <Card className="w-[250px]">
              <CardHeader className="p-3">
                <div className="flex items-center space-x-2">
                  {getColumnIdFromStatus(activeTask.status || "PENDING") ===
                  "todo" ? (
                    <Circle className="h-4 w-4 text-blue-500" />
                  ) : getColumnIdFromStatus(activeTask.status || "PENDING") ===
                    "in-progress" ? (
                    <Clock className="h-4 w-4 text-yellow-500" />
                  ) : (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  )}
                  <CardTitle className="text-sm font-medium">
                    {activeTask.title}
                  </CardTitle>
                </div>
              </CardHeader>
            </Card>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
