import React from 'react';

interface ActionButtonsProps {
  primaryAction: {
    label: string;
    onClick: () => void;
    className: string;
  };
  secondaryAction: {
    label: string;
    onClick: () => void;
    className: string;
  };
  styles: {
    actionContainer: string;
  };
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  primaryAction,
  secondaryAction,
  styles
}) => {
  return (
    <div className={styles.actionContainer}>
      <button
        onClick={primaryAction.onClick}
        className={primaryAction.className}
      >
        {primaryAction.label}
      </button>
      <button
        onClick={secondaryAction.onClick}
        className={secondaryAction.className}
      >
        {secondaryAction.label}
      </button>
    </div>
  );
};

export default ActionButtons;
