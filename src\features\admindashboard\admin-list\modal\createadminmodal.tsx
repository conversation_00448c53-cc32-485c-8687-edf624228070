import { useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Eye, EyeOff } from "lucide-react";
import { DialogClose } from "@radix-ui/react-dialog";
import { AdminCreate } from "@/features/admindashboard/admin-list/adminlist_api/adminList_api";
import { toast } from "react-toastify";

interface OnboardFormProps {
  defaultValues?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    domain?: string;
    role?: string;
    package?: string;
    password?: string;
  };
  refetchList: () => void;
}

const CreateAdminModal = ({ defaultValues, refetchList }: OnboardFormProps) => {
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState(defaultValues?.password || "");
  const [passwordOption, setPasswordOption] = useState<"auto" | "manual">(
    "auto"
  );
  const closeRef = useRef<HTMLButtonElement>(null);
  const [firstName, setFirstName] = useState(defaultValues?.firstName || "");
  const [lastName, setLastName] = useState(defaultValues?.lastName || "");
  const [emailPrefix, setEmailPrefix] = useState(
    defaultValues?.email?.split("@")[0] || ""
  );
  const [domain, setDomain] = useState(
    defaultValues?.email?.split("@")[1] || "macgence.com"
  );
  const [isCustomDomain, setIsCustomDomain] = useState(false);
  const [loading, setLoading] = useState(false);

  const setValue = (field: string, value: string) => {
    switch (field) {
      case "domain":
        setDomain(value);
        break;
      default:
        console.error(`Unknown field: ${field}`);
    }
  };

  const generatePassword = () => {
    const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lower = "abcdefghijklmnopqrstuvwxyz";
    const number = "0123456789";
    const special = "!@#$%^&*()_+-=[]{}|;:',.<>?";
    const allChars = upper + lower + number + special;

    let pwd = [
      upper[Math.floor(Math.random() * upper.length)],
      lower[Math.floor(Math.random() * lower.length)],
      number[Math.floor(Math.random() * number.length)],
      special[Math.floor(Math.random() * special.length)],
    ];

    for (let i = pwd.length; i < 12; i++) {
      pwd.push(allChars[Math.floor(Math.random() * allChars.length)]);
    }

    pwd = pwd.sort(() => Math.random() - 0.5);
    setPassword(pwd.join(""));
  };

  const fullEmail = `${emailPrefix}@${domain}`;

  const [errors, setErrors] = useState({
    firstName: "",
    emailPrefix: "",
    domain: "",
    password: "",
  });

  const handleNameInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow letters, numbers, spaces, and dots
    const filteredValue = value.replace(/[^a-zA-Z0-9\s.]/g, '');
    
    // Show error if filtered value is different from original input
    if (value !== filteredValue) {
      setErrors(prev => ({
        ...prev,
        emailPrefix: "Only letters, numbers, spaces and periods are allowed"
      }));
    } else {
      setErrors(prev => ({ ...prev, emailPrefix: "" }));
    }
    
    setEmailPrefix(filteredValue);
  };

  const handleCreateAdmin = async () => {
    const newErrors: typeof errors = {
      firstName: "",
      emailPrefix: "",
      domain: "",
      password: "",
    };

    // Validation
    if (!firstName.trim()) newErrors.firstName = "First name is required";
    if (!emailPrefix.trim()) newErrors.emailPrefix = "Email prefix is required";
    else if (/[^a-zA-Z0-9\s.]/.test(emailPrefix)) {
      newErrors.emailPrefix = "Invalid characters in name";
    }
    if (!domain.trim()) newErrors.domain = "Domain is required";
    if (passwordOption === "manual") {
      if (!password.trim()) newErrors.password = "Password is required";
      else if (password.length < 8)
        newErrors.password = "Password must be at least 8 characters";
    }

    // If any error, set and return
    const hasError = Object.values(newErrors).some(Boolean);
    if (hasError) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({
      firstName: "",
      emailPrefix: "",
      domain: "",
      password: "",
    });

    setLoading(true);
    try {
      await AdminCreate({
        name: firstName,
        lastname: lastName,
        email: fullEmail,
        password: passwordOption === "manual" ? password : undefined,
        sendPasswordLink: passwordOption === "auto",
        domain,
      });

      toast.success("Admin created successfully!", {
        position: "top-right",
        className: "bg-green-500 text-white",
      });

      refetchList();
      closeRef.current?.click();
    } catch (err: any) {
      toast.error(`Error: ${err.message || "Something went wrong"}`, {
        position: "top-right",
        className: "bg-red-500 text-white",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full lg-only:max-w-3xl xl-only:max-w-4xl 2xl-only:max-w-5xl lg-only:p-3 xl-only:p-5 2xl-only:p-6">
      <h2 className="lg-only:text-base xl-only:text-xl 2xl-only:text-2xl font-semibold lg-only:mb-2 xl-only:mb-4 2xl-only:mb-5">
        User Information
      </h2>
      <div className="grid grid-cols-1 lg-only:grid-cols-2 xl-only:grid-cols-2 2xl-only:grid-cols-2 lg-only:gap-2 xl-only:gap-4 2xl-only:gap-5">
        <div className="lg-only:py-0.5 xl-only:py-1 2xl-only:py-1.5">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2 block">
            First Name *
          </Label>
          <div className="border-gradient rounded-lg">
            <Input
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Enter first name"
              className="bg-[#F9EFEF] outline-none w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
            />
          </div>
          {errors.firstName && (
            <p className="text-red-500 text-xs mt-1 ml-2">{errors.firstName}</p>
          )}
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1 2xl-only:py-1.5">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2 block">
            Last Name
          </Label>
          <div className="border-gradient rounded-lg">
            <Input
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Enter last name"
              className="bg-[#F9EFEF] w-full outline-none text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
            />
          </div>
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1 2xl-only:py-1.5">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2 block">
            Primary Email *
          </Label>
          <div className="flex items-center gap-1 border-gradient rounded-lg">
            <Input
              value={emailPrefix}
              onChange={handleNameInput}
              placeholder="Enter email prefix"
              className="bg-[#F9EFEF] outline-none text-[#5E5E5E] w-full lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
            />
          </div>
          {errors.emailPrefix && (
            <p className="text-red-500 text-xs mt-1 ml-2">
              {errors.emailPrefix}
            </p>
          )}
        </div>

        <div className="lg-only:py-0.5 xl-only:py-1 2xl-only:py-1.5">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2 block">
            Domain
          </Label>
          {isCustomDomain ? (
            <div className="border-gradient rounded-lg">
              <div>
                <Input
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  placeholder="Enter custom domain"
                  className="bg-[#F9EFEF] w-full outline-none text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
                />
                {errors.domain && (
                  <p className="text-red-500 text-xs mt-1">{errors.domain}</p>
                )}
              </div>
            </div>
          ) : (
            <Select
              defaultValue={domain}
              onValueChange={(value) => {
                if (value === "custom") {
                  setIsCustomDomain(true);
                  setDomain("");
                } else {
                  setIsCustomDomain(false);
                  setDomain(value);
                }
              }}
            >
              <SelectTrigger className="bg-[#F9EFEF] border-gradient w-full text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2">
                <SelectValue placeholder="Select domain" />
              </SelectTrigger>
              <SelectContent className="border-transparent lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                <SelectItem value="macgence.com">macgence.com</SelectItem>
                <SelectItem value="macgence.in">macgence.in</SelectItem>
                <SelectItem value="custom">Custom Domain</SelectItem>
              </SelectContent>
            </Select>
          )}
          <div className="flex flex-col">
            <span className="lg-only:mt-0.5 xl-only:mt-1 2xl-only:mt-1.5 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-gray-500">
              Full email: <strong>{fullEmail}</strong>
            </span>
            <span
              className="lg-only:text-[10px] xl-only:text-[12px] 2xl-only:text-base text-blue-500 cursor-pointer"
              onClick={() => setValue("domain", "")}
            >
              Change domain
            </span>
          </div>
        </div>
      </div>

      <div className="lg-only:mt-2 xl-only:mt-4 2xl-only:mt-6">
        <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base lg-only:mb-1 xl-only:mb-1.5 2xl-only:mb-2 block">
          Password
        </Label>
        <RadioGroup
          value={passwordOption}
          onValueChange={(value) =>
            setPasswordOption(value as "auto" | "manual")
          }
          className="lg-only:mt-1 xl-only:mt-2 2xl-only:mt-3 flex flex-col lg-only:gap-1 xl-only:gap-2 2xl-only:gap-3"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="auto"
              id="auto"
              className="border-[#FF577F] lg-only:h-3 lg-only:w-3 xl-only:h-4 xl-only:w-4 2xl-only:h-5 2xl-only:w-5"
            />
            <Label
              htmlFor="auto"
              className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
            >
              Automatic send password link to mail
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value="manual"
              id="manual"
              className="border-[#FF577F] lg-only:h-3 lg-only:w-3 xl-only:h-4 xl-only:w-4 2xl-only:h-5 2xl-only:w-5"
            />
            <Label
              htmlFor="manual"
              className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
            >
              Create password
            </Label>
          </div>
        </RadioGroup>

        {passwordOption === "manual" && (
          <div className="flex lg-only:mt-1 xl-only:mt-2 2xl-only:mt-3 items-center lg-only:gap-1 xl-only:gap-2 2xl-only:gap-3">
            <div className="flex flex-col lg-only:w-[30%] xl-only:w-[35%] 2xl-only:w-[40%] relative">
              <div className="border-gradient rounded-lg">
                <Input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password"
                  className="w-full bg-[#F9EFEF] text-[#5E5E5E] pr-10 lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-5 xl-only:py-5 2xl-only:py-2"
                  autoComplete="new-password"
                />

                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-2 top-1/2 -translate-y-1/2"
                >
                  {showPassword ? (
                    <Eye className="lg-only:w-3 lg-only:h-3 xl-only:w-4 xl-only:h-4 2xl-only:w-5 2xl-only:h-5 text-gray-500" />
                  ) : (
                    <EyeOff className="lg-only:w-3 lg-only:h-3 xl-only:w-4 xl-only:h-4 2xl-only:w-5 2xl-only:h-5 text-gray-500" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            <Button
              variant="gradient"
              onClick={generatePassword}
              className="lg-only:text-xs lg-only:py-1 lg-only:px-2 xl-only:text-sm xl-only:py-1.5 xl-only:px-3 2xl-only:text-base 2xl-only:py-2 2xl-only:px-4 "
            >
              Generate Password
            </Button>
          </div>
        )}
      </div>

      <div className="lg-only:mt-2 xl-only:mt-4 2xl-only:mt-6 flex justify-end lg-only:gap-1 xl-only:gap-1.5 2xl-only:gap-2">
        <Button
          variant="gradient"
          className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
          onClick={handleCreateAdmin}
          disabled={loading}
        >
          {loading ? "Saving..." : "Save"}
        </Button>
        <DialogClose>
          <Button
            ref={closeRef}
            onClick={() => closeRef.current?.click()}
            variant="ghost"
            className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 border-[#FF577F] border text-[#FF577F] hover:text-[#fd4a74] lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
          >
            Cancel
          </Button>
        </DialogClose>
      </div>
    </div>
  );
};

export default CreateAdminModal;