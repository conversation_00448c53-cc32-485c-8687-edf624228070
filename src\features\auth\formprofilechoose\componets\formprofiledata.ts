export const countries = [
  { value: 'US', label: 'United States', phoneCode: '+1' },
  { value: 'IN', label: 'India', phoneCode: '+91' },
  { value: 'CN', label: 'China', phoneCode: '+86' },
  { value: 'JP', label: 'Japan', phoneCode: '+81' },
  { value: 'DE', label: 'Germany', phoneCode: '+49' },
  { value: 'GB', label: 'United Kingdom', phoneCode: '+44' },
  { value: 'FR', label: 'France', phoneCode: '+33' },
  { value: 'BR', label: 'Brazil', phoneCode: '+55' },
  { value: 'IT', label: 'Italy', phoneCode: '+39' },
  { value: 'CA', label: 'Canada', phoneCode: '+1' },
  { value: 'KR', label: 'South Korea', phoneCode: '+82' },
  { value: 'RU', label: 'Russia', phoneCode: '+7' },
  { value: 'AU', label: 'Australia', phoneCode: '+61' },
  { value: 'ES', label: 'Spain', phoneCode: '+34' },
  { value: 'MX', label: 'Mexico', phoneCode: '+52' },
  { value: 'ID', label: 'Indonesia', phoneCode: '+62' },
  { value: 'NL', label: 'Netherlands', phoneCode: '+31' },
  { value: 'SA', label: 'Saudi Arabia', phoneCode: '+966' },
  { value: 'TR', label: 'Turkey', phoneCode: '+90' },
  { value: 'CH', label: 'Switzerland', phoneCode: '+41' },
  { value: 'AR', label: 'Argentina', phoneCode: '+54' },
  { value: 'SE', label: 'Sweden', phoneCode: '+46' },
  { value: 'PL', label: 'Poland', phoneCode: '+48' },
  { value: 'BE', label: 'Belgium', phoneCode: '+32' },
  { value: 'NG', label: 'Nigeria', phoneCode: '+234' },
  { value: 'TH', label: 'Thailand', phoneCode: '+66' },
  { value: 'AE', label: 'United Arab Emirates', phoneCode: '+971' },
  { value: 'VN', label: 'Vietnam', phoneCode: '+84' },
  { value: 'MY', label: 'Malaysia', phoneCode: '+60' },
  { value: 'SG', label: 'Singapore', phoneCode: '+65' },
  { value: 'PH', label: 'Philippines', phoneCode: '+63' },
  { value: 'DK', label: 'Denmark', phoneCode: '+45' },
  { value: 'IR', label: 'Iran', phoneCode: '+98' },
  { value: 'NO', label: 'Norway', phoneCode: '+47' },
  { value: 'ZA', label: 'South Africa', phoneCode: '+27' },
  { value: 'EG', label: 'Egypt', phoneCode: '+20' },
  { value: 'FI', label: 'Finland', phoneCode: '+358' },
  { value: 'CO', label: 'Colombia', phoneCode: '+57' },
  { value: 'PK', label: 'Pakistan', phoneCode: '+92' },
  { value: 'GR', label: 'Greece', phoneCode: '+30' },
  { value: 'IE', label: 'Ireland', phoneCode: '+353' },
  { value: 'PT', label: 'Portugal', phoneCode: '+351' },
  { value: 'CZ', label: 'Czech Republic', phoneCode: '+420' },
  { value: 'IL', label: 'Israel', phoneCode: '+972' },
  { value: 'NZ', label: 'New Zealand', phoneCode: '+64' },
  { value: 'RO', label: 'Romania', phoneCode: '+40' },
  { value: 'HU', label: 'Hungary', phoneCode: '+36' },
  { value: 'HK', label: 'Hong Kong', phoneCode: '+852' },
  { value: 'CL', label: 'Chile', phoneCode: '+56' },
  { value: 'KE', label: 'Kenya', phoneCode: '+254' }
];

export const timeZones = [
  { value: 'UTC-12:00', label: 'UTC-12:00 (International Date Line West)' },
  { value: 'UTC+05:30', label: 'UTC+05:30 (Indian Standard Time - IST)' },
  { value: 'UTC-11:00', label: 'UTC-11:00 (Samoa Time Zone)' },
  { value: 'UTC-10:00', label: 'UTC-10:00 (Hawaii-Aleutian Time)' },
  { value: 'UTC-09:00', label: 'UTC-09:00 (Alaska Time)' },
  { value: 'UTC-08:00', label: 'UTC-08:00 (Pacific Time)' },
  { value: 'UTC-07:00', label: 'UTC-07:00 (Mountain Time)' },
  { value: 'UTC-06:00', label: 'UTC-06:00 (Central Time)' },
  { value: 'UTC-05:00', label: 'UTC-05:00 (Eastern Time)' },
  { value: 'UTC-04:00', label: 'UTC-04:00 (Atlantic Time)' },
  { value: 'UTC-03:00', label: 'UTC-03:00 (Brasília Time)' },
  { value: 'UTC-02:00', label: 'UTC-02:00 (Mid-Atlantic Time)' },
  { value: 'UTC-01:00', label: 'UTC-01:00 (Azores Time)' },
  { value: 'UTC±00:00', label: 'UTC±00:00 (Greenwich Mean Time)' },
  { value: 'UTC+01:00', label: 'UTC+01:00 (Central European Time)' },
  { value: 'UTC+02:00', label: 'UTC+02:00 (Eastern European Time)' },
  { value: 'UTC+03:00', label: 'UTC+03:00 (Moscow Time)' },
  { value: 'UTC+04:00', label: 'UTC+04:00 (Gulf Standard Time)' },
  { value: 'UTC+05:00', label: 'UTC+05:00 (Pakistan Standard Time)' },
  { value: 'UTC+06:00', label: 'UTC+06:00 (Bangladesh Time)' },
  { value: 'UTC+07:00', label: 'UTC+07:00 (Indochina Time)' },
  { value: 'UTC+08:00', label: 'UTC+08:00 (China Standard Time)' },
  { value: 'UTC+09:00', label: 'UTC+09:00 (Japan Standard Time)' },
  { value: 'UTC+10:00', label: 'UTC+10:00 (Australian Eastern Time)' },
  { value: 'UTC+11:00', label: 'UTC+11:00 (Solomon Islands Time)' },
  { value: 'UTC+12:00', label: 'UTC+12:00 (New Zealand Standard Time)' },
  { value: 'UTC+13:00', label: 'UTC+13:00 (Tonga Time)' },
  { value: 'UTC+14:00', label: 'UTC+14:00 (Line Islands Time)' }
];

export const formDefaultValues = {
  companyName: '',
  website: '',
  country: '',
  state: '',
  phoneNumber: '',
  timeZone: '',
  address: '',
  postalCode: '',
};

// Add TypeScript interface for better type safety
export interface Country {
  value: string;
  label: string;
  phoneCode: string;
}

export interface TimeZone {
  value: string;
  label: string;
}

export interface FormDefaultValues {
  companyName: string;
  website: string;
  country: string;
  state: string;
  phoneNumber: string;
  timeZone: string;
  address: string;
  postalCode: string;
}