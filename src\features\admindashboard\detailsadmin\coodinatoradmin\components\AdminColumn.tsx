"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
// import { AttendanceType } from "./attendancetype";
import { useNavigate } from "react-router-dom"; // ✅ React Router

// useAdminColumns hook definition
export const useAdminColumns = (): ColumnDef<any>[] => {
  const navigate = useNavigate(); // ✅ react-router-dom navigation

  return [
    {
      accessorKey: "name",
      header: () => (
        <div
          className="w-[200px] font-medium cursor-pointer"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Clients
        </div>
      ),
      cell: ({ row }) => {
        const client = row.original;
        return (
          <div className="flex items-center gap-2">
            <div>
              <p className="font-medium">{client.name}</p>
              <p className="text-xs text-gray-500">{client.email}</p>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "package",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Packages
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        // Get the latest subscription package name
        const latestSubscription = client.Subscription && client.Subscription.length > 0
          ? client.Subscription[client.Subscription.length - 1]
          : null;

        return (
          <div className="pl-4">
            {latestSubscription ? latestSubscription.package.name : "No Package"}
          </div>
        );
      },
    },
    {
      accessorKey: "startDate",
      header: () => (
        <Button
          variant="ghost"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Started on
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        // Get the latest subscription start date
        const latestSubscription = client.Subscription && client.Subscription.length > 0
          ? client.Subscription[client.Subscription.length - 1]
          : null;

        if (!latestSubscription) return <div className="pl-4">N/A</div>;

        try {
          const date = new Date(latestSubscription.startDate);
          const formatted = date.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "2-digit",
          });
          return <div className="pl-4">{formatted}</div>;
        } catch (e) {
          return <div className="pl-4">Invalid date</div>;
        }
      },
    },
    {
      accessorKey: "endDate",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          End Date
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        // Get the latest subscription end date
        const latestSubscription = client.Subscription && client.Subscription.length > 0
          ? client.Subscription[client.Subscription.length - 1]
          : null;

        if (!latestSubscription) return <div className="pl-4">N/A</div>;

        try {
          const date = new Date(latestSubscription.endDate);
          const formatted = date.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "2-digit",
          });
          return <div className="pl-4">{formatted}</div>;
        } catch (e) {
          return <div className="pl-4">Invalid date</div>;
        }
      },
    },
    {
      accessorKey: "projectsOwned",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Projects
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        return <div className="pl-4">{client._count?.projectsOwned || 0}</div>;
      },
    },
    {
      accessorKey: "coWorkers",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Co-Workers
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        return <div className="pl-4">{client._count?.coWorkers || 0}</div>;
      },
    },
    {
      accessorKey: "assignmentsAsClient",
      header: () => (
        <Button
          variant="ghost"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Annotators
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        return <div className="pl-4">{client._count?.assignmentsAsClient || 0}</div>;
      },
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        const client = row.original;
        return (
          <div className="pl-4">
            <Button
              variant="gradient"
              className="px-7 rounded-xl"
              onClick={() => navigate(`/admin/adminclients?id=${client.id}&name=${encodeURIComponent(client.name)}`)}
            >
              View All Project
            </Button>
          </div>
        );
      },
    },
  ];
};
