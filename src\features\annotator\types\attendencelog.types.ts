type AttendanceRecord = {
  date: string; // ISO date string
  timeIn: string; // ISO datetime string
  timeOut: string | null;
  arrival: string;
  breakHours: string;
  workingHours: string;
  status: string;
  rawData: {
    breakMinutes: number;
    workingMinutes: number;
  };
};

type Pagination = {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type AttendanceResponse = {
  data: {
    data: AttendanceRecord[];
    pagination: Pagination;
  };
};
