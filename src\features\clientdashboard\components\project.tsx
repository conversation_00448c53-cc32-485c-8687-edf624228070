import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  getDashboardDataProjects,
  getAllClientCooworkerProjects,
} from "./dashboard_api/dashboard_api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";

type ProjectStatus = "View Project";

// Type definition based on the API response
type Project = {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  startDate: string;
  dueDate: string;
  tasks: any[];
  createdBy: {
    id: string;
    name: string;
  };
};

// Type for our UI display
type ProjectDisplay = {
  id: string;
  name: string;
  tasksCount: number;
  status: ProjectStatus;
  avatarUrl: string;
};

const statusColors: Record<ProjectStatus, string> = {
  "View Project":
    "bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white",
};

const ProjectList: React.FC = () => {
  const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const navigate = useNavigate();
  const [projects, setProjects] = useState<ProjectDisplay[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);

        console.log("Fetching projects from API...");
        const response = await (user === "CLIENT"
          ? getDashboardDataProjects()
          : getAllClientCooworkerProjects());
        console.log("API Response:", response);

        if (response && response.data && Array.isArray(response.data)) {
          // Transform API data to our display format
          const projectsData = response.data.map((project: Project) => ({
            id: project.id,
            name: project.name,
            tasksCount: project.tasks ? project.tasks.length : 0,
            status: "View Project" as ProjectStatus,
            avatarUrl: getAvatarUrl(project.name),
          }));

          setProjects(projectsData);
        } else {
          console.error("Invalid API response format:", response);
          setError("Failed to load projects data");
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
        setError("Failed to load projects");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg p-4 lg:w-full xl:w-full 2xl:w-full lg:h-[13rem] xl:h-[13rem] 2xl:h-[14rem] flex flex-col">
      <div className="flex justify-between items-center mb-3">
        <h2 className="lg:text-base xl:text-lg 2xl:text-xl font-semibold font-poppins">
          Projects
        </h2>
        <button
          onClick={() => navigate("/dashboard/task-details/projects")}
          className="lg:text-xs xl:text-sm 2xl:text-base font-semibold px-3 py-1 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all"
        >
          View All
        </button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-full">
          <p className="lg:text-sm xl:text-base 2xl:text-lg text-gray-500">
            Loading projects...
          </p>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-full">
          <p className="lg:text-sm xl:text-base 2xl:text-lg text-red-500">
            {error}
          </p>
        </div>
      ) : projects.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <p className="lg:text-sm xl:text-base 2xl:text-lg text-gray-500">
            No projects available
          </p>
        </div>
      ) : (
        <ul className="CustomScroll overflow-y-auto pr-1 flex-grow">
          {projects.map((project) => (
            <li
              key={project.id}
              className="flex items-center gap-3 mb-3 last:mb-0"
            >
              <img
                src={project.avatarUrl}
                alt="Project avatar"
                className="lg:w-6 lg:h-6 xl:w-8 xl:h-8 2xl:w-10 2xl:h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="font-medium lg:text-xs xl:text-sm 2xl:text-base text-[#282828]">
                  {project.name.split(" ").slice(0, 1).join(" ")}
                </p>
                <p className="lg:text-[10px] xl:text-xs 2xl:text-sm text-[#727272]">
                  {project.tasksCount}{" "}
                  {project.tasksCount === 1 ? "task" : "tasks"} assigned
                </p>
              </div>
              <button
                onClick={() =>
                  navigate(`/dashboard/project-details?id=${project.id}`)
                }
                className={`text-white lg:text-xs xl:text-xs 2xl:text-base px-2 py-1 rounded-md ${
                  statusColors[project.status]
                }`}
              >
                {project.status}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ProjectList;
