// banktransferapi/bank_transfer_api.tsx
import { customAxios } from "@/utils/axio-interceptor";

interface BankTransferResponse {
  status: number;
  message: string;
  data: {
    payments: Array<{
      id: string;
      paymentId: string;
      userId: string;
      subscriptionId: string;
      packageId: string;
      amount: number;
      currency: string;
      transactionId: string;
      bankHolderName: string;
      accountNumber: string;
      ifscCode: string;
      bankName: string;
      screenshotUrl: string;
      status: string;
      adminNotes: string | null;
      verifiedAt: string | null;
      verifiedById: string | null;
      createdAt: string;
      updatedAt: string;
      user: {
        id: string;
        name: string;
        email: string;
      };
      package: {
        id: string;
        name: string;
        price: number;
      };
      subscription: {
        id: string;
        userId: string;
        packageId: string;
        product_id: string | null;
        startDate: string;
        endDate: string;
        status: string;
        createdAt: string;
        updatedAt: string;
      };
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

interface VerifyBankTransferResponse {
  status: number;
  message: string;
}

export const AdminGetBanktranferList = async (): Promise<BankTransferResponse> => {
  try {
    const response = await customAxios.get("/v1/bank-transfer/admin/pending");
    return response.data;
  } catch (error) {
    console.error("Error fetching bank transfer list:", error);
    throw error;
  }
};

export const VerifyPostBanktranfer = async (
  bankTransferPaymentId: string,
  paymentId: string,
  status: "VERIFIED" | "REJECTED",
  adminNotes: string
): Promise<VerifyBankTransferResponse> => {
  try {
    const response = await customAxios.post(`/v1/bank-transfer/admin/verify/${bankTransferPaymentId}`, {
      status,
      paymentId,
      adminNotes,
    });
    return response.data;
  } catch (error) {
    console.error("Error verifying bank transfer payment:", error);
    throw error;
  }
};






// Add this interface at the top with other interfaces
interface VerifiedBankTransferHistoryResponse {
  status: number;
  message: string;
  data: {
    verifiedUsers: Array<{
      user: {
        id: string;
        name: string;
        lastname: string | null;
        email: string;
        role: string;
        createdAt: string;
      };
      totalPayments: number;
      totalAmount: number;
      packages: Array<{
        id: string;
        name: string;
        price: number;
        billingType: string;
      }>;
      subscriptions: Array<{
        id: string;
        status: string;
        startDate: string;
        endDate: string;
      }>;
      firstPaymentDate: string;
      lastPaymentDate: string;
      verifiedBy: {
        id: string;
        name: string;
        email: string;
      };
      latestPaymentDetails: {
        transactionId: string;
        bankName: string;
        amount: number;
        currency: string;
        verifiedAt: string;
      };
    }>;
    rawPayments: Array<{
      id: string;
      paymentId: string;
      userId: string;
      subscriptionId: string;
      packageId: string;
      amount: number;
      currency: string;
      transactionId: string;
      bankHolderName: string;
      accountNumber: string;
      ifscCode: string;
      bankName: string;
      screenshotUrl: string;
      status: string;
      adminNotes: string | null;
      verifiedAt: string | null;
      verifiedById: string | null;
      createdAt: string;
      updatedAt: string;
      user: {
        id: string;
        name: string;
        lastname: string | null;
        email: string;
        role: string;
        createdAt: string;
      };
      package: {
        id: string;
        name: string;
        price: number;
        billingType: string;
      };
      subscription: {
        id: string;
        status: string;
        startDate: string;
        endDate: string;
      };
      verifiedBy: {
        id: string;
        name: string;
        email: string;
      };
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      totalUniqueUsers: number;
    };
    summary: {
      totalVerifiedPayments: number;
      totalUniqueUsers: number;
      totalRevenue: number;
      averagePaymentPerUser: number;
    };
  };
}

// Add this function at the bottom
export const AdminGetVerifyedBanktransfer = async (): Promise<VerifiedBankTransferHistoryResponse> => {
  try {
    const response = await customAxios.get("/v1/bank-transfer/admin/verified-users");
    return response.data;
  } catch (error) {
    console.error("Error fetching verified bank transfer history:", error);
    throw error;
  }
};