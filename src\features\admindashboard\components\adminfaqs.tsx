"use client";
import { useEffect, useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { MdEdit, MdDeleteOutline } from "react-icons/md";
import { IoIosAddCircleOutline } from "react-icons/io";
import { Button } from "@/components/ui/button";
import ConfirmDeleteModal from "../components/common/deletemodal";
import AddFAQModal from "../components/common/faqsmodal";
import { getAllFAQ, deleteFAQId } from "./faq_setting_api/faq_api/faqsapi";

interface FAQ {
  id: string;
  question: string;
  answer: string;
}

export default function AdminFAQs() {
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  const [showModal, setShowModal] = useState(false);
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editFAQData, setEditFAQData] = useState<FAQ | null>(null);

  useEffect(() => {
    fetchFAQs();
  }, []);

  const fetchFAQs = async () => {
    try {
      const response = await getAllFAQ();
      const allFaqs = Array.isArray(response) ? response : response?.data || [];
      setFaqs(allFaqs);
    } catch (error) {
      console.error("Error fetching FAQs:", error);
      setFaqs([]);
    }
  };

  const toggleFAQ = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };



  const openDeleteModal = (index: number) => {
    setDeleteIndex(index);
    setShowModal(true);
  };

  const handleDelete = async () => {
    if (deleteIndex !== null) {
      const faqToDelete = faqs[deleteIndex];
      try {
        await deleteFAQId(faqToDelete.id); // Delete FAQ from server
        const updated = faqs.filter((faq) => faq.id !== faqToDelete.id);
        setFaqs(updated); // Update the UI after deletion
        setDeleteIndex(null);
        setShowModal(false);
      } catch (error) {
        console.error("Failed to delete FAQ:", error);
      }
    }
  };

  return (
    <div className="w-full mx-auto px-10">
      <div className="flex justify-between">
        <h2 className="text-[30px] font-bold mb-6">Frequently Asked Questions</h2>
        <Button
          variant="gradient"
          className="flex items-center px-6 py-6 text-white"
          onClick={() => {
            setEditFAQData(null);
            setShowAddModal(true);
          }}
        >
          Add New FAQ
          <IoIosAddCircleOutline className="text-4xl ml-2 shrink-0" />
        </Button>
      </div>

      {faqs.map((faq, index) => (
        <div
          key={faq.id}
          className={`relative rounded-[18px] border mb-4 mt-3 transition-all duration-200 ${activeIndex === index ? "border-[#f14e64] shadow-[0px_6px_16px_0px_#D5314830] p-3 border-2 bg-white" : "shadow-[0px_5px_16px_0px_#080F340F] p-2"}`}
        >
          <button
            onClick={() => toggleFAQ(index)}
            className="w-full flex justify-between items-center px-6 py-4 text-left"
          >
            <span className="font-semibold text-gray-800 text-[18px] flex-1">{faq.question}</span>
            {activeIndex === index ? (
              <ChevronDown className="text-white w-7 h-7 rounded-full bg-[#D53148]" />
            ) : (
              <ChevronRight className="bg-[#D53148] text-[#e4e3e3] w-7 h-7 rounded-full" />
            )}
          </button>

          {activeIndex === index && (
            <div className="relative px-6 pb-10 text-gray-600 text-[18px]">
              <p className="w-[95%]">{faq.answer}</p>

              <div className="absolute top-0 right-6 flex flex-col space-y-1 items-center">
                {/* <div className="relative">
                  <MdOutlineFileCopy
                    className="text-2xl text-gray-600 cursor-pointer hover:text-[#D53148]"
                    title="Copy"
                    onClick={() => handleCopy(index)}
                  />
                  {copiedIndex === index && (
                    <span className="absolute -top-6 -left-2 text-sm text-[#D53148] animate-pulse">Copied!</span>
                  )}
                </div> */}

                <MdEdit
                  className="text-xl text-[#555] hover:text-[#D53148] cursor-pointer"
                  title="Edit"
                  onClick={() => {
                    setEditFAQData(faq);
                    setShowAddModal(true);
                  }}
                />

                <MdDeleteOutline
                  className="text-2xl text-gray-600 cursor-pointer hover:text-[#D53148]"
                  title="Delete"
                  onClick={() => openDeleteModal(index)}
                />
              </div>
            </div>
          )}
        </div>
      ))}

      <ConfirmDeleteModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleDelete}
      />

      <AddFAQModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        isEditMode={!!editFAQData}
        initialData={editFAQData || undefined}
        onSuccess={fetchFAQs}
      />
    </div>
  );
}
