import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux"; // Import useSelector for Redux
import { getcordinatorclientLeaves } from "./Annotatornotification_api/annonatornotification_api";
import { toast } from "react-toastify";

interface Annotator {
  id: string;
  name: string;
  email: string;
}

interface LeaveRequest {
  id: string;
  annotatorId: string;
  startDate: string;
  endDate: string;
  reason: string;
  status: string;
  rejectionReason: string | null;
  approvedById: string | null;
  approvedAt: string | null;
  createdAt: string;
  updatedAt: string;
  approvedBy: {
    id: string;
    name: string;
    email: string;
  } | null;
}

interface AuthState {
  user: Annotator | null; // Match the User interface from Redux
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const AnnotatorLeaveNotification: React.FC = () => {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  // const [processingIds, setProcessingIds] = useState<string[]>([]);
  
  // Get user data from Redux store
  const user = useSelector((state: { auth: AuthState }) => state.auth.user);

  useEffect(() => {
    const fetchLeaveRequests = async () => {
      try {
        const response = await getcordinatorclientLeaves();
        console.log("Leave API Response:", response.data); // Debug
        const leaveData = Array.isArray(response.data) ? response.data : [];
        setLeaveRequests(leaveData);
      } catch (error) {
        console.error("Error fetching leave requests:", error);
        toast.error("Failed to fetch leave requests");
        setLeaveRequests([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaveRequests();
  }, []);

  // Function to get annotator details from Redux
  const getAnnotatorDetails = (annotatorId: string) => {
    if (user && user.id === annotatorId) {
      return { name: user.name, email: user.email };
    }
    return { name: "Unknown", email: "N/A" }; // Fallback if no match
  };

 // In AdminAnnotatorCard.tsx, update the formatDate function to return hh:mm [weekday] dd/mm/yyyy
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-based
  const year = date.getFullYear();
  const weekday = date.toLocaleString("en-US", { weekday: "long" }); // Get full weekday name (e.g., Monday)
  return `${hours}:${minutes} ${weekday} ${day}/${month}/${year}`;
};

  const formatDateRange = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return "N/A";
    const start = new Date(startDate).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    const end = new Date(endDate).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    return `${start} - ${end}`;
  };

  const getStatusBadge = (status: string) => {
    let bgColor = "bg-gray-200";
    let textColor = "text-gray-800";

    if (status === "APPROVED") {
      bgColor = "bg-green-500";
      textColor = "text-white";
    } else if (status === "PENDING") {
      bgColor = "bg-yellow-200";
      textColor = "text-yellow-800";
    } else if (status === "REJECTED") {
      bgColor = "bg-red-500";
      textColor = "text-white";
    }

    return (
      <span
        className={`px-4 py-2 text-sm font-semibold rounded-md ${bgColor} ${textColor}`}
      >
        {status}
      </span>
    );
  };

  return (
    <div className="space-y-4">
      {loading ? (
        <div className="text-center py-10">Loading leave requests...</div>
      ) : leaveRequests.length === 0 ? (
        <div className="text-center text-[16px] font-semibold font-poppins py-10 text-gray-500">
          No pending leave requests
        </div>
      ) : (
        leaveRequests.map((request) => {
          const annotator = getAnnotatorDetails(request.annotatorId);
          return (
            <div key={request.id} className="bg-white border rounded-lg shadow p-6">
              <div className="mb-2">
                <h3 className="text-lg font-semibold">
                  {annotator.name !== "Unknown"
                    ? `${annotator.name} has requested a leave`
                    : `Leave request (Annotator ID: ${request.annotatorId})`}
                </h3>
                <p className="text-gray-600 text-sm">
                  Leave period: {formatDateRange(request.startDate, request.endDate)}
                </p>
                <p className="text-gray-600 text-sm">
                  Reason: {request.reason || "N/A"}
                </p>
                {request.approvedBy && (
                  <p className="text-gray-600 text-sm">
                    Approved by: {request.approvedBy.name}  
                  </p>
                )}
                {request.rejectionReason && (
                  <p className="text-gray-600 text-sm">
                    Rejection Reason: {request.rejectionReason}
                  </p>
                )}
              </div>
              <div className="flex items-center mt-4">
                <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden mr-3">
                  <img
                    src={`https://ui-avatars.com/api/?name=${annotator.name}&background=random`}
                    alt={annotator.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-800">{annotator.name}</p>
                  <p className="text-sm text-gray-500">{annotator.email}</p>
                </div>
                <div className="ml-auto space-x-2 flex items-center">
                  <span className="text-xs text-gray-400 mr-4">
                    {formatDate(request.createdAt)}
                  </span>
                  {getStatusBadge(request.status)}
                </div>
              </div>
            </div>
          );
        })
      )}
    </div>
  );
};

export default AnnotatorLeaveNotification;