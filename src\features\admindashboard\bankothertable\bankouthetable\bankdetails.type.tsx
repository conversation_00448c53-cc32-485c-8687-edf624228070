// bankdetails.type.ts
export interface BankDetailsType {
  id: string;
  paymentId: string;
  name: string;
  transactionId: string;
  transactionDate: string;
  transferedAccNo: string;
  bankHolderName: string;
  amount: number;
  accountNumber: string;
  status: string;
  screenshotUrl: string;
}

// Define custom TableMeta type for potential future use
export interface CustomTableMeta<> {
  refreshData?: () => void;
}

// Add this below your existing types in bankdetails.type.ts
export interface BankTransferHistoryType extends BankDetailsType {
  verifiedBy: string;
  verifiedAt: string;
  adminNotes?: string | null;
}