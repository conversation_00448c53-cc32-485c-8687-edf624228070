import { useDroppable } from "@dnd-kit/core";
import { AnimatePresence } from "framer-motion";
// import { Task } from "@/types/adminkanbantype";
import { DetailsTask } from "@/types/kanbantasktype";
import { AnnotatorDraggableProjectDetailsTask } from "./annotator_details_draggable";
import AnnotatorCreateProjectTask from "./annotator_details_task";

export type Column = {
  id: string;
  title: string;
  tasks: DetailsTask[];
};

export function AnnotatorColumncontainerDetailstask({
  column,
  tasks,
  onDeleteTask,
  onAddTask,
  fetchTasksProp,
}: {
  column: Column;
  tasks: DetailsTask[];
  onDeleteTask: (task: DetailsTask) => void;
  onAddTask: (newTask: DetailsTask, columnId: string) => void;
  fetchTasksProp: (saveToLocalStorage: boolean) => Promise<void>;
}) {
  const { setNodeRef } = useDroppable({
    id: column.id,
  });
  const fetchTasks = () => fetchTasksProp(true);

  const getColumnColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-[#FBF4F4]";
      case "in-progress":
        return "bg-[#FBF4F4]";
      case "done":
        return "bg-[#FBF4F4]";
      default:
        return "bg-[#FBF4F4]";
    }
  };

  const getTitleIndicatorColor = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return "bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]";
      case "in-progress":
        return "bg-white";
      case "done":
        return "bg-white";
      default:
        return "bg-gray-400";
    }
  };

  return (
    <div>
      <div
        className={`w-40 p-3 flex justify-center items-center rounded-full  ${getTitleIndicatorColor(
          column.id
        )}`}
      >
        <AnnotatorCreateProjectTask
          onAddTask={(task) => onAddTask(task, column.id)}
          columnId={column.id}
        />
      </div>
      <div className="flex justify-between px-1 mb-2 items-center mt-4">
        <h2 className="font-semibold text-[#545454] text-muted-foreground">
          {column.title}
        </h2>
      </div>
      <div
        ref={setNodeRef}
        className={`rounded-lg p-4 mt-4 border border-red-500  ${getColumnColor(
          column.id
        )}`}
        style={{ height: "443px", overflowY: "auto" }}
      >
        <AnimatePresence mode="popLayout">
          {tasks.map((task) => (
            <AnnotatorDraggableProjectDetailsTask
              key={task.id}
              task={task}
              column={column}
              onDelete={onDeleteTask}
              fetchTasks={fetchTasks}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
