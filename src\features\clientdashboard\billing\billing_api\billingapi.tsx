import { customAxios } from "@/utils/axio-interceptor"

export const getBillingList = async () => {
  try {
    const response = await customAxios.get('/v1/billings/clients');
    return response.data;
  } catch (error) {
    console.error("Error fetching billing data:", error);
    throw error;
  }
}




export const getSubscriptionDetails = async () => {
     try {
    const response = await customAxios.get('/v1/billings/subscriptions');
    return response.data;
  } catch (error) {
    console.error("Error fetching Subascriptions  data:", error);
    throw error;
  }
}

export const getUserProfile = async () => {
  try {
    console.log("Making profile API call...");
    const response = await customAxios.get('/v1/users/profile');
    console.log("Profile API response:", response);
    return response.data;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw error;
  }
}


//biloing tbale payment id invoice  billing list se payment id aayega tbale me billing list aadd hai waha se 
export const getInvoiceData = async (paymentId: string) => {
  try {
    const response = await customAxios.get(`/v1/billings/invoice/${paymentId}`);
    
    if (response.data.status === 0) {
      throw new Error(response.data.message || "Payment not found");
    }
    
    return response.data;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
}