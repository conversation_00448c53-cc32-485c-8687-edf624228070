// utils/api.ts
import { customAxios } from "@/utils/axio-interceptor";

interface AdminPayload {
  name: string;
  lastname: string;
  email: string;
  password?: string;
  sendPasswordLink?: boolean;
  domain?: string;
}

export const AdminCreate = async (payload: AdminPayload) => {
  const response = await customAxios.post("/v1/admin/create-admin", payload);
  return response.data;
};

export const getAllAdmins = async () => {
  const response = await customAxios.get("/v1/admin/get-all-admin");
  return response.data;
};

//admin id
export const getAdminById = async (id: string) => {
  const response = await customAxios.get(`/v1/admin/get-by-id/${id}`);
  return response.data;
};

//{getbyid
// {
//   "name": "Virat",
//   "lastname": "<PERSON><PERSON><PERSON>",
//   "email": "<EMAIL>",
//   "domain": "Macgence.in",
//   //"sendPasswordLink": false,
//   "password": "********"
// }
//}

// suspend admin
export const suspendAdmin = async (id: string, duration: string) => {
  const response = await customAxios.put(`/v1/admin/suspend-admin/${id}`, {
    duration: duration, // Send the duration parameter
  });
  return response.data;
};

//active account admin
export const activeAdmin = async (id: string) => {
  const response = await customAxios.put(`/v1/admin/activate-admin/${id}`);
  return response.data;
};

// delete admin
export const deleteAdmin = async (id: string) => {
  const response = await customAxios.delete(`/v1/admin/delete-admin/${id}`);
  return response.data;
};

// api/adminList_api.ts or wherever you define your API calls
export const resetAdminPassword = async (id: string, newPassword: string) => {
  const response = await customAxios.put(`/v1/admin/reset-password/${id}`, {
    newPassword,
  });
  return response.data;
};
