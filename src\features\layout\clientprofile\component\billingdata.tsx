// components/BillingData.tsx
import { Input } from '@/components/ui/input'
// import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'

import { useEffect, useState } from 'react'
import { BillingDetailsApiGet, clientProfileShow } from '../profileclient_api/Clientprofile_api'

interface AddressData {
  country: string;
  state: string;
  address: string;
  postalCode: string;
}

const BillingData = () => {
  // const [sameAsBusiness, setSameAsBusiness] = useState(false);
  const [billingData, setBillingData] = useState<AddressData>({
    country: '',
    state: '',
    address: '',
    postalCode: ''
  });
  // const [businessData, setBusinessData] = useState<AddressData>({
  //   country: '',
  //   state: '',
  //   address: '',
  //   postalCode: ''
  // });
  const [taxId, setTaxId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log("settexid in billingdata", setTaxId)
  useEffect(() => {
    const fetchAddressData = async () => {
      try {
        // Fetch both addresses in parallel
        const [billingResponse] = await Promise.all([
          BillingDetailsApiGet(),
          clientProfileShow()
        ]);

        setBillingData({
          country: billingResponse.country,
          state: billingResponse.state,
          address: billingResponse.address || billingResponse.street,
          postalCode: billingResponse.postalCode
        });

        // setBusinessData({
        //   country: profileResponse.profile.country,
        //   state: profileResponse.profile.stateProvince,
        //   address: profileResponse.profile.address,
        //   postalCode: profileResponse.profile.postalCode
        // });

      } catch (err) {
        console.error('Failed to fetch address data:', err);
        setError('Failed to load address data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchAddressData();
  }, []);

  // const handleCheckboxChange = (checked: boolean) => {
  //   setSameAsBusiness(checked);
  // };

  const displayData =  billingData;

  if (loading) {
    return (
      <div className="w-full mx-auto p-6 bg-white rounded-lg space-y-6">
        <h2 className="text-xl font-semibold">Billing Address</h2>
        <div>Loading address data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full mx-auto p-6 bg-white rounded-lg space-y-6">
        <h2 className="text-xl font-semibold">Billing Address</h2>
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-lg space-y-6">
      <h2 className="text-xl font-semibold">Billing Address</h2>

      {/* <div className="flex items-center space-x-2">
        <Checkbox
          id="same-address"
          checked={sameAsBusiness}
          onCheckedChange={handleCheckboxChange}
          className='bg-[#F9EFEF]'
        />
        <Label htmlFor="same-address">Same as Business Address</Label>
      </div> */}

      <div className="flex flex-col gap-6">
        <div className="flex flex-row gap-2">
          <div className="w-1/2">
            <Label>Country</Label>
          <div className='border-gradient rounded-lg'>
              <Input
                value={displayData.country}
                className="text-[#5E5E5E] bg-[#F9EFEF]"
                readOnly
              />
           </div>
          </div>

          <div className="w-1/2">
            <Label>State</Label>
          <div className='border-gradient rounded-lg'>
              <Input
                value={displayData.state}
                className="bg-[#F9EFEF] text-[#5E5E5E]"
                readOnly
              />
           </div>
          </div>
        </div>

        <div className="flex-row flex gap-4 w-full">
          <div className="w-1/2">
            <Label>QST/VAT (Optional)</Label>
            <div className='border-gradient rounded-lg'>
              <Input
                value={taxId || ''}
                className="bg-[#F9EFEF] text-[#5E5E5E]"
                readOnly
                placeholder="Not provided"
              />
          </div>
          </div>

          <div className="w-1/2">
            <Label>Address</Label>
            <div className='border-gradient rounded-lg'>
              <Input
                value={displayData.address}
                className="bg-[#F9EFEF] text-[#5E5E5E]"
                readOnly
              />
           </div>
          </div>
        </div>

        <div className='w-full flex flex-row gap-4'>
          <div className="w-1/2">
            <Label>Postal Code</Label>
            <div className='border-gradient rounded-lg'>
              <Input
                value={displayData.postalCode}
                className="bg-[#F9EFEF] text-[#5E5E5E] rounded-[0.65rem]"
                readOnly
              />
           </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingData;