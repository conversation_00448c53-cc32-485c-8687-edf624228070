import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import { useEffect, useRef, useState } from "react";
import { ChevronDown } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { createFeature, fetchPackages } from "../../api/api";

type PackageType = {
  id: string;
  name: string;
};

type FormValues = {
  featureTitle: string;
  featureCategory: string[];
};

type FeaturesModalProps = {
  onSuccess?: () => void;
};

const FeaturesModal = ({ onSuccess }: FeaturesModalProps) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [packages, setPackages] = useState<PackageType[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null); // Ref for dropdown container

  useEffect(() => {
    if (isSubmitting) {
      window.location.reload();
    }
  }, [isSubmitting]);

  const closeDialogRef = useRef<HTMLButtonElement>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      featureTitle: "",
      featureCategory: [],
    },
  });

  const selectedCategories = watch("featureCategory");

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Fetch Packages on mount
  useEffect(() => {
    const getPackages = async () => {
      try {
        const data = await fetchPackages();
        setPackages(data);
      } catch (error) {
        console.error("Error fetching packages:", error);
        toast.error("Failed to load packages");
      }
    };
    getPackages();
  }, []);

  const toggleCategory = (value: string) => {
    const currentValues = selectedCategories || [];
    const updatedValues = currentValues.includes(value)
      ? currentValues.filter((v) => v !== value)
      : [...currentValues, value];
    setValue("featureCategory", updatedValues, { shouldValidate: true });
  };

  const onSubmit = async (formData: FormValues) => {
    if (formData.featureCategory.length === 0) {
      toast.error("Please select at least one category");
      return;
    }
    setIsSubmitting(true);
    try {
      const featureData = {
        rule: formData.featureTitle,
        packageIds: formData.featureCategory,
      };
      const response = await createFeature(featureData);
      console.log("Feature created:", response);
      toast.success("Feature created successfully!");
      reset();
      if (onSuccess) {
        onSuccess();
      }
      closeDialogRef.current?.click();
    } catch (error) {
      console.error("Error creating feature:", error);
      toast.error("Failed to create feature");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full flex flex-col gap-6"
    >
      <h2 className="text-xl font-semibold mb-4">Add New Feature</h2>
      <div className="flex flex-col justify-center gap-4">
        {/* Feature Title */}
        <div>
          <Label>Feature Title *</Label>
          <div className="border-gradient rounded-lg">
            <Input
              placeholder="Enter feature title"
              {...register("featureTitle", {
                required: "Feature title is required",
              })}
              className="bg-[#F9EFEF] text-[#5E5E5E]"
              disabled={isSubmitting}
            />
          </div>
          {errors.featureTitle && (
            <p className="text-red-500 text-sm mt-1">
              {errors.featureTitle.message}
            </p>
          )}
        </div>

        {/* Multi-select Category */}
        <div ref={dropdownRef}>
          <Label>Feature Category *</Label>
          <div className="relative w-full mt-2 border-blue-400 border rounded-md">
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault(); // Prevent form submission
                setDropdownOpen(!dropdownOpen);
              }}
              disabled={isSubmitting}
              className={`w-full py-1 px-2 rounded-md text-left flex items-center justify-between bg-[#F9EFEF] text-[#5E5E5E] ${
                isSubmitting ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {selectedCategories.length > 0
                ? packages
                    .filter((pkg) => selectedCategories.includes(pkg.id))
                    .map((pkg) => pkg.name)
                    .join(", ")
                : "Select category"}
              <ChevronDown
                className={`ml-2 h-4 w-4 transition-transform ${
                  dropdownOpen ? "rotate-180" : "rotate-0"
                }`}
              />
            </button>

            {dropdownOpen && (
              <div className="absolute w-full max-h-[200px] overflow-y-auto bg-white border-blue-400 border mt-1 rounded-md shadow-lg z-10">
                {packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className="flex items-center justify-between cursor-pointer py-2 px-3 hover:bg-gray-100"
                    onClick={() => !isSubmitting && toggleCategory(pkg.id)}
                  >
                    <div className="flex items-center w-full">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(pkg.id)}
                        onChange={() => toggleCategory(pkg.id)}
                        disabled={isSubmitting}
                        className="mr-2 accent-brown-san-x cursor-pointer"
                      />
                      <p className="truncate">{pkg.name}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          {errors.featureCategory && (
            <p className="text-red-500 text-sm mt-1">
              Please select at least one category
            </p>
          )}
        </div>
      </div>

      {/* Footer Buttons */}
      <div className="mt-6 flex justify-end gap-2">
        <Button
          type="submit"
          variant="gradient"
          className="px-[4rem] py-6"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save"}
        </Button>
        <DialogClose>
          <Button
            ref={closeDialogRef}
            variant="ghost"
            className="px-[4rem] py-6 border-gradient"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </DialogClose>
      </div>
    </form>
  );
};

export default FeaturesModal;