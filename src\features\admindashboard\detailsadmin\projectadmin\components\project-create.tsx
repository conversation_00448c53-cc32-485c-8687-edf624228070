import { CircleHelp, Upload } from "lucide-react";
import { useState } from "react";
import { useProjectCreateStyles } from "../styles/ProjectCreateStyles";

export default function ProjectCreate({
  onClose,
  onSuccess,
}: {
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "",
    annotator: "",
    attachment: null as File | null,
    startDate: "",
    endDate: "",
  });

  const [errors, setErrors] = useState({
    title: false,
    description: false,
    priority: false,
    annotator: false,
    startDate: false,
    endDate: false,
  });

  const [apiError, setApiError] = useState("");

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: false });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData({ ...formData, attachment: file });
  };

  const validateForm = () => {
    let newErrors = {
      title: !formData.title,
      description: !formData.description,
      priority: !formData.priority,
      annotator: !formData.annotator,
      startDate: !formData.startDate,
      endDate: !formData.endDate,
    };

    // Additional validation for dates
    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);

      if (end < start) {
        newErrors.endDate = true;
        setApiError("Due date cannot be earlier than start date");
      }
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setApiError("");

    if (validateForm()) {
      console.log("Form submitted:", formData);
      onClose(); // Close modal
      onSuccess(); // Trigger toast from parent
    }
  };

  const styles = useProjectCreateStyles();

  return (
    <div className={styles.modalContainer}>
      <div className={styles.formContainer}>
        <h2 className={styles.heading}>Create Project</h2>
        {apiError && <div className={styles.errorContainer}>{apiError}</div>}
        <form onSubmit={handleSubmit} className={styles.formSpacing}>
          {/* Title Field */}
          <div>
            <label className={styles.label}>Title *</label>
            <div className={styles.inputContainer}>
              <input
                type="text"
                name="title"
                placeholder="Enter task title"
                className={`${styles.input} ${
                  errors.title ? "border-red-500" : ""
                }`}
                value={formData.title}
                onChange={handleChange}
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-sm">Title is required</p>
            )}
          </div>

          {/* Description Field */}
          <div>
            <label className={styles.label}>Description *</label>
            <div className={styles.textareaContainer}>
              <div className={styles.textareaInner}>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className={styles.textarea}
                  placeholder="Enter description*"
                ></textarea>
              </div>
            </div>
            {errors.description && (
              <p className="text-red-500 text-sm">Description is required</p>
            )}
          </div>

          <div className={styles.flexContainer}>
            {/* Priority Field */}
            <div>
              <label className={styles.label}>Priority *</label>
              <div className={styles.selectContainer}>
                <select
                  name="priority"
                  className={`${styles.select} ${
                    errors.priority ? "ring-1 ring-red-500" : ""
                  }`}
                  value={formData.priority}
                  onChange={handleChange}
                >
                  <option value="">Select Priority</option>
                  <option value="HIGH">High</option>
                  <option value="MEDIUM">Medium</option>
                  <option value="LOW">Low</option>
                </select>
              </div>
              {errors.priority && (
                <p className="text-red-500 text-sm">Priority is required</p>
              )}
            </div>

            {/* Annotator Field */}
            <div>
              <label className={styles.label}>Annotator *</label>
              <div className={styles.selectContainer}>
                <select
                  name="annotator"
                  className={`${styles.select} ${
                    errors.annotator ? "ring-1 ring-red-500" : ""
                  }`}
                  value={formData.annotator}
                  onChange={handleChange}
                >
                  <option value="">Select Annotator</option>
                  <option value="1">John Doe</option>
                  <option value="2">Jane Smith</option>
                </select>
              </div>
              {errors.annotator && (
                <p className="text-red-500 text-sm">Annotator is required</p>
              )}
            </div>
          </div>

          {/* Date Fields */}
          <div className={styles.flexContainer}>
            <div>
              <label className={styles.label}>Start Date *</label>
              <div className={styles.dateContainer}>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  className={`${styles.dateInput} ${
                    errors.startDate ? "ring-1 ring-red-500" : ""
                  }`}
                />
              </div>
              {errors.startDate && (
                <p className="text-red-500 text-sm">Start Date is required</p>
              )}
            </div>

            <div>
              <label className={styles.label}>Due Date *</label>
              <div className={styles.dateContainer}>
                <input
                  type="date"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  className={`${styles.dateInput} ${
                    errors.endDate ? "ring-1 ring-red-500" : ""
                  }`}
                />
              </div>
              {errors.endDate && (
                <p className="text-red-500 text-sm">Due Date is required</p>
              )}
            </div>
          </div>

          {/* Attachment Field (Optional) */}
          <div>
            <label className={styles.label}>Attachment (Optional)</label>
            <div className={styles.uploadContainer}>
              <div className={styles.uploadInner}>
                <Upload className={styles.uploadIcon} />
                <span className={styles.uploadText}>Choose file</span>
                <input
                  type="file"
                  name="attachment"
                  onChange={handleFileChange}
                  className={styles.uploadInput}
                />
              </div>
            </div>
          </div>

          <div className={styles.helpContainer}>
            <CircleHelp className={styles.helpIcon} />
            <p>Required fields are marked with *</p>
          </div>

          {/* Buttons */}
          <div className={styles.buttonContainer}>
            <button
              type="button"
              onClick={onClose}
              className={styles.cancelButton}
            >
              Cancel
            </button>

            <button type="submit" className={styles.submitButton}>
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
