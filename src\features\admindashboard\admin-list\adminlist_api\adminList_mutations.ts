import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { suspendAdmin, activeAdmin } from "./adminList_api";
import { errorMessage } from "@/utils/errorHandler";
import { customAxios } from "@/utils/axio-interceptor";
import { resetAdminPassword } from "./adminList_api";

  export const useResetAdminPasswordMutation = () => {
  return useMutation({
    mutationFn: ({ id, newPassword }: { id: string; newPassword: string }) =>
      resetAdminPassword(id, newPassword),
  });
};

// Delete admin API function
export const deleteAdmin = async (id: string) => {
  const response = await customAxios.delete(`/v1/admin/delete-admin/${id}`);
  return response.data;
};

// Suspend Admin Mutation
export function useSuspendAdminMutation() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (data: { id: string; duration: string }) =>
      suspendAdmin(data.id, data.duration),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Admin suspended successfully");
        await queryClient.invalidateQueries({ queryKey: ["admins"] });
      }
    },
  });

  return {
    ...mutation,
    isLoading: mutation.isPending
  };
}

export function useActivateAdminMutation() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (id: string) => activeAdmin(id),
    onSuccess: async () => {
      toast.success("Admin activated successfully");
      await queryClient.invalidateQueries({ queryKey: ["admins"] });
    },
    onError: (error: any) => {
      console.error(error);
      if (error?.response?.data) {
        errorMessage(error);
      } else {
        toast.error("Failed to activate admin");
      }
    },
  });

  return mutation;
}


// Reset Admin Password Mutation
// export function useResetAdminPasswordMutation() {
//   const queryClient = useQueryClient();

//   const mutation = useMutation({
//     mutationFn: (data: { id: string; password: string | null; sendPasswordLink: boolean }) =>
//       resetAdminPassword(data),
//     onSettled: async (_data, error) => {
//       if (error) {
//         console.error(error);
//         if ((error as any)?.response?.data) {
//           errorMessage(error);
//         }
//       } else {
//         toast.success("Password reset successfully");
//         await queryClient.invalidateQueries({ queryKey: ["admins"] });
//       }
//     },
//   });

//   return {
//     ...mutation,
//     isLoading: mutation.isPending
//   };
// }

// Delete Admin Mutation
export function useDeleteAdminMutation() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: (id: string) => deleteAdmin(id),
    onSettled: async (_data, error) => {
      if (error) {
        console.error(error);
        if ((error as any)?.response?.data) {
          errorMessage(error);
        }
      } else {
        toast.success("Admin deleted successfully");
        await queryClient.invalidateQueries({ queryKey: ["admins"] });
      }
    },
  });

  return {
    ...mutation,
    isLoading: mutation.isPending
  };
}
