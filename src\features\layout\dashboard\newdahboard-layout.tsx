// @ts-ignore
import React, { useState } from "react";
import { sidebarData } from "./sidebar-newdata";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Outlet } from "react-router-dom";
import { USER_ROLES } from "@/utils/constants";
import TopNavbar from "../top-navbar";
import Sidebar from "./components/Sidebar";
import { Toaster } from "@/components/ui/toaster";

export default function DashboardSidebar() {
  const userRole = useSelector((state: RootState) => state.auth.user?.role);
  const [navbar, setNavbar] = useState(false);
  const handleNavbar = () => setNavbar(!navbar);

  if (!userRole) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <span>Loading...</span>
      </div>
    );
  }

  return (
    <div className="w-full flex bg-white">
      <div className="w-full">
        {/* TopNavbar with consistent styling */}
        <TopNavbar handleNavbar={handleNavbar} navbar={navbar} />

        <div className="h-[calc(100vh-71px)] flex">
          {/* Sidebar with consistent styling */}
          {userRole && (
            <Sidebar
              sidebarData={sidebarData}
              userRole={userRole as USER_ROLES}
              navbar={navbar}
            />
          )}

          {/* Main Content */}
          <div className="flex-1 overflow-y-auto bg-white lg:p-4 xl:p-5 2xl:p-6">
            <Outlet />
          </div>
        </div>

        {/* Toast component for notifications */}
        <Toaster />
      </div>
    </div>
  );
}