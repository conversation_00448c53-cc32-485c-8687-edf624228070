"use client";

import {
  ColumnDef,
  SortingState,
  flexRender,
  ColumnFiltersState,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useReactTable,
  getPaginationRowModel,
  PaginationState,
  Updater,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useMemo, useState, useRef, useEffect } from "react";
import { DataTablePagination } from "./pagination";
import { Loader } from "@/components/globalfiles/loader";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  loading?: boolean;
  onPaginationChange?: (updater: Updater<PaginationState>) => void;
  pagination?: PaginationState;
  pageCount?: number;
  title?: string;
  disablePagination?: boolean;
  className?: string;
  totalCount?: number;
  fetchMoreData?: () => Promise<void>;
  hasMore?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  loading,
  onPaginationChange,
  pagination,
  pageCount,
  disablePagination = false,
  className = "",
  totalCount,
  fetchMoreData,
  hasMore = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [localPagination, setLocalPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 100,
  });
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  const effectivePagination = pagination || localPagination;
  const effectivePageCount = disablePagination
    ? 1
    : pageCount ||
      Math.ceil((totalCount || data.length) / effectivePagination.pageSize);

  // Infinite scroll handler
  useEffect(() => {
    const container = tableContainerRef.current;
    if (!container || disablePagination || !fetchMoreData) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - (scrollTop + clientHeight) < 100;

      if (isNearBottom && hasMore && !isFetchingMore && !loading) {
        setIsFetchingMore(true);
        fetchMoreData().finally(() => setIsFetchingMore(false));
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [hasMore, isFetchingMore, loading, disablePagination, fetchMoreData]);

  // Only paginate if pagination is enabled and not using infinite scroll
  const paginatedData = useMemo(() => {
    if (disablePagination || fetchMoreData) return data;
    const start = effectivePagination.pageIndex * effectivePagination.pageSize;
    const end = start + effectivePagination.pageSize;
    return data.slice(start, end);
  }, [data, disablePagination, effectivePagination, fetchMoreData]);

  const table = useReactTable({
    data: disablePagination || fetchMoreData ? data : paginatedData,
    columns,
    pageCount: effectivePageCount,
    state: {
      sorting,
      columnFilters,
      pagination: effectivePagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function" ? updater(effectivePagination) : updater;
      if (onPaginationChange) {
        onPaginationChange(newPagination);
      }
      if (!pagination) {
        setLocalPagination(newPagination);
      }
    },
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel:
      disablePagination || fetchMoreData ? undefined : getPaginationRowModel(),
    manualPagination: !disablePagination && !fetchMoreData,
  });

  return (
    <div className={`w-full rounded-md ${className}`}>
      <div
        ref={tableContainerRef}
        className="border border-[#FF577F] h-[calc(100vh-250px)] min-h-[400px] overflow-y-auto rounded-md"
      >
        <Table className="min-w-full">
          <TableHeader className="sticky top-0 z-20 bg-white shadow-sm">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="text-[#000000] sticky top-0 border-b-[3px] border-b-[#DFE1E6] font-medium text-[12px] whitespace-nowrap"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading && !data.length ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center h-[300px]">
                    <Loader />
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-[300px] text-center text-muted-foreground"
                >
                  No data available
                </TableCell>
              </TableRow>
            )}
            {isFetchingMore && (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="text-center py-4"
                >
                  <Loader />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {!disablePagination && !fetchMoreData && (
        <div className="flex items-center justify-between px-2 py-4">
          <DataTablePagination table={table} />
        </div>
      )}
    </div>
  );
}
