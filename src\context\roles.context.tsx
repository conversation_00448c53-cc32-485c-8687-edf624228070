import React, { createContext, useContext } from "react";
import { Role, rolePermissions } from "../utils/permissions";

interface RoleContextType {
  role: Role | null | undefined;
  permissions: (typeof rolePermissions)[Role] | null;
}

const RoleContext = createContext<RoleContextType | null>(null);

export const RoleProvider = ({
  children,
  role,
}: {
  children: React.ReactNode;
  role: Role | null | undefined;
}) => {
  const permissions = role ? rolePermissions[role] : null;
  return (
    <RoleContext.Provider value={{ role, permissions }}>
      {children}
    </RoleContext.Provider>
  );
};

export const useRole = () => {
  const context = useContext(RoleContext);
  if (!context) throw new Error("useRole must be used within a RoleProvider");
  return context;
};
