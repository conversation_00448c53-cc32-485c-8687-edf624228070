import React, { useState, useEffect } from "react";
import { getcordinatorc<PERSON><PERSON>ea<PERSON>, AproovLeave, RejectLeave } from "./clientnotification_api/adminnotification_api";
import { toast } from "react-toastify";

interface Annotator {
  id: string;
  name: string;
  email: string;
}

interface LeaveRequest {
  id: string;
  annotatorId: string;
  startDate: string;
  endDate: string;
  reason: string;
  status: string;
  rejectionReason: string | null;
  approvedById: string | null;
  approvedAt: string | null;
  createdAt: string;
  updatedAt: string;
  annotator: Annotator;
  approvedBy: {
    id: string;
    name: string;
    email: string;
  } | null;
}

const ClientLeaveNotification: React.FC = () => {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingIds, setProcessingIds] = useState<string[]>([]);

  useEffect(() => {
    const fetchLeaveRequests = async () => {
      try {
        const response = await getcordinatorclientLeaves();
        // Assuming response.data.data contains the array of leave requests
        const leaveData = Array.isArray(response.data) ? response.data : [];
        setLeaveRequests(leaveData);
      } catch (error) {
        console.error("Error fetching leave requests:", error);
        toast.error("Failed to fetch leave requests");
        setLeaveRequests([]); // Ensure leaveRequests is an array on error
      } finally {
        setLoading(false);
      }
    };

    fetchLeaveRequests();
  }, []);

  const handleApprove = async (id: string) => {
    try {
      setProcessingIds((prev) => [...prev, id]);
      const response = await AproovLeave(id);

      if (response.status === 1) {
        toast.success("Leave approved successfully");
        setLeaveRequests((prev) =>
          prev.map((req) =>
            req.id === id ? { ...req, status: "APPROVED" } : req
          )
        );
      } else {
        toast.error("Failed to approve leave");
      }
    } catch (error) {
      console.error("Error approving leave:", error);
      toast.error("Error approving leave");
    } finally {
      setProcessingIds((prev) => prev.filter((item) => item !== id));
    }
  };

  const handleReject = async (id: string) => {
    try {
      setProcessingIds((prev) => [...prev, id]);
      const response = await RejectLeave(id);

      if (response.status === 1) {
        toast.success("Leave rejected successfully");
        setLeaveRequests((prev) =>
          prev.map((req) =>
            req.id === id ? { ...req, status: "REJECTED" } : req
          )
        );
      } else {
        toast.error("Failed to reject leave");
      }
    } catch (error) {
      console.error("Error rejecting leave:", error);
      toast.error("Error rejecting leave");
    } finally {
      setProcessingIds((prev) => prev.filter((item) => item !== id));
    }
  };

 // In AdminAnnotatorCard.tsx, update the formatDate function to return hh:mm [weekday] dd/mm/yyyy
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-based
  const year = date.getFullYear();
  const weekday = date.toLocaleString("en-US", { weekday: "long" }); // Get full weekday name (e.g., Monday)
  return `${hours}:${minutes} ${weekday} ${day}/${month}/${year}`;
};

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    const end = new Date(endDate).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    return `${start} - ${end}`;
  };

  const renderActionButtons = (request: LeaveRequest) => {
    if (request.status === "PENDING") {
      return (
        <div className="space-x-2">
          <button
            onClick={() => handleReject(request.id)}
            className="px-4 py-2 text-sm border rounded-md font-semibold text-gray-700 border-gray-300 hover:bg-gray-100"
            disabled={processingIds.includes(request.id)}
          >
            {processingIds.includes(request.id) ? "Processing..." : "Reject"}
          </button>
          <button
            onClick={() => handleApprove(request.id)}
            className="px-4 py-2 text-sm bg-red-500 text-white font-semibold rounded-md hover:bg-red-600"
            disabled={processingIds.includes(request.id)}
          >
            {processingIds.includes(request.id) ? "Processing..." : "Approve"}
          </button>
        </div>
      );
    }

    return (
      <span
        className={`px-4 py-2 text-sm font-semibold rounded-md ${
          request.status === "APPROVED"
            ? "bg-green-500 text-white"
            : "bg-red-500 text-white"
        }`}
      >
        {request.status}
      </span>
    );
  };

  return (
    <div className="space-y-4">
      {loading ? (
        <div className="text-center py-10">Loading leave requests...</div>
      ) : leaveRequests.length === 0 ? (
        <div className="text-center text-[16px] font-semibold font-poppins py-10 text-gray-500">
          No pending leave requests
        </div>
      ) : (
        leaveRequests.map((request) => (
          <div key={request.id} className="bg-white border rounded-lg shadow p-6">
            <div className="mb-2">
              <h3 className="text-lg font-semibold">
                {request.annotator.name} has requested a leave
              </h3>
              <p className="text-gray-600 text-sm">
                Leave period: {formatDateRange(request.startDate, request.endDate)}
              </p>
              <p className="text-gray-600 text-sm">Reason: {request.reason}</p>
                {request.approvedBy && (
                  <p className="text-gray-600 text-sm">
                    Approved by: {request.approvedBy.name} 
                  </p>
                )}
                {request.rejectionReason && (
                  <p className="text-gray-600 text-sm">
                    Rejection Reason: {request.rejectionReason}
                  </p>
                )}
            </div>
            <div className="flex items-center mt-4">
              <div className="w-10 h-10 rounded-full bg-gray-200 overflow-hidden mr-3">
                <img
                  src={`https://ui-avatars.com/api/?name=${request.annotator.name}&background=random`}
                  alt={request.annotator.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="font-semibold text-gray-800">
                  {request.annotator.name}
                </p>
                <p className="text-sm text-gray-500">{request.annotator.email}</p>
              </div>
              <div className="ml-auto space-x-2 flex items-center">
                <span className="text-xs text-gray-400 mr-4">
                  {formatDate(request.createdAt)}
                </span>
                {renderActionButtons(request)}
              </div>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ClientLeaveNotification;