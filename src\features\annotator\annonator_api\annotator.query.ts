import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { getAttendenceLog, getMyAttendenceLogs } from "./annonator_api";
import { AttendanceResponse } from "../types/attendencelog.types";

export const useGetAttendenceLog = () => {
  return useQuery({
    queryKey: ["attendencelog"],
    queryFn: () => getAttendenceLog(),
  });
};

// type AttendanceQueryParams = {
//   page: number;
//   limit: number;
//   from?: string;
//   to?: string;
// };

// export const useGetAttendenceLogsHistory = ({
//   page,
//   limit,
//   from,
//   to,
// }: AttendanceQueryParams) => {
//   return useQuery({
//     queryKey: ["attendencelog", { page, limit, from, to }],
//     queryFn: ({ queryKey }) => {
//       const [, params] = queryKey as [string, AttendanceQueryParams];
//       return getMyAttendenceLogs(params);
//     },
//   });
// };

type AttendanceQueryParams = {
  from?: string;
  to?: string;
  limit: number;
};

export const useInfiniteAttendanceLogs = ({
  from,
  to,
  limit,
}: AttendanceQueryParams) => {
  return useInfiniteQuery<AttendanceResponse, Error>({
    queryKey: ["attendencelog", { from, to, limit }],
    queryFn: async ({ pageParam = 1, queryKey }) => {
      const [, params] = queryKey as [string, AttendanceQueryParams];
      const queryParams: AttendanceQueryParams & { page: number } = {
        ...params,
        page: pageParam as number, // ✅ Cast pageParam to number
      };
      return getMyAttendenceLogs(queryParams);
    },
    getNextPageParam: (lastPage) => {
      const { page, totalPages } = lastPage.data.pagination;
      return page < totalPages ? page + 1 : undefined;
    },
    initialPageParam: 1,
  });
};
