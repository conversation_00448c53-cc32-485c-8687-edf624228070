import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive project card styles
export const useProjectCardStyles = () => {
  const { isLaptopMd } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Common styles for all screen sizes
  const commonStyles = {
    card: "border border-[#FF577F] rounded-lg shadow-md bg-white flex flex-col h-full",
    profileSection: "flex flex-col relative",
    statusContainer: "flex justify-end items-center absolute top-0 right-0",
    projectContainer: "flex items-center gap-x-1",
    projectImage: "rounded-md",
    nameContainer: "flex flex-col flex-1",
    name: "font-semibold",
    starIcon: "text-[#FFC107]",
    description: "text-gray-500",
    infoContainer: "flex-grow",
    infoRow: "flex items-center justify-between",
    infoLabel: "text-gray-600 font-medium",
    infoValue: "text-gray-800",
    progressContainer: "",
    progressBar: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] rounded-full",
    progressText: "text-gray-600",
    actionContainer: "flex justify-between mt-auto",
  };

  // Get styles based on screen size
  const getStyles = () => {
    // 1024px
    if (windowWidth <= 1024) {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-3`,
        profileSection: `${commonStyles.profileSection} mb-2 pt-5`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-1`,
        projectContainer: `${commonStyles.projectContainer}`,
        projectImage: `${commonStyles.projectImage} w-10 h-10`,
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        description: `${commonStyles.description} text-[11px]`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-2 py-0.5 text-[10px]",
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-2.5`,
        infoRow: `${commonStyles.infoRow} text-[11px] py-0.5`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: `${commonStyles.progressContainer} mt-2`,
        progressBar: `${commonStyles.progressBar} h-2.5`,
        progressText: `${commonStyles.progressText} text-[11px] mt-1`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1 text-[10px] rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-2 py-1 text-white text-[10px] rounded-lg flex-1 text-center"
      };
    }
    // 1440px
    else if (isLaptopMd) {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-4`,
        profileSection: `${commonStyles.profileSection} mb-3 pt-6`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-2`,
        projectContainer: `${commonStyles.projectContainer}`,
        projectImage: `${commonStyles.projectImage} w-10 h-10`,
        name: `${commonStyles.name} text-base`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        description: `${commonStyles.description} text-xs`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-2 py-0.5 text-[10px]",
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-3`,
        infoRow: `${commonStyles.infoRow} text-sm py-0.5`,
        infoLabel: `${commonStyles.infoLabel} w-[100px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: `${commonStyles.progressContainer} mt-3`,
        progressBar: `${commonStyles.progressBar} h-2.5`,
        progressText: `${commonStyles.progressText} text-sm mt-1`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-xs rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-2 py-1.5 text-white text-xs rounded-lg flex-1 text-center"
      };
    }
    // 2560px (4K)
    else {
      return {
        ...commonStyles,
        card: `${commonStyles.card} p-5`,
        profileSection: `${commonStyles.profileSection} mb-4 pt-7`,
        statusContainer: `${commonStyles.statusContainer} mt-1 mr-3`,
        projectContainer: `${commonStyles.projectContainer}`,
        projectImage: `${commonStyles.projectImage} w-12 h-12`,
        name: `${commonStyles.name} text-lg`,
        starIcon: `${commonStyles.starIcon} text-xl`,
        description: `${commonStyles.description} text-sm`,
        statusBadge: "bg-[#5AB24A] text-white rounded-full px-3 py-1 text-xs",
        infoContainer: `${commonStyles.infoContainer} mt-3 space-y-4`,
        infoRow: `${commonStyles.infoRow} text-base py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[120px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: `${commonStyles.progressContainer} mt-4`,
        progressBar: `${commonStyles.progressBar} h-3`,
        progressText: `${commonStyles.progressText} text-sm mt-1`,
        actionContainer: `${commonStyles.actionContainer} gap-3 mt-4`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-3 py-2 text-sm rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-3 py-2 text-white text-sm rounded-lg flex-1 text-center"
      };
    }
  };

  return getStyles();
};
