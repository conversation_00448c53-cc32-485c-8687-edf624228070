// @ts-ignore
import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { clientProfileShow, clientProfileUpdate } from '../profileclient_api/Clientprofile_api';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { countries } from '@/utils/countries';
import { z } from 'zod';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ProfileUserallDataProps {
  isEditing: boolean;
  onSaveSuccess: () => void;
  onCancel: () => void;
}

interface FormData {
  name: string;
  companyName: string;
  phoneNumber: string;
  country: string;
  stateProvince: string;
  email: string;
  website: string;
  timezone: string;
  address: string;
  postalCode: string;
}

// Zod validation schema
const profileSchema = z.object({
  name: z.string().min(1, "Name is required"),
  companyName: z.string().min(1, "Company name is required"),
  phoneNumber: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .max(15, "Phone number can't be longer than 15 digits")
    .regex(/^\+[0-9]+$/, "Phone number must start with country code"),
  country: z.string().min(1, "Country is required"),
  stateProvince: z.string().min(1, "State/Province is required"),
  email: z.string().email("Invalid email address"),
  website: z.string().url("Invalid URL").or(z.literal('')),
  timezone: z.string().min(1, "Timezone is required"),
  address: z.string().min(1, "Address is required"),
  postalCode: z.string()
    .min(3, "Postal code must be at least 3 characters")
    .regex(/^[0-9]+$/, "Postal code must contain only numbers")
});

const ProfileUserallData = ({ isEditing, onSaveSuccess, onCancel }: ProfileUserallDataProps) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    companyName: '',
    phoneNumber: '',
    country: '',
    stateProvince: '',
    email: '',
    website: '',
    timezone: '',
    address: '',
    postalCode: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);

  const fetchProfileData = async () => {
    try {
      setLoading(true);
      const response = await clientProfileShow();

      setFormData({
        name: response.user?.name ?? '',
        companyName: response.profile?.companyName ?? '',
        phoneNumber: response.profile?.phoneNumber ?? '',
        country: response.profile?.country ?? '',
        stateProvince: response.profile?.stateProvince ?? '',
        email: response.user?.email ?? '',
        website: response.profile?.website ?? '',
        timezone: response.user?.timezone ?? '',
        address: response.profile?.address ?? '',
        postalCode: response.profile?.postalCode ?? ''
      });
    } catch (error: any) {
      console.error('Profile fetch error:', error);
      if (error.response) {
        toast.error(error.response.data?.message || 'Failed to fetch profile data');
      } else if (error.request) {
        toast.error('Network error - please check your connection');
      } else {
        toast.error('Error fetching profile data');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfileData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, field: keyof FormData) => {
    // For phone number, ensure it starts with + and only contains numbers after
    if (field === 'phoneNumber') {
      const value = e.target.value;
      if (value && !/^\+?[0-9]*$/.test(value)) {
        return;
      }
      // Ensure it starts with +
      const formattedValue = value.startsWith('+') ? value : `+${value}`;
      setFormData(prev => ({
        ...prev,
        [field]: formattedValue
      }));
      return;
    }

    // For postal code, only allow numbers
    if (field === 'postalCode' && e.target.value && !/^[0-9]*$/.test(e.target.value)) {
      return;
    }

    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleCountryChange = (value: string) => {
    const selectedCountry = countries.find(c => c.code === value);
    if (!selectedCountry) return;

    setFormData(prev => {
      let phoneNumber = prev.phoneNumber;
      
      // If phone is empty or doesn't start with any country code
      if (!phoneNumber || !phoneNumber.startsWith('+')) {
        phoneNumber = selectedCountry.phoneCode;
      } 
      // If phone has a different country code
      else {
        const currentCountry = countries.find(c => 
          phoneNumber.startsWith(c.phoneCode)
        );
        
        if (!currentCountry || currentCountry.code !== selectedCountry.code) {
          const numberWithoutCode = phoneNumber.replace(/^\+\d+\s?/, '');
          phoneNumber = `${selectedCountry.phoneCode}${numberWithoutCode}`;
        }
      }

      return {
        ...prev,
        country: value,
        phoneNumber
      };
    });
  };

  const validateForm = () => {
    try {
      profileSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          newErrors[err.path[0]] = err.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    try {
      const updateData = {
        companyName: formData.companyName,
        phoneNumber: formData.phoneNumber,
        website: formData.website,
        address: formData.address,
        postalCode: formData.postalCode,
        country: formData.country,
        stateProvince: formData.stateProvince
      };

      await clientProfileUpdate(updateData);
      await fetchProfileData();
      onSaveSuccess();
      // toast.success("Profile updated successfully");
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
    }
  };

  // Common styling classes
  const viewModeStyle = "w-full p-2 rounded-lg bg-[#F9EFEF] text-[#5E5E5E] border-gradient";
  const inputContainerStyle = "w-full rounded-lg p-[1px] border-gradient";
  const inputStyle = "w-full bg-[#F9EFEF] text-[#5E5E5E]";

  if (loading) {
    return <div>Loading...</div>;
  }
  return (
    <div className="w-full mx-auto p-6 bg-white rounded-lg">
      {isEditing && (
        <div className="flex justify-end gap-2 ">
          <Button
            variant="outline"
            onClick={onCancel}
            className='border border-[#FF577F] px-8'
          >
            Cancel
          </Button>
          <Button
            variant={"gradient"}
            className='px-12'
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      )}
      <div className="space-y-6">
        <div className='flex flex-row gap-4 w-full'>
          {/* Name */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Name</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange(e, 'name')}
                  className={inputStyle}
                  disabled // Name might not be editable in profile
                />
                {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.name || 'N/A'}</div>
            )}
          </div>

          {/* Company Name */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Company Name</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.companyName}
                  onChange={(e) => handleInputChange(e, 'companyName')}
                  className={inputStyle}
                />
                {errors.companyName && <p className="text-red-500 text-xs mt-1">{errors.companyName}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.companyName || 'N/A'}</div>
            )}
          </div>
        </div>

       <div className='flex flex-row gap-4 w-full'>
          {/* Phone Number */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Phone Number</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange(e, 'phoneNumber')}
                  className={inputStyle}
                  type="tel"
                />
                {errors.phoneNumber && <p className="text-red-500 text-xs mt-1">{errors.phoneNumber}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.phoneNumber || 'N/A'}</div>
            )}
          </div>

          {/* Country */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Country</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Select
                  value={formData.country}
                  onValueChange={handleCountryChange}
                >
                  <SelectTrigger className={inputStyle}>
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {countries.map((country) => (
                      <SelectItem key={country.code} value={country.code}>
                        {country.name} ({country.phoneCode})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.country && <p className="text-red-500 text-xs mt-1">{errors.country}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>
                {countries.find(c => c.code === formData.country)?.name || formData.country || 'N/A'}
              </div>
            )}
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* State */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">State</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.stateProvince}
                  onChange={(e) => handleInputChange(e, 'stateProvince')}
                  className={inputStyle}
                />
                {errors.stateProvince && <p className="text-red-500 text-xs mt-1">{errors.stateProvince}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.stateProvince || 'N/A'}</div>
            )}
          </div>

          {/* Email */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Email</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.email}
                  onChange={(e) => handleInputChange(e, 'email')}
                  className={inputStyle}
                  disabled // Email might not be editable in profile
                />
                {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.email || 'N/A'}</div>
            )}
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* Website */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Website</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.website}
                  onChange={(e) => handleInputChange(e, 'website')}
                  className={inputStyle}
                />
                {errors.website && <p className="text-red-500 text-xs mt-1">{errors.website}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.website || 'N/A'}</div>
            )}
          </div>

          {/* Time Zone */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Time Zone</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.timezone}
                  onChange={(e) => handleInputChange(e, 'timezone')}
                  className={inputStyle}
                />
                {errors.timezone && <p className="text-red-500 text-xs mt-1">{errors.timezone}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.timezone || 'N/A'}</div>
            )}
          </div>
        </div>

        <div className='flex flex-row gap-4 w-full'>
          {/* Address */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Address</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.address}
                  onChange={(e) => handleInputChange(e, 'address')}
                  className={inputStyle}
                />
                {errors.address && <p className="text-red-500 text-xs mt-1">{errors.address}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.address || 'N/A'}</div>
            )}
          </div>

          {/* Postal Code */}
          <div className="flex flex-col gap-1 items-start w-1/2">
            <label className="text-sm font-medium text-gray-700">Postal Code</label>
            {isEditing ? (
              <div className={inputContainerStyle}>
                <Input
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange(e, 'postalCode')}
                  className={inputStyle}
                />
                {errors.postalCode && <p className="text-red-500 text-xs mt-1">{errors.postalCode}</p>}
              </div>
            ) : (
              <div className={viewModeStyle}>{formData.postalCode || 'N/A'}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileUserallData;