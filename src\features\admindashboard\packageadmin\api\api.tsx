// src/api/api.ts
import { useInfiniteQuery, useQuery, } from "@tanstack/react-query";
import { customAxios } from "@/utils/axio-interceptor";
import { PackageType, PaginationResponse } from "../component/features/table/attendancetype";

export interface FeatureResponse {
  id: string;
  rule: string;
  createdAt: string;
  updatedAt: string;
  packages?: { packageId: string }[]; // Packages associated with the feature
  features?: string; // Optional, if provided by API
  categories?: string[]; // Optional, if provided by API
}

export const fetchPackages = async (): Promise<PackageType[]> => {
  try {
    const res = await customAxios.get("/v1/packages/get");
    return res.data.data.packages;
  } catch (error) {
    console.error("Failed to fetch packages", error);
    throw error;
  }
};

export const createFeature = async (feature: {
  rule: string;
  packageIds: string[];
}): Promise<FeatureResponse> => {
  try {
    const res = await customAxios.post("/v1/packages/create-feature", feature);
    return res.data.data.feature;
  } catch (error) {
    console.error("Failed to create feature", error);
    throw error;
  }
};

export const useGetFeatureDataList = (limit: number = 10) => {
  return useInfiniteQuery<PaginationResponse<FeatureResponse>, Error>({
    queryKey: ["Features", { limit }],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await customAxios.get("/v1/packages/get-package-features", {
        params: {
          page: pageParam,
          limit,
        },
      });

      return {
        data: response.data.data.features,
        totalCount: response.data.data.totalCount,
        totalPages: response.data.data.totalPages,
        currentPage: response.data.data.currentPage,
      };
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.currentPage < lastPage.totalPages) {
        return lastPage.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useGetPackageList = () => {
  return useQuery<PackageType[], Error>({
    queryKey: ["Packages"],
    queryFn: async () => {
      const response = await customAxios.get("/v1/packages/get");
      return response.data.data.packages;
    },
  });
};

export const deleteFeature = async (id: string) => {
  const response = await customAxios.delete(`/v1/packages/delete-feature/${id}`);
  return response.data;
};

export const updateFeature = async (
  featureId: string,
  featureData: { rule: string; packageIds: string[] }
): Promise<FeatureResponse> => {
  try {
    const res = await customAxios.patch(`/v1/packages/update-feature/${featureId}`, featureData);
    return res.data.data.feature;
  } catch (error) {
    console.error("Failed to update feature", error);
    throw error;
  }
};