// src/api/attendancetype.ts
export interface FeaturedType {
  id: string;
  packageId?: string; // Optional, for backward compatibility
  featureId: string;
  rule: string;
  createdAt: string;
  updatedAt: string;
  features: string; // Single string, possibly the rule or description
  categories: string[]; // Possibly package IDs or names
  packageIds: string[]; // Multiple package IDs
  Package?: {
    name: string;
  };
}

export interface PackageType {
  id: string;
  name: string;
  description: string;
  price: number;
  billingType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface PaginationResponse<T> {
  data: T[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}