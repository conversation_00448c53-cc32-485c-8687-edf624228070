import { useState } from "react";
import CustomToast from "@/_components/common/customtoast";
import { useNavigate } from "react-router-dom";
import { useCoordinatorClientList } from "../../api/query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";

export default function Clientscord() {
  const navigate = useNavigate();
  // const [isModalOpen, setIsModalOpen] = useState(false);
  // const [selectedAnnotator, setSelectedAnnotator] =
  //   useState<AnnotatorProps | null>(null);
  const { data, isLoading } = useCoordinatorClientList("client");
  const [showToast, setShowToast] = useState(false);
  // const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // const openModal = (annotator: AnnotatorProps) => {
  //   setSelectedAnnotator(annotator);
  //   setIsModalOpen(true);
  // };

  // const closeModal = () => {
  //   setIsModalOpen(false);
  //   setSelectedAnnotator(null);
  // };

  // const handleSuccess = () => {
  //   setShowToast(true);
  // };


  const annotators = data?.pages.flatMap((page) => page.data) || [];

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  
if (annotators.length === 0) {
  return (
    <div className="text-lg font-medium mt-10 text-gray-500 flex items-center justify-center text-center">
      <span>There are no coordinator available at the moment.</span>
    </div>
  );
}

  return (
    <div className="relative">
      <div className="grid lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5 gap-3 py-2 mx-auto max-w-[98%]">
        {annotators.map((annotator, index) => (
          <div
            key={index}
            className="border border-[#FF577F] lg-only:p-2 xl-only:p-2.5 2xl-only:p-3 rounded-lg shadow-md bg-white"
          >
            <div className="flex flex-row items-center lg-only:gap-2 xl-only:gap-2.5 2xl-only:gap-3">
              <img
                src={getAvatarUrl(annotator.client.name)}
                alt={annotator.client.name}
                className="lg-only:w-10 lg-only:h-10 xl-only:w-11 xl-only:h-11 2xl-only:w-12 2xl-only:h-12 rounded-full"
              />
              <div className="flex flex-col">
                <h3 className="lg-only:text-sm xl-only:text-base 2xl-only:text-lg font-semibold">
                  {annotator.client.name}
                </h3>
              </div>
            </div>
            <div className="lg-only:mt-1.5 xl-only:mt-2 2xl-only:mt-2.5 flex flex-col lg-only:px-2 xl-only:px-3 2xl-only:px-4 lg-only:gap-y-1.5 xl-only:gap-y-2 2xl-only:gap-y-3 lg-only:text-[10px] xl-only:text-[13px] 2xl-only:text-[12px]">
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Annotators:</strong>
                <span>{annotator.client._count.assignmentsAsClient}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Projects:</strong>
                <span>{annotator.client._count.projectsOwned}</span>
              </p>
              <p className="flex justify-between items-center">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Coworkers:</strong>
                <span>{(annotator.client._count as any).coWorkers}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Joined:</strong>
                <span>{new Date(annotator.client.createdAt).toLocaleDateString()}</span>
              </p>
              <p className="flex justify-between">
                <strong className="text-[#5B5B5B] lg-only:w-[80px] xl-only:w-[90px] 2xl-only:w-[100px] inline-block">Expiring on:</strong>
                <span>
                  {annotator.client.Subscription?.length > 0
                    ? new Date(annotator.client.Subscription[0].endDate).toLocaleDateString()
                    : "No active subscription"}
                </span>
              </p>
            </div>
            <div className="flex flex-row justify-center lg-only:mt-1.5 xl-only:mt-2 2xl-only:mt-2.5 lg-only:px-1.5 xl-only:px-2 2xl-only:px-2.5">
              <button
                onClick={() => navigate(`/coordinator/coordinatorprojects?id=${annotator.client.id}&name=${encodeURIComponent(annotator.client.name)}`)}
                className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] lg-only:px-4 lg-only:py-1.5 xl-only:px-6 xl-only:py-1.5 2xl-only:px-8 2xl-only:py-2 text-white lg-only:text-[10px] xl-only:text-[11px] 2xl-only:text-[12px] rounded-lg w-full"
              >
                All Projects
              </button>
            </div>
          </div>
        ))}

        {/* Modal Open Hoga Yaha */}
      </div>
      {/* ✅ Toast */}
      {showToast && (
        <div className="fixed top-5 right-5 z-[100]">
          <CustomToast
            title="Shift Change Requested"
            message="Your shift change request was submitted successfully."
            type="success"
            duration={4000}
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
