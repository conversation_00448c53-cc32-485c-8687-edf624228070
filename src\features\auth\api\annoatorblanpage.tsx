import { customAxios } from "@/utils/axio-interceptor";

export const AnnonatorblankPageApi = async () => {
  try {
    const response = await customAxios.get('/v1/dashboard/client-annotators');
    
    if (response.data && typeof response.data.count !== 'undefined') {
      return {
        count: response.data.count,
        data: response.data.data
      };
    }
    throw new Error('Invalid response format');
  } catch (error) {
    console.error('Error fetching annotators:', error);
    throw error;
  }
};