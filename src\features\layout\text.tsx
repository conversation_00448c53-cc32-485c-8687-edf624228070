// import  { useState } from "react";
// import { Link, NavLink, Outlet, useLocation } from "react-router-dom";
// import { ArrowDropDown, ArrowRight } from "@mui/icons-material";
// import TopNavbar from "./top-navbar";
// import { sidebarItems, settingSection, supportSection } from "./sidebar-data";
// import LogoutBar from "@/_components/common/LogoutBar";

// const DashboardPageRoutes = () => {
//   const location = useLocation();
//   const [open, setOpen] = useState<number | null>(null);
//   const [navbar, setNavbar] = useState(false);
//   const [activeIndex, setActiveIndex] = useState<string | null>(location.pathname); // ✅ Active item state

//   const handleNavbar = () => setNavbar(!navbar);
//   const handleOpen = (index: number) => setOpen(open === index ? null : index);

//   return (
//     <div className="w-full flex bg-gray-100 ">
//       <div className="w-full">
//         <TopNavbar handleNavbar={handleNavbar} navbar={navbar} />
//         <div className="h-[calc(100vh-70px)] flex ">
//           {/* Sidebar */}
//           <div className={`h-full bg-white shadow-md transition-all ${navbar ? "w-[250px]" : "w-[250px]"}`}>
//             <div className="p-5 overflow-y-auto flex flex-col gap-6 h-full justify-between">
//               <div>
//                 {[...sidebarItems, settingSection, supportSection].map((section, sectionIndex) => (
//                   <div key={sectionIndex} className="p-2 rounded-md">
//                     <h3 className="text-gray-500 uppercase text-[10px] font-semibold mb-2">{section.section}</h3>
//                     {section.items.map((item, index) => {
//                       const isActive = activeIndex === item.path; // ✅ Track active index
//                       const hasChildren = item.children && item.children.length > 0;

//                       return (
//                         <div key={index} className="text-[#757575] font-semibold space-y-2">
//                           {item.path ? (
//                             <Link
//                               to={item.path}
//                               onClick={() => setActiveIndex(item.path!)} // ✅ Set active on click
//                               className={`flex items-center gap-4 p-2 rounded-md cursor-pointer ${
//                                 isActive ? "bg-[#f6f6f6] text-[#FF577F]" : "hover:bg-[#f6f6f6]"
//                               }`}
//                             >
//                               <div>{item.icon}</div>
//                               <span className="text-[15px]">{item.name}</span>
//                             </Link>
//                           ) : (
//                             <button
//                               className="flex items-center gap-4 p-2 rounded-md cursor-pointer w-full text-left text-[#757575] hover:bg-gray-200"
//                               onClick={() => handleOpen(index)}
//                             >
//                               <div>{item.icon}</div>
//                               <span className="text-[15px]">{item.name}</span>
//                               {hasChildren && <div className="ml-auto">{open === index ? <ArrowDropDown /> : <ArrowRight />}</div>}
//                             </button>
//                           )}

//                           {open === index && hasChildren && (
//                             <div className="relative ml-4 pl-4 text-[13px]">
//                               <div className="h-[calc(100%-23px)] w-2 absolute -left-[5px] rounded border-l-4 border-[#F6F6F6]"></div>
//                               {item.children?.map((subItem, subIndex) => (
//                                 <NavLink
//                                   key={subIndex}
//                                   to={subItem.path!}
//                                   onClick={() => setActiveIndex(subItem.path!)} // ✅ Set active for sub-items
//                                   className={`relative block py-3 pl-6 text-gray-600  hover:rounded-md hover:bg-[#f6f6f6] hover:text-[#FF577F] text-xm ${
//                                     activeIndex === subItem.path ? "text-[#FF577F] font-semibold" : ""
//                                   }`}
//                                 >
//                                   {subItem.name}
//                                   <span className="absolute -left-[20px] top-3 w-5 h-3 border-b-4 border-l-2 rounded-l-2xl border-[#F6F6F6] rounded-tl-md"></span>
//                                 </NavLink>
//                               ))}
//                             </div>
//                           )}
//                         </div>
//                       );
//                     })}
//                   </div>
//                 ))}
//               </div>
//               <div className="w-full rounded-md">
//                 <LogoutBar />
//               </div>
//             </div>
//           </div>

//           {/* Content Area */}
//           <div className="flex-1 overflow-y-auto p-4 bg-white">
//             <Outlet />
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default DashboardPageRoutes;