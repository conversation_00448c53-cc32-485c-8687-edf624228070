// import { useEffect } from "react";
// import { useNavigate } from "react-router-dom";

type CoworkerInviteRedirectProps = {
  children: React.ReactNode;
};

/**
 * A component that allows both authenticated and unauthenticated users
 * to access the coworker invite acceptance page.
 */
const CoworkerInviteRedirect = ({ children }: CoworkerInviteRedirectProps) => {
  return <>{children}</>;
};

export default CoworkerInviteRedirect;
