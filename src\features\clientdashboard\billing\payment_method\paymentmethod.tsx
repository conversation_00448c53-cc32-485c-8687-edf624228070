// @ts-ignore
import React from 'react';
import { BackButton } from '@/_components/common';
import { Button } from '@/components/ui/button';
import imgcard from "@/assets/icons/svgcardicon.svg"
import { FaTrash } from 'react-icons/fa';

const PaymentMethod = () => {
  return (
    <div className="w-full flex flex-col mx-auto p-6 bg-white">
      <div className='flex flex-row items-center mb-6'>
        <BackButton/>
        <h1 className="text-[28px] font-semibold ">Payment Methods</h1>
      </div>
      <div className=' flex flex-row justify-end'>
        <Button variant={"ghost"}
        className='bg-[#D53148] text-white hover:bg-[#ad2839] hover:text-white'>
            Add New Payment Method
        </Button>
      </div>
      
      <h2 className="text-base font-semibold text-gray-700 mb-4">Credit & Debit Cards</h2>
      
      {/* Default Card */}
      <div className="border bg-[#F9EFEF66] rounded-lg px-4 py-6 mb-3 flex justify-between items-start">
        <div className="flex items-start space-x-3">
          <div className=" rounded">
            <img src={imgcard} alt="imgcxard" className='w-12 h-10'/>
          </div>
          <div>
            
            <div className="text-gray-600 text-sm">Visa ending in 7856</div>
            <div className="text-gray-600 text-sm">Expiring on Dec 3rd, 2025</div>
          </div>
        </div>
       
         <div className='flex flex-col gap-3 items-center justify-center'>
           <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Default</span>
         <div className='text-[#7e7d7d] hover:text-red-400 cursor-pointer'>
            <FaTrash/>
        </div>
      </div>
      </div>
      
      {/* Non-default Card */}
      <div className="border bg-[#F9EFEF66] rounded-lg px-4 py-6 mb-3 flex justify-between items-start">
        <div className="flex items-start space-x-3">
          <div className=" rounded">
            <img src={imgcard} alt="imgcxard" className='w-12 h-10'/>
          </div>
          <div>
            
            <div className="text-gray-600 text-sm">Visa ending in 7856</div>
            <div className="text-gray-600 text-sm">Expiring on Dec 3rd, 2025</div>
          </div>
        </div>
       
          <div className='flex flex-row gap-3 items-center justify-center'>
           <button className="text-blue-600 text-sm font-medium hover:text-blue-800">Make it Default</button>
         <div className='text-[#7e7d7d] hover:text-red-400 cursor-pointer'>
            <FaTrash/>
        </div>
       </div>
      </div>
      
      {/* Expired Card */}
      <div className="bg-[#F9EFEF66] border rounded-lg px-4 py-6 mb-3 flex justify-between items-start opacity-60">
        <div className="flex items-start space-x-3">
         <div className=" rounded">
            <img src={imgcard} alt="imgcxard" className='w-12 h-10'/>
          </div>
          <div>
            
            <div className="text-gray-600 text-sm">Visa ending in 7856</div>
            <div className="text-gray-600 text-sm">Expiring on Dec 3rd, 2025</div>
          </div>
        </div>
       <div className='flex flex-row gap-3'>
         <span className="text-red-500 text-sm font-medium">Expired</span>
         <div className='text-[#7e7d7d] hover:text-red-400 cursor-pointer'>
            <FaTrash/>
        </div>
       </div>
      </div>
      
    
      
      {/* Add PayPal Button */}
      <div className='w-full flex flex-row justify-end p-4'>
        <Button className=" border-2 bg-[#D53148] hover:bg-[#ac2537] border-gray-300 rounded-lg p-4 px-8 text-white font-medium flex items-center justify-center ">
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
        </svg>
        <span>Add PayPal</span>
      </Button>
        </div>

        {/* PayPal */}
      <div className="bg-[#F9EFEF66] border mt-3 rounded-lg px-4 py-6  mb-3 flex flex-row justify-between items-center">
        <div className="flex items-start space-x-3">
         <div className=" rounded">
            <img src={imgcard} alt="imgcxard" className='w-12 h-10'/>
          </div>
          <div>
           
            <div className="text-gray-600 text-sm">Vk620914@paypal</div>
            <div className="text-gray-600 text-sm">Expiring on Dec 3rd, 2025</div>
          </div>
        </div>

        <div className='text-[#7e7d7d]'>
            <FaTrash/>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethod;