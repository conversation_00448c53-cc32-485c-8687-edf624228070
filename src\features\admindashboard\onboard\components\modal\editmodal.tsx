import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import UpdateOnboardForm from "./updateForm";

interface EditModalProps {
  userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    domain?: string;
    role?: string;
    packageId?: string;
    password?: string;
  };
}

const EditModal: React.FC<EditModalProps> = ({ userData }: EditModalProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="w-full justify-start">
          Edit
        </Button> 
      </DialogTrigger>
      <DialogContent className=" max-w-2xl w-full">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
        </DialogHeader>
        <UpdateOnboardForm userData={userData} />
      </DialogContent>
    </Dialog>
  );
};

export default EditModal;
