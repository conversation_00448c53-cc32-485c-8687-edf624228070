// // import { useState } from "react";
// import { usePagination } from "@/components/globalfiles/usePagination";
// import { BackButton } from "@/_components/common";
// import { DataTable } from "./components/data-table";
// import { columns } from "./components/column";
// import { transactiondummyData } from "../billing/transaction/transdummydata";

// const SupportList = () => {
//   const { onPaginationChange, pagination } = usePagination();
//   // const [data] = useState<any>(null);

//   return (
//     <div className="p-3 bg-white h-full space-y-6">
//       <div className="flex flex-col justify-center">
//         <div className="flex flex-row items-center">
//           <BackButton />
//           <p className="font-bold font-poppins text-[34px] text-[#F97B9A]">
//             Supports
//           </p>
//         </div>
//       </div>
//       <div className="">
//         <DataTable
//           title="Forms"
//           columns={columns}
//           data={transactiondummyData}
//           onPaginationChange={onPaginationChange}
//           loading={false}
//           pagination={pagination}
//           pageCount={1}
//         />
//       </div>
//     </div>
//   );
// };

// export default SupportList;
