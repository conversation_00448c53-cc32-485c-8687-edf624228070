"use client";
import { ColumnDef } from "@tanstack/react-table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AttendanceType } from "./attendancetype";
import { FaEdit } from "react-icons/fa";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { Dialog, DialogClose, Di<PERSON>Footer, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog";
import { DialogContent, DialogTrigger } from "@/components/ui/dialog";
import EditPackage from "../../modal/editpackage.tsx";
import { deletePackageApi } from "../api_package/api_package.tsx";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import showToast from "@/_components/common/customtoast";
import { useRef } from "react";

// Define a package type that matches your API response
interface PackageType {
  id: string;
  name: string;
  price: number;
  description?: string;
  isActive: boolean;
  billingType?: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string | null;
}


export const useAdminColumns = (): ColumnDef<AttendanceType>[] => {
  const queryClient = useQueryClient();
  const dialogCloseRef = useRef<HTMLButtonElement>(null);

  // Delete package mutation
  const { mutate: deletePackageMutation, isPending } = useMutation({
    mutationFn: deletePackageApi,
    onSuccess: async () => {
      // Close the dialog first
      if (dialogCloseRef.current) {
        dialogCloseRef.current.click();
      }

      // Show success toast
      showToast({
        title: "Success",
        message: "Package deleted successfully",
        type: "success",
        onClose: () => {},
      });

      // Force immediate refetch with refetchQueries instead of just invalidating
      await queryClient.refetchQueries({
        queryKey: ["packageList"],
        exact: true,
        type: 'active'
      });
    },
    onError: (error) => {
      console.error("Delete error:", error);
      showToast({
        title: "Error",
        message: "Failed to delete package",
        type: "error",
        onClose: () => {},
      });
    },
  });

  return [
    {
      accessorKey: "name",
      header: () => (
        <div
          className="w-[350px] text-[14px] font-medium cursor-pointer"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
        </div>
      ),
      cell: ({ row }) => <div className="text-[14px] font-normal">{row.getValue("name")}</div>,
    },
    {
      accessorKey: "price",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Original Price
        </Button>
      ),
      cell: ({ row }) => <div className="pl-6 text-[14px] font-normal">{row.getValue("price")}</div>,
    },
    {
      accessorKey: "discount",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Discount Price
        </Button>
      ),
      cell: ({ row }) => <div className="pl-8 text-[14px] font-normal">{row.getValue("discount")}</div>,
    },
    {
      accessorKey: "isActive",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const isActive = row.getValue("isActive");
        return (
          <div
            className={`flex items-center justify-center px-6 py-1 rounded-3xl border text-white w-fittext-[14px] font-medium text-center
              ${isActive ? "bg-[#5AB24A]" : "bg-[#FF4D4F]"}`}
          >
            {isActive ? "Active" : "Inactive"}
          </div>
        );
      },
    },




    {
      accessorKey: "Acitons",
      header: () => <div className="pl-6 text-[14px] font-medium">Actions</div>,
      cell: ({ row }) => {
        // Explicitly cast row.original to PackageType
        const packageData = row.original as unknown as PackageType;

        return (
          <div className="flex flex-row gap-2 pl-3">
            <div className="flex">{row.getValue("Acitons")}</div>
            <div className="px-2 text-xl cursor-pointer hover:text-[#398bc2]">

              <Dialog>
                <DialogTrigger asChild>
                  <FaEdit />
                </DialogTrigger>
                <DialogContent>
                  <EditPackage
                    data={{
                      id: packageData.id,
                      name: packageData.name,
                      price: packageData.price,
                      description: packageData.description || "",
                      isActive: packageData.isActive,
                      billingType: packageData.billingType || "MONTHLY",
                    }}
                  />
                </DialogContent>
              </Dialog>

            </div>
          
            <div className=" px-2 text-xl text-[#ff3c3c] hover:text-[#eb6767] cursor-pointer">
              <Dialog>
                <DialogTrigger asChild>
                  <RiDeleteBin6Fill />
                </DialogTrigger>
                <DialogContent className="px-[4rem] py-14">
                  <DialogHeader>
                    <DialogTitle className="text-[24px]">Do you want to delete this Package?</DialogTitle>
                  </DialogHeader>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button
                        type="button"
                        className="border-gradient bg-white hover:bg-[#faf5f5] px-14 py-6 text-black"
                      >
                        Cancel
                      </Button>
                    </DialogClose>
                    <Button
                      variant="gradient"
                      className="px-[4rem] py-6"
                      disabled={isPending}
                      onClick={async () => {
                        const packageData = row.original as unknown as PackageType;
                        console.log("Deleting package with ID:", packageData.id);

                        try {
                          // Optimistically update the UI by removing the deleted item
                          // This makes the UI feel more responsive while the actual deletion happens
                          const previousData = queryClient.getQueryData<any>(["packageList"]);

                          if (previousData && previousData.data && previousData.data.packages) {
                            // Create a filtered copy without the deleted item
                            const updatedPackages = previousData.data.packages.filter(
                              (pkg: any) => pkg.id !== packageData.id
                            );

                            // Update the cache immediately
                            queryClient.setQueryData(["packageList"], {
                              ...previousData,
                              data: {
                                ...previousData.data,
                                packages: updatedPackages
                              }
                            });
                          }

                          // Perform the actual deletion
                          deletePackageMutation(packageData.id);
                        } catch (error) {
                          console.error("Error during optimistic update:", error);
                          // If optimistic update fails, just do the regular deletion
                          deletePackageMutation(packageData.id);
                        }
                      }}
                    >
                      {isPending ? "Processing..." : "Delete"}
                    </Button>
                    {/* Hidden DialogClose button that will be triggered programmatically */}
                    <DialogClose ref={dialogCloseRef} className="hidden" />
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        );
      },
    },


  ];
};
