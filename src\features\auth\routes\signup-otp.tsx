// "use client";

// import { z } from "zod";
// import { useEffect, useState } from "react";
// import { useForm, FormProvider, Controller } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { ClientSignupOtp } from "@/features/auth/api/client-api"; // Importing the OTP verification function

// import {
//   InputOTP,
//   InputOTPGroup,
//   InputOTPSlot,
// } from "@/components/ui/input-otp";
// import { Button } from "@/components/ui/button";
// import {
//   FormControl,
//   FormDescription,
//   FormLabel,
//   FormMessage,
//   FormItem,
// } from "@/components/ui/form";

// import { MoveRight } from "lucide-react";
// import CustomToast from "@/_components/common/customtoast";
// import { useLocation, useNavigate } from "react-router-dom";
// // 🛡️ Zod schema
// const FormSchema = z.object({
//   otp: z
//     .string()
//     .min(4, { message: "Your one-time password must be 4 characters." }),
//   email: z.string().email({
//     message: "Invalid email address.",
//   }),
// });

// type FormValues = z.infer<typeof FormSchema>;

// export function SignupOTPForm() {
//   const navigate = useNavigate();
//   const location = useLocation();
//   const [showToast, setShowToast] = useState(false);
//   const [toastMessage, setToastMessage] = useState("");
//   const [countdown, setCountdown] = useState(30);

//   const form = useForm<FormValues>({
//     resolver: zodResolver(FormSchema),
//     defaultValues: {
//       otp: "",
//       email: location.state?.email || "", // ✅ initialize email
//     },
//   });

//   const {
//     control,
//     handleSubmit,
//     formState: { isSubmitting },
//   } = form;

//   const onSubmit = async (data: FormValues) => {
//     try {
//       await ClientSignupOtp(data.email, data.otp); // No need to store the response
//       setToastMessage("OTP Verified Successfully!");
//       setShowToast(true);

//       setTimeout(() => {
//         navigate(`/auth/questionaire`, {
//           state: {
//             email: data.email,
//           },
//         });
//       }, 2000);
//     } catch (error) {
//       setToastMessage("OTP Verification Failed.");
//       setShowToast(true);
//     }
//   };

//   useEffect(() => {
//     let timer: NodeJS.Timeout;
//     if (countdown > 0) {
//       timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
//     }
//     return () => clearTimeout(timer);
//   }, [countdown]);

//   return (
//     <div className="flex flex-col h-full justify-center items-center">
//       <FormProvider {...form}>
//         <div className="flex flex-col items-center gap-4">
//           <Controller
//             name="otp"
//             control={control}
//             render={({ field, fieldState }) => (
//               <div className="space-y-6">
//                 <FormItem>
//                   <FormLabel className="text-[28px] text-[#282828]">
//                     Enter One Time Password...
//                   </FormLabel>
//                   <FormDescription className="text-[14px] text-[#757575]">
//                     Enter the 4-digit code sent to your email.
//                   </FormDescription>
//                   <FormControl>
//                     <InputOTP
//                       maxLength={4}
//                       value={field.value}
//                       onChange={field.onChange}
//                     >
//                       <InputOTPGroup className="flex flex-row gap-2 outline-none">
//                         {[0, 1, 2, 3].map((index) => (
//                           <InputOTPSlot
//                             key={index}
//                             index={index}
//                             className="border-gradient rounded-lg"
//                           />
//                         ))}
//                       </InputOTPGroup>
//                     </InputOTP>
//                   </FormControl>
//                   {fieldState.error && (
//                     <FormMessage>{fieldState.error.message}</FormMessage>
//                   )}
//                 </FormItem>

//                 <Button
//                   type="button"
//                   onClick={handleSubmit(onSubmit)}
//                   className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-10 py-6 text-[16px]"
//                   disabled={isSubmitting}
//                 >
//                   Verify
//                   <MoveRight className="ml-1 w-4 h-4" />
//                 </Button>

//                 <div className="text-sm text-gray-500 pt-2">
//                   {countdown > 0 ? (
//                     <>Resend OTP in {countdown}s</>
//                   ) : (
//                     <button
//                       className="text-blue-500 underline"
//                       onClick={() => setCountdown(30)}
//                     >
//                       Resend OTP
//                     </button>
//                   )}
//                 </div>
//               </div>
//             )}
//           />
//         </div>
//       </FormProvider>
//       {showToast && (
//         <div className="fixed bottom-4 right-4 z-50">
//           <CustomToast
//             title="OTP Verification"
//             message={toastMessage}
//             onClose={() => setShowToast(false)}
//           />
//         </div>
//       )}
//     </div>
//   );
// }

// components/OTPModal.tsx
"use client";

import { z } from "zod";
import { useEffect, useState } from "react";
import { useForm, FormProvider, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormLabel,
  FormMessage,
  FormItem,
} from "@/components/ui/form";
import { MoveRight } from "lucide-react";
import CustomToast from "@/_components/common/customtoast";
import {
  ClientResendOtp,
  ClientSignupOtp,
} from "@/features/auth/api/client-api";

const FormSchema = z.object({
  otp: z
    .string()
    .min(4, { message: "Your one-time password must be 4 characters." }),
  email: z.string().email({
    message: "Invalid email address.",
  }),
});

type FormValues = z.infer<typeof FormSchema>;

interface OTPModalProps {
  email: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function OTPModal({ email, isOpen, onClose, onSuccess }: OTPModalProps) {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [countdown, setCountdown] = useState(30);
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    console.log("OTPModal mounted, onSuccess is:", typeof onSuccess);
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      otp: "",
      email: email,
    },
  });

  const {
    control,
    handleSubmit,
    formState: { isSubmitting },
  } = form;

  const onSubmit = async (data: FormValues) => {
    try {
      await ClientSignupOtp(data.email, data.otp);
      setToastMessage("OTP Verified Successfully!");
      setShowToast(true);

      console.log("About to call onSuccess, type:", typeof onSuccess);
      if (typeof onSuccess === "function") {
        console.log("Calling onSuccess function");
        onSuccess();
      } else {
        console.error("onSuccess is not a function or is undefined");
      }

      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      setToastMessage("OTP Verification Failed.");
      setShowToast(true);
    }
  };

  const handleResendOtp = async () => {
    if (countdown > 0) return;

    setIsResending(true);
    try {
      await ClientResendOtp(email);
      setToastMessage("OTP resent successfully!");
      setShowToast(true);
      setCountdown(30);
    } catch (error) {
      console.error("Failed to resend OTP:", error);
      setToastMessage("Failed to resend OTP. Please try again.");
      setShowToast(true);
    } finally {
      setIsResending(false);
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-8 rounded-lg max-w-md w-full">
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col items-center gap-4">
              <Controller
                name="otp"
                control={control}
                render={({ field, fieldState }) => (
                  <div className="space-y-6 w-full">
                    <FormItem>
                      <FormLabel className="text-[28px] text-[#282828]">
                        Enter One Time Password...
                      </FormLabel>
                      <FormDescription className="text-[14px] text-[#757575]">
                        Enter the 4-digit code sent to {email}
                      </FormDescription>
                      <FormControl>
                        <InputOTP
                          maxLength={4}
                          value={field.value}
                          onChange={field.onChange}
                          onComplete={() => handleSubmit(onSubmit)()}
                          className="flex flex-row items-center justify-between"
                        >
                          <InputOTPGroup className="w-full flex flex-row justify-between items-center px-4  outline-none">
                            {[0, 1, 2, 3].map((index) => (
                              <InputOTPSlot
                                key={index}
                                index={index}
                                className="border-gradient rounded-lg"
                              />
                            ))}
                          </InputOTPGroup>
                        </InputOTP>
                      </FormControl>
                      {fieldState.error && (
                        <FormMessage>{fieldState.error.message}</FormMessage>
                      )}
                    </FormItem>

                    <div className="flex gap-4">
                      <Button
                        type="submit"
                        className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white px-10 py-6 text-[16px] flex-1"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? "Verifying..." : "Verify"}
                        <MoveRight className="ml-1 w-4 h-4" />
                      </Button>

                      <Button
                        type="button"
                        onClick={onClose}
                        className="bg-gray-500 text-white px-10 py-6 text-[16px] flex-1"
                      >
                        Close
                      </Button>
                    </div>

                    <div className="text-sm text-gray-500 pt-2">
                      {countdown > 0 ? (
                        <>Resend OTP in {countdown}s</>
                      ) : (
                        <button
                          type="button"
                          className="text-blue-500 underline"
                          onClick={handleResendOtp}
                          disabled={isResending}
                        >
                          {isResending ? "Sending..." : "Resend OTP"}
                        </button>
                      )}
                    </div>
                  </div>
                )}
              />
            </div>
          </form>
        </FormProvider>
        {showToast && (
          <div className="fixed bottom-4 right-4 z-50">
            <CustomToast
              title="OTP Verification"
              message={toastMessage}
              onClose={() => setShowToast(false)}
            />
          </div>
        )}
      </div>
    </div>
  );
}
