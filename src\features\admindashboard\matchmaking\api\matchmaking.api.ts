import { PaginatedResponse } from "@/types/generics";
import { ClientData } from "@/types/matchmaking.types";
import { Annota<PERSON> } from "@/types/onboarding.types";
import { customAxios } from "@/utils/axio-interceptor";
import { QueryFunctionContext } from "@tanstack/react-query";

export const matchMakingList = async ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}): Promise<PaginatedResponse<Annotator>> => {
  const response = await customAxios.get("/v1/matchmaking/get", {
    params: {
      page,
      limit,
    },
  });
  return response.data.data;
};

  export const getMatchMakingList = async (
    context: QueryFunctionContext
  ): Promise<PaginatedResponse<ClientData>> => {
    const page = (context.pageParam ?? 1) as number;

    const response = await customAxios.get("/v1/matchmaking/get", {
      params: { page },
    });

    return response.data.data;
  };

export const getAnnotatorList = async (
  context: QueryFunctionContext
): Promise<PaginatedResponse<Annotator>> => {
  const page = (context.pageParam ?? 1) as number;

  try {
    // Use the endpoint that returns all annotators
    const response = await customAxios.get("/v1/annotator/get-all-annotators", {
      params: { page },
    });

    return response.data.data;
  } catch (error) {
    console.error("Error fetching annotator list:", error);
    throw error;
  }
};

export const getCoordinatorList = async (
  context: QueryFunctionContext
): Promise<PaginatedResponse<Annotator>> => {
  const page = (context.pageParam ?? 1) as number;

  try {
    // Use the endpoint that returns all coordinators
    const response = await customAxios.get("/v1/annotator/get-all-coordinator", {
      params: { page },
    });

    return response.data.data;
  } catch (error) {
    console.error("Error fetching coordinator list:", error);
    throw error;
  }
};

export const assignAnnotatorCoordinator = async (data: {
  clientId: string;
  developerId: string;
  coordinatorId: string;
  availableFrom: string | null;
  availableTo: string | null;
  packageId: string | null | undefined;
}) => {
  const response = await customAxios.patch(
    `/v1/matchmaking/assign/annotator/${data.clientId}`,
    {
      developerId: data.developerId,
      coordinatorId: data.coordinatorId,
      availableFrom: data.availableFrom,
      availableTo: data.availableTo,
      packageId: data.packageId,
    }
  );

  return response.data;
};
