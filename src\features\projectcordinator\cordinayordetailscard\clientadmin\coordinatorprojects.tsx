import { DataTable } from "@/components/globalfiles/data.table";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate, useLocation } from "react-router-dom";
import { useCoordinatorColumns } from "./coordinatorcolumn";
import { useState, useEffect } from "react";
import { useCoordinatorClientProjects } from "../../api/useCoordinatorClientProjects";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
// import { NoData } from "@/_components/common";

const CoordinatorProjects = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [clientName, setClientName] = useState<string>("Client");

  // Get client ID from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const clientId = queryParams.get('id');
  const clientNameParam = queryParams.get('name');

  // Set client name if available in URL params
  useEffect(() => {
    if (clientNameParam) {
      setClientName(clientNameParam);
    }
  }, [clientNameParam]);

  // Get the columns configuration
  const columns = useCoordinatorColumns();

  // Fetch projects data using React Query
  const { data, isLoading, error } = useCoordinatorClientProjects(clientId || "");

  // Process the data
  let projects: any[] = [];

  if (data) {
    console.log("Client projects response:", data);

    // Check different possible response structures
    if (data?.data?.data && Array.isArray(data.data.data)) {
      console.log("Found data in data.data.data");
      projects = data.data.data;
    } else if (data?.data && Array.isArray(data.data)) {
      console.log("Found data in data.data");
      projects = data.data;
    } else if (Array.isArray(data)) {
      console.log("Found data in data");
      projects = data;
    } else {
      console.log("No projects data found in response");
    }
  }

  // If client ID is missing, show error
  if (!clientId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>Missing client ID. Please select a client first.</p>
          <button
            onClick={() => navigate('/coordinator/projectdetails/clients')}
            className="mt-4 px-4 py-2 bg-[#FF577F] text-white rounded-md hover:bg-[#ff3c6a]"
          >
            Go to Clients
          </button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  // Don't show error state, just treat it as empty data
  // This ensures the table headers are always shown
  if (error) {
    console.error("Error fetching client projects:", error);
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h1 className="text-[#282828] text-[24px]">{clientName}'s Projects</h1>
      </div>

      <DataTable
        title="Projects"
        columns={columns}
        data={projects.length === 0 ? [{}] : projects}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default CoordinatorProjects;
