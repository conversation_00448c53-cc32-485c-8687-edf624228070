import * as React from "react";

// Define screen size breakpoints
const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1023,
  LAPTOP_SM: 1024,
  LAPTOP_MD: 1440,
  LAPTOP_LG: 2560, // 4K
};

type ScreenSize = 
  | "mobile" 
  | "tablet" 
  | "laptop-sm" 
  | "laptop-md" 
  | "laptop-lg";

/**
 * Hook to detect current screen size based on defined breakpoints
 * @returns Current screen size category and boolean flags for each breakpoint
 */
export function useResponsive() {
  const [screenSize, setScreenSize] = React.useState<ScreenSize | undefined>(undefined);
  const [width, setWidth] = React.useState<number>(0);

  React.useEffect(() => {
    // Handler to call on window resize
    const handleResize = () => {
      const currentWidth = window.innerWidth;
      setWidth(currentWidth);
      
      // Determine screen size category
      if (currentWidth < BREAKPOINTS.MOBILE) {
        setScreenSize("mobile");
      } else if (currentWidth < BREAKPOINTS.TABLET) {
        setScreenSize("tablet");
      } else if (currentWidth < BREAKPOINTS.LAPTOP_MD) {
        setScreenSize("laptop-sm"); // 1024-1439
      } else if (currentWidth < BREAKPOINTS.LAPTOP_LG) {
        setScreenSize("laptop-md"); // 1440-2559
      } else {
        setScreenSize("laptop-lg"); // 2560+
      }
    };

    // Initial call
    handleResize();
    
    // Set up event listener
    window.addEventListener("resize", handleResize);
    
    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return {
    screenSize,
    width,
    isMobile: screenSize === "mobile",
    isTablet: screenSize === "tablet",
    isLaptopSm: screenSize === "laptop-sm", // 1024px
    isLaptopMd: screenSize === "laptop-md", // 1440px
    isLaptopLg: screenSize === "laptop-lg", // 2560px/4K
    isDesktop: screenSize === "laptop-sm" || screenSize === "laptop-md" || screenSize === "laptop-lg",
  };
}

/**
 * Component to conditionally render content based on screen size
 */
interface ResponsiveProps {
  children: React.ReactNode;
  showOnMobile?: boolean;
  showOnTablet?: boolean;
  showOnLaptopSm?: boolean; // 1024px
  showOnLaptopMd?: boolean; // 1440px
  showOnLaptopLg?: boolean; // 2560px/4K
}

export const Responsive: React.FC<ResponsiveProps> = ({
  children,
  showOnMobile = false,
  showOnTablet = false,
  showOnLaptopSm = true,
  showOnLaptopMd = true,
  showOnLaptopLg = true,
}) => {
  const { screenSize } = useResponsive();

  if (!screenSize) return null;

  // Determine if we should show the content based on current screen size
  const shouldShow =
    (screenSize === "mobile" && showOnMobile) ||
    (screenSize === "tablet" && showOnTablet) ||
    (screenSize === "laptop-sm" && showOnLaptopSm) ||
    (screenSize === "laptop-md" && showOnLaptopMd) ||
    (screenSize === "laptop-lg" && showOnLaptopLg);

  return shouldShow ? <>{children}</> : null;
};
