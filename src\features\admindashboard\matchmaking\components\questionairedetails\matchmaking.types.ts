// @/types/matchmaking.types.ts
export interface ClientData {
  id: string;
  name: string;
}

export interface QuestionnaireData {
  id: string;
  clientId: string;
  packageId: string;
  availableFrom: string;
  availableTo: string;
  timezone: string;
  industry: string;
  category: string;
  startOn: string;
  description: string;
  assignedAnnotatorId: string | null;
  subscriptionId: string;
  createdAt: string;
}

export interface PackageData {
  id: string;
  name: string;
  description: string;
  price: number;
  billingType: string;
  isActive: boolean;
  currency: string;
  discount: number;
  product_id: string;
  business_id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  zohoProductId: string | null;
}