import { customAxios } from "@/utils/axio-interceptor";

// Fetch questionnaire details by subscription ID
export const QuestionireShow_api = (id: string) =>
  customAxios.get(`/v1/matchmaking/details/${id}`)
    .then((response) => response.data)
    .catch((error) => {
      console.error("Error fetching questionnaire data:", error);
      throw error;
    });

// Fetch all packages
export const GetAllPackages_api = () =>
  customAxios.get(`/v1/packages/get`)
    .then((response) => response.data)
    .catch((error) => {
      console.error("Error fetching packages data:", error);
      throw error;
    });