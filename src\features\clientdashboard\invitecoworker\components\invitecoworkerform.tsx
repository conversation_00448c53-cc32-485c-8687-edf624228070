// InviteCoworkerForm.tsx
// import React from 'react';
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { useInviteCoWorkerMutation } from "../api/mutation";
import { useEffect } from "react";

const inviteSchema = z.object({
  email: z.string().email("Invalid email address"),
  notify: z.boolean(),
  permission: z.enum(["EDIT", "VIEW"]),
});

type InviteFormData = z.infer<typeof inviteSchema>;

const InviteCoworkerForm = () => {
  const { mutate: inviteCoWorker, isPending, isSuccess } = useInviteCoWorkerMutation();
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
    setValue,
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: "",
      notify: true,
      permission: "EDIT",
    },
  });

  const permission = watch("permission");

  const onSubmit = (data: InviteFormData) => {
    console.log(data);
    inviteCoWorker(data);
    // Submit logic here
  };

  useEffect(() => {
    if (isSuccess) {
      reset();
    }
  }, [isSuccess]);

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="w-full mx-auto p-6">
        <h2 className="text-2xl font-bold mb-6">Invite co-worker</h2>

        {/* Email Field */}
        <div className="mb-4">
          <label className="block text-gray-700 font-medium mb-2">
            Email Address <span className="text-red-500">*</span>
          </label>
          <div className="border-gradient w-full rounded-lg">
            <input
              type="email"
              {...register("email")}
              placeholder="Enter their Email Address"
              className="w-full bg-[#F9EFEF] rounded-md px-4 py-2 outline-none"
            />
          </div>
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        {/* Notify Checkbox */}
        <div className="mb-6">
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              {...register("notify")}
              className="form-checkbox h-5 w-5 text-pink-500 focus:ring-blue-400"
            />
            <span className="text-gray-700 font-medium">Notify Person</span>
          </label>
        </div>

        {/* Permission Selection */}
        <div className="mb-6">
          <label className="block text-gray-700 font-medium mb-2">
            Permission roles <span className="text-red-500">*</span>
          </label>

          {(["EDIT", "VIEW"] as const).map((role) => (
            <div
              key={role}
              className={`border ${
                permission === role ? "border-gradient" : "border-gradient"
              } bg-pink-50 rounded-md p-4 mb-4 cursor-pointer`}
              onClick={() => setValue("permission", role)}
            >
              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 rounded-full border-2 border-pink-400 flex items-center justify-center">
                  {permission === role && (
                    <div className="w-2.5 h-2.5 bg-pink-500 rounded-full" />
                  )}
                </div>
                <div>
                  <p className="font-semibold capitalize">{role}</p>
                  <p className="text-sm text-gray-600">
                    {role === "EDIT"
                      ? "Edit the project and assign the annotator."
                      : "Can only view the project."}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Required Field Info */}
        <p className="text-gray-500 text-sm mt-4">
          ⓘ Required fields are marked with{" "}
          <span className="text-red-500">*</span>
        </p>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            variant="gradient"
            type="submit"
            className="px-6 py-2 rounded-md  text-white font-semibold"
            disabled={isPending}
          >
            {isPending ? "Submitting..." : "Invite co-worker"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default InviteCoworkerForm;
