// profilclient_Api/clientprofile_api.ts
import { customAxios } from "@/utils/axio-interceptor"
import { countries } from "@/utils/countries" // We'll create this next

interface ProfileData {
  companyName: string;
  phoneNumber: string;
  website: string;
  address: string;
  postalCode: string;
  country: string;
  stateProvince: string;
}

interface ClientProfileResponse {
  user: {
    id: string;
    name: string;
    lastname: string | null;
    email: string;
    timezone: string;
    domain: string;
  };
  profile: ProfileData;
}

interface BillingAddressResponse {
  id: string;
  country: string;
  address: string;
  state: string;
  postalCode: string;
  street: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

// Fetch client profile data
export const clientProfileShow = async (): Promise<ClientProfileResponse> => {
  const response = await customAxios.get('/v1/clients/profile-details');
  return response.data;
}

// Update client profile data
export const clientProfileUpdate = async (data: ProfileData): Promise<void> => {
  await customAxios.post('/v1/clients/profile-details', data);
}

// Get billing address with proper typing
export const BillingDetailsApiGet = async (): Promise<BillingAddressResponse> => {
  try {
    const response = await customAxios.get('/v1/clients/getaddress');
    const billingData = response.data.data;
    
    // Convert country code to full name
    const countryData = countries.find(c => c.code === billingData.country);
    const countryName = countryData ? countryData.name : billingData.country;
    
    return {
      ...billingData,
      country: countryName,
      postalCode: billingData.postalCodel // Fix field name mismatch
    };
  } catch (error) {
    console.error('Error fetching billing address:', error);
    throw error;
  }
}