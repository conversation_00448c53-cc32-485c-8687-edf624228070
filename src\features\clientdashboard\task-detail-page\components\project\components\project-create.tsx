import { <PERSON><PERSON>elp, Upload } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Shadcn DropdownMenu components
import { Checkbox } from "@/components/ui/checkbox"; // Shadcn Checkbox
import { Label } from "@/components/ui/label"; // Shadcn Label
import {
  createProject,
  AllAnoonatorClient,
  AllCooworkerAnoonatorClient,
} from "../project_api/Project_api";
import { useProjectCreateStyles } from "../styles/ProjectCreateStyles";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";

interface Annotator {
  id: string;
  name: string;
  email: string;
}

export default function ClientProjectCreate({
  onClose,
  onSuccess,
}: {
  onClose: () => void;
  onSuccess: () => void;
}) {
  const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "",
    annotatorIds: [] as string[], // Array for multiple annotators
    attachment: null as File | null,
    startDate: "",
    endDate: "",
  });

  const [errors, setErrors] = useState({
    title: false,
    description: false,
    priority: false,
    annotatorIds: false,
    startDate: false,
    endDate: false,
  });

  const [apiError, setApiError] = useState("");
  const [annotators, setAnnotators] = useState<Annotator[]>([]);
  const [loadingAnnotators, setLoadingAnnotators] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [fileError, setFileError] = useState("");

  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        setLoadingAnnotators(true);
        const response = await (user === "CLIENT"
          ? AllAnoonatorClient()
          : AllCooworkerAnoonatorClient());
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            setAnnotators(response.data);
          } else if (response.data.data && Array.isArray(response.data.data)) {
            setAnnotators(response.data.data);
          } else {
            console.error("Unexpected API response structure:", response);
            setAnnotators([]);
          }
        } else {
          setAnnotators([]);
        }
      } catch (error) {
        console.error("Error fetching annotators:", error);
        setApiError("Failed to load annotators. Please try again.");
      } finally {
        setLoadingAnnotators(false);
      }
    };

    fetchAnnotators();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setErrors({ ...errors, [e.target.name]: false });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFileError("");

    if (file) {
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        setFileError(
          `File size exceeds 10MB limit (${(file.size / (1024 * 1024)).toFixed(
            2
          )}MB)`
        );
        return;
      }
    }

    setFormData({ ...formData, attachment: file });
  };

  const validateForm = () => {
    setApiError("");

    const newErrors = {
      title: !formData.title,
      description: !formData.description,
      priority: !formData.priority,
      annotatorIds: formData.annotatorIds.length === 0,
      startDate: !formData.startDate,
      endDate: !formData.endDate,
    };

    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      if (end < start) {
        newErrors.endDate = true;
        setApiError("Due date cannot be earlier than start date");
      }
    }

    if (fileError) {
      setApiError(fileError);
      return false;
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    const projectForm = new FormData();
    projectForm.append("name", formData.title);
    projectForm.append("description", formData.description);
    projectForm.append("priority", formData.priority);
    projectForm.append("annotatorIds", JSON.stringify(formData.annotatorIds));

    const startDateObj = formData.startDate ? new Date(formData.startDate) : null;
    const dueDateObj = formData.endDate ? new Date(formData.endDate) : null;

    if (startDateObj) {
      startDateObj.setHours(9, 0, 0, 0);
    }
    if (dueDateObj) {
      dueDateObj.setHours(18, 0, 0, 0);
    }

    const startDate = startDateObj ? startDateObj.toISOString() : "";
    const dueDate = dueDateObj ? dueDateObj.toISOString() : "";

    projectForm.append("startDate", startDate);
    projectForm.append("dueDate", dueDate);

    if (formData.attachment) {
      projectForm.append("attachment", formData.attachment);
    }

    try {
      console.log("Submitting project with data:", {
        name: formData.title,
        description: formData.description,
        priority: formData.priority,
        annotatorIds: formData.annotatorIds,
        startDate,
        dueDate,
        hasAttachment: !!formData.attachment,
        attachmentName: formData.attachment ? formData.attachment.name : null,
        attachmentSize: formData.attachment ? formData.attachment.size : null,
        attachmentType: formData.attachment ? formData.attachment.type : null,
      });

      await createProject(projectForm, user);
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Error creating client project:", error);
      if (error.response && error.response.data && error.response.data.message) {
        setApiError(error.response.data.message);
      } else {
        setApiError("Failed to create project. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDateWithFullMonth = (dateStr: string): string => {
    if (!dateStr) return "";
    try {
      const date = new Date(dateStr);
      const day = String(date.getDate()).padStart(2, "0");
      const month = date.toLocaleString("default", { month: "long" });
      const year = String(date.getFullYear()).slice(-2);
      return `${day}/${month}/${year}`;
    } catch (error) {
      return "";
    }
  };

  // Handle annotator selection in dropdown
  const handleAnnotatorToggle = (annotatorId: string) => {
    setFormData((prev) => {
      const newAnnotatorIds = prev.annotatorIds.includes(annotatorId)
        ? prev.annotatorIds.filter((id) => id !== annotatorId)
        : [...prev.annotatorIds, annotatorId];
      setErrors({ ...errors, annotatorIds: newAnnotatorIds.length === 0 });
      return { ...prev, annotatorIds: newAnnotatorIds };
    });
  };

  const styles = useProjectCreateStyles();

  return (
    <div className={styles.modalContainer}>
      <div className={styles.scrollContainer}>
        <div className={styles.formContainer}>
          <h2 className={styles.heading}>Create Project</h2>
          {apiError && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md mb-4">
              {apiError}
            </div>
          )}
          <form onSubmit={handleSubmit} className={styles.formSpacing}>
            <div>
              <label className={styles.label}>Title *</label>
              <div className="border-gradient w-full rounded-md">
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className={`${styles.input} ${errors.title ? "border border-red-500" : ""}`}
                  placeholder="Enter task title"
                  disabled={isSubmitting}
                />
              </div>
              {errors.title && (
                <p className="text-red-500 text-sm">Title is required</p>
              )}
            </div>

            <div>
              <label className={styles.label}>Description *</label>
              <div className="border-gradient w-full h-20 rounded-md">
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className={styles.textarea}
                  placeholder="Enter description"
                  disabled={isSubmitting}
                />
              </div>
              {errors.description && (
                <p className="text-red-500 text-sm">Description is required</p>
              )}
            </div>

            <div className={styles.flexContainer}>
              <div className="flex-1">
                <label className={styles.label}>Priority *</label>
                <div className="border-gradient w-full rounded-md">
                  <select
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    className={`${styles.select} ${errors.priority ? "ring-1 ring-red-500" : ""}`}
                    disabled={isSubmitting}
                  >
                    <option value="">Select Priority</option>
                    <option value="HIGH">HIGH</option>
                    <option value="MEDIUM">MEDIUM</option>
                    <option value="LOW">LOW</option>
                  </select>
                </div>
                {errors.priority && (
                  <p className="text-red-500 text-sm">Priority is required</p>
                )}
              </div>

              <div className="flex-1">
                <label className={styles.label}>
                  Annotators *{" "}
                  {loadingAnnotators && (
                    <span className="text-blue-500 text-xs ml-1">(Loading...)</span>
                  )}
                </label>
                <div className="border-gradient w-full rounded-md">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className={`w-full text-[13px] font-normal  bg-[#F9EFEF] hover:bg-[#f3dddd] text-left py-[21px] ${errors.annotatorIds ? "border-red-500" : ""}`}
                        disabled={loadingAnnotators || isSubmitting}
                      >
                        {formData.annotatorIds.length > 0
                          ? ` ${formData.annotatorIds
                            .map((id) => annotators.find((a) => a.id === id)?.name)
                            .filter(Boolean)
                            .join(", ")}`
                          : "Select Annotators"}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-[280px] bg-[#F9EFEF] max-w-[400px]  max-h-60 overflow-y-auto">
                      {loadingAnnotators ? (
                        <DropdownMenuItem disabled>
                          Loading annotators...
                        </DropdownMenuItem>
                      ) : annotators.length > 0 ? (
                        annotators.map((annotator) => (
                          <DropdownMenuItem
                            key={annotator.id}
                            onSelect={(e) => {
                              e.preventDefault();
                              handleAnnotatorToggle(annotator.id);
                            }}
                            className="flex items-center w-full space-x-2 border py-2 cursor-pointer"
                          >
                            <Checkbox
                              id={annotator.id}
                              checked={formData.annotatorIds.includes(annotator.id)}
                              disabled={isSubmitting}
                              className="bg-[#f9efef]"
                            />
                            <Label htmlFor={annotator.id} className="text-[13px] font-normal cursor-pointer">
                              {annotator.name}
                            </Label>
                          </DropdownMenuItem>
                        ))
                      ) : (
                        <DropdownMenuItem disabled>
                          No annotators available
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {errors.annotatorIds && (
                  <p className="text-red-500 text-sm mt-1">
                    At least one annotator is required
                  </p>
                )}
                {annotators.length === 0 && !loadingAnnotators && (
                  <p className="text-amber-500 text-xs mt-1">
                    No annotators are assigned to you. Please contact admin.
                  </p>
                )}
              </div>
            </div>

            <div className={styles.flexContainer}>
              <div className="flex-1">
                <label className={styles.label}>Start Date *</label>
                <div className="border-gradient w-full rounded-md">
                  <input
                    type="date"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleChange}
                    className={styles.dateInput}
                    min={new Date().toLocaleDateString("en-CA")}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.startDate && (
                  <p className="text-red-500 text-sm mt-1">Start Date is required</p>
                )}
                {formData.startDate && (
                  <p className="text-gray-500 text-xs mt-1">
                    Selected: {formatDateWithFullMonth(formData.startDate)}
                  </p>
                )}
              </div>
              <div className="flex-1">
                <label className={styles.label}>Due Date *</label>
                <div className="border-gradient w-full rounded-md">
                  <input
                    type="date"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleChange}
                    className={styles.dateInput}
                    min={new Date().toLocaleDateString("en-CA")}
                    disabled={isSubmitting}
                  />
                </div>
                {errors.endDate && (
                  <p className="text-red-500 text-sm mt-1">End Date is required</p>
                )}
                {formData.endDate && (
                  <p className="text-gray-500 text-xs mt-1">
                    Selected: {formatDateWithFullMonth(formData.endDate)}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className={styles.label}>Attachment (Optional)</label>
              <div className="relative w-full mt-2 rounded-lg border-gradient">
                <div className={styles.uploadContainer}>
                  <Upload className={styles.uploadIcon} />
                  <span className={styles.uploadText}>
                    {formData.attachment
                      ? formData.attachment.name
                      : "Choose file (Pdf, Docs file)"}
                  </span>
                  <input
                    type="file"
                    name="attachment"
                    onChange={handleFileChange}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
              {fileError ? (
                <p className="text-red-500 text-xs mt-1">{fileError}</p>
              ) : (
                formData.attachment && (
                  <p className="text-gray-500 text-xs mt-1">
                    Selected file:{" "}
                    {formData.attachment.name.split(" ").slice(0, 2).join(" ")}
                    {formData.attachment.name.split(" ").length > 5 && "..."} (
                    {(formData.attachment.size / 1024).toFixed(2)} KB)
                  </p>
                )
              )}
            </div>

            <div className={styles.helpText}>
              <CircleHelp className={styles.helpIcon} />
              <p>Required fields are marked with *</p>
            </div>

            <div className={styles.buttonContainer}>
              <Button
                variant="ghost"
                type="button"
                onClick={onClose}
                className="px-10 py-2.5 text-sm border-gradient"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                variant="gradient"
                type="submit"
                className="px-10 py-2.5 text-sm text-white"
                disabled={isSubmitting || loadingAnnotators}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}