import React from "react";

interface Props {
  errorCode: number;
}

export const ErrorPage: React.FC<Props> = ({ errorCode }) => {
  const renderErrorContent = () => {
    switch (errorCode) {
      case 404:
        return (
          <>
            <h1>404 - Page Not Found</h1>
            <p>Sorry, the page you're looking for does not exist.</p>
          </>
        );
      case 403:
        return (
          <>
            <h1>403 - Forbidden</h1>
            <p>You don't have permission to access this page.</p>
          </>
        );
      case 500:
      default:
        return (
          <>
            <h1>500 - Internal Server Error</h1>
            <p>Something went wrong. Please try again later.</p>
          </>
        );
    }
  };

  return (
    <div style={{ padding: "40px", textAlign: "center" }}>
      {renderErrorContent()}
    </div>
  );
};
