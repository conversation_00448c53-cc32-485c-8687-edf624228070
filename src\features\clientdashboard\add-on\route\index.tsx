import { Route } from "react-router-dom";

import { Routes } from "react-router-dom";
import AddOn from "../component/addon";
import BillingForm from "../component/commonpackageplan/address.checkout";
import AddOnQuestioniare from "../component/commonpackageplan/addquestioniare";

export default function AddOnRoutePage() {
  return (
    <>
      <Routes>
        <Route path="/" element={<AddOn />} />
        <Route path="/addon-questinaire" element={<AddOnQuestioniare />} />
        <Route path="/address-billing" element={<BillingForm />} />
        {/* <Route path="/billing" element={<BillingForm/>} /> */}
      </Routes>
    </>
  );
}
