import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { BackButton } from "@/_components/common";
import { useEffect, useState } from "react";
import { AdminAndOtherProfileapi } from "../profileclient_api/adminother_api";
import { toast } from "react-toastify";

const AdminAndOtherProfile = () => {
  const [user, setUser] = useState({
    name: "",
    email: "",
    role: "",
    // id: "",
    accountStatus: "",
    createdAt: ""
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);
        const profileData = await AdminAndOtherProfileapi();
        
        setUser({
          name: profileData.name,
          email: profileData.email,
          role: profileData.role,
          // id: profileData.id,
          accountStatus: profileData.accountStatus,
          createdAt: profileData.createdAt
        });
      } catch (error) {
        toast.error('Failed to fetch profile data');
        console.error('Error fetching profile:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  if (loading) {
    return (
      <Card className="w-full border-none">
        <CardHeader className="flex flex-row">
          <BackButton/>
          <CardTitle>User Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <div>Loading profile data...</div>
        </CardContent>
      </Card>
    );
  }

  // Format the date for display
  const formattedDate = new Date(user.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <Card className="w-full border-none">
      <CardHeader className="flex flex-row">
        <BackButton/>
        <CardTitle>User Profile</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Column 1 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <div className="border-gradient rounded-lg">
                <Input id="name" value={user.name} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <div className="border-gradient rounded-lg">
                <Input id="email" type="email" value={user.email} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="joinDate">Join Date</Label>
              <div className="border-gradient rounded-lg">
                <Input id="joinDate" value={formattedDate} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div>
          </div>

          {/* Column 2 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="role">User Role</Label>
              <div className="flex gap-2 border-gradient rounded-lg">
                <Input id="role" value={user.role} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div>

            {/* <div className="space-y-2">
              <Label htmlFor="id">User ID</Label>
              <div className="border-gradient rounded-lg">
                <Input id="id" value={user.id} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div> */}

            <div className="space-y-2">
              <Label htmlFor="status">Account Status</Label>
              <div className="flex items-center gap-2 border-gradient rounded-lg">
                <Input id="status" value={user.accountStatus} readOnly className="bg-[#F9EFEF]"/>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminAndOtherProfile;