import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { HiDotsVertical } from "react-icons/hi";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import DeleteAdminClient from "./modal/deleteadminclient";
import ReactiveAdminClient from "./modal/reactiveadminclient";
import SuspendeAdminClient from "./modal/suspendadminclient";
import { Dialog } from "@/components/ui/dialog";

interface ClientAdminCardProps {
  client: {
    id: string;
    name: string;
    email: string;
    annotators: string;
    projects: string;
    joiningDate: string;
    expiringon: string;
    coworker: string[];
    image?: string;
    status: "active" | "suspended";
  };
  onStatusChange?: (id: string, newStatus: "active" | "suspended") => void;
}

export default function ClientAdminCard({
  client,
  onStatusChange,
}: ClientAdminCardProps) {
  const navigate = useNavigate();
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [currentModal, setCurrentModal] = useState<{
    type: "suspend" | "reactive" | "delete";
    open: boolean;
  } | null>(null);

  const getCustomAvatarUrl = (name: string): string => {
    if (!name) return "";
    const firstName = name.trim().split(" ")[0];
    const initials =
      firstName.length >= 2
        ? firstName.substring(0, 2).toUpperCase()
        : firstName.charAt(0).toUpperCase();
    return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(
      initials
    )}`;
  };

  const imageUrl = client.image || getCustomAvatarUrl(client.name);

  const handleMenuItemClick = (type: "suspend" | "reactive" | "delete") => {
    setCurrentModal({ type, open: true });
    setOpenDropdown(false);
  };

  const closeModal = () => {
    setCurrentModal(null);
  };

  const handleSuspendSuccess = () => {
    if (onStatusChange) onStatusChange(client.id, "suspended");
  };

  const handleReactivateSuccess = () => {
    if (onStatusChange) onStatusChange(client.id, "active");
  };

  const renderModal = () => {
    if (!currentModal) return null;

    switch (currentModal.type) {
      case "delete":
        return (
          <DeleteAdminClient
            client={client}
            open={currentModal.open}
            onOpenChange={closeModal}
          />
        );
      case "reactive":
        return (
          <ReactiveAdminClient
            client={client}
            open={currentModal.open}
            onOpenChange={closeModal}
            onSuccess={handleReactivateSuccess}
          />
        );
      case "suspend":
        return (
          <SuspendeAdminClient
            client={client}
            open={currentModal.open}
            onOpenChange={closeModal}
            onSuccess={handleSuspendSuccess}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="border border-[#FF577F] rounded-lg shadow-md bg-white flex flex-col gap-y-0 h-full min-h-[250px] relative">
      {/* Client Header Section */}
      <div className="flex flex-row items-center justify-between">
        <div className="flex flex-row items-center p-2 gap-2">
          <img
            src={imageUrl}
            alt={client.name}
            className="rounded-full w-10 h-10"
          />
          <div className="flex flex-col">
            <div className="flex items-center gap-3">
              <h3 className="font-semibold text-sm">{client.name}</h3>
              <StatusIndicator status={client.status} />
            </div>
          </div>
        </div>

        <div>
          <Dialog>
            <DropdownMenu open={openDropdown} onOpenChange={setOpenDropdown}>
              <DropdownMenuTrigger asChild>
                <button className="p-1 hover:bg-gray-100 rounded-full">
                  <HiDotsVertical className="text-gray-600" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-[10rem] p-2 font-normal cursor-pointer"
              >
                <DropdownMenuItem asChild>
                  <Link
                    to={`/admin/admindetails/clients/${client.id}`}
                    className="w-full cursor-pointer focus:text-blue-600"
                  >
                    View Profile
                  </Link>
                </DropdownMenuItem>

                {client.status !== "suspended" ? (
                  <DropdownMenuItem
                    onClick={() => handleMenuItemClick("suspend")}
                    className="cursor-pointer focus:text-red-600"
                  >
                    Suspend
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    onClick={() => handleMenuItemClick("reactive")}
                    className="cursor-pointer focus:text-green-600"
                  >
                    Reactivate
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem
                  onClick={() => handleMenuItemClick("delete")}
                  className="focus:text-red-600 cursor-pointer"
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            {renderModal()}
          </Dialog>
        </div>
      </div>

      {/* Client Details Section */}
      <div className="flex flex-col text-[12px] gap-y-1 flex-grow px-3 py-2 space-y-1 border-t border-gray-100">
        <div className="flex justify-between py-[2px]">
          <span className="text-[#5B5B5B] w-[90px] inline-block">
            Annotators:
          </span>
          <span>{client.annotators}</span>
        </div>

        <div className="flex justify-between py-[2px]">
          <span className="text-[#5B5B5B] w-[90px] inline-block">
            Projects:
          </span>
          <span>{client.projects}</span>
        </div>

        <div className="flex justify-between items-center py-[2px]">
          <span className="text-[#5B5B5B] w-[90px] inline-block">
            Coworker:
          </span>
          {client.coworker && client.coworker.length > 0 ? (
            <div
              className="flex items-center relative cursor-pointer"
              onMouseEnter={() => setHoveredIndex(1)}
              onMouseLeave={() => setHoveredIndex(null)}
            >
              <span>{client.coworker[0]}</span>
              {client.coworker.length > 1 && (
                <span className="text-[#0000FF] ml-1">more</span>
              )}
              {hoveredIndex === 1 && client.coworker.length > 1 && (
                <div className="absolute top-full right-0 mt-1 bg-white border rounded shadow-md px-3 py-2 z-50 w-40 lg:w-40 xl:w-44 2xl:w-48 text-left">
                  <ul className="text-sm list-disc ml-3 lg:ml-3 xl:ml-4 2xl:ml-4 space-y-1">
                    {client.coworker.map((coworker, idx) => (
                      <li key={idx} className="text-sm text-[#7373ec]">
                        {coworker}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ) : (
            <span>0</span>
          )}
        </div>

        <div className="flex justify-between py-[2px]">
          <span className="text-[#5B5B5B] w-[90px] inline-block">
            Joining Date:
          </span>
          <span>{client.joiningDate}</span>
        </div>

        <div className="flex justify-between py-[2px]">
          <span className="text-[#5B5B5B] w-[90px] inline-block">
            Expiring on:
          </span>
          <span>{client.expiringon}</span>
        </div>
      </div>

      {/* All Projects Button */}
      <div className="w-full p-2 mt-auto">
        <button
          onClick={() =>
            navigate(
              `/admin/adminclients?id=${client.id}&name=${encodeURIComponent(
                client.name
              )}`
            )
          }
          className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-medium w-full py-1 text-[11px] lg:text-[11px] xl:text-[12px] 2xl:text-[12px]"
        >
          All Projects
        </button>
      </div>
    </div>
  );
}

const StatusIndicator = ({ status }: { status: "active" | "suspended" }) => {
  return (
    <span className="text-xs font-medium flex items-center gap-1">
      {status === "suspended" ? (
        <>
          <span className="relative flex h-2 w-2">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
          </span>
          Suspended
        </>
      ) : (
        <>
          <span className="relative flex h-2 w-2">
            <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
          </span>
          Active
        </>
      )}
    </span>
  );
};
