import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

const QuestionnaireForm: React.FC = () => {
  const [formData, setFormData] = useState({
    packageCategory: "",
    timezone: "",
    industry: "",
    annotationCategory: "",
    fromTime: "",
    toTime: "",
    description: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
    setErrors(prev => ({ ...prev, [id]: "" })); // clear error on change
  };

  // const validate = () => {
  //   const newErrors: { [key: string]: string } = {};
  //   if (!formData.packageCategory) newErrors.packageCategory = "Please select a package category.";
  //   if (!formData.timezone) newErrors.timezone = "Please select a time zone.";
  //   if (!formData.industry) newErrors.industry = "Please select an industry.";
  //   if (!formData.annotationCategory) newErrors.annotationCategory = "Please select an annotation category.";
  //   if (!formData.fromTime) newErrors.fromTime = "Please select a start time.";
  //   if (!formData.toTime) newErrors.toTime = "Please select an end time.";
  //   return newErrors;
  // };

  // const handleSubmit = (e: React.FormEvent) => {
  //   e.preventDefault();
  //   const validationErrors = validate();
  //   if (Object.keys(validationErrors).length === 0) {
  //     console.log("Form submitted!", formData);
  //     // Add actual submit logic here (API call, etc.)
  //   } else {
  //     setErrors(validationErrors);
  //   }
  // };

  return (
    <div className="max-w-2xl mx-auto mt-3 max-h-[87vh] rounded-lg bg-white font-inter">
      <h2 className="text-[24px] font-bold mb-6 text-left">Questionnaire</h2>

      <form className="space-y-4">
        <div>
          <label className="block mb-1 text-[17px] font-semibold" htmlFor="packageCategory">Package Category</label>
          <select id="packageCategory" value={formData.packageCategory} onChange={handleChange}
            className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
            <option value="">Select Package Category</option>
            <option>Basic</option>
            <option>Standard</option>
            <option>Premium</option>
          </select>
          {errors.packageCategory && <p className="text-red-500 text-sm mt-1">{errors.packageCategory}</p>}
        </div>

        <div>
          <label className="block mb-1 text-[17px] font-medium" htmlFor="timezone">Time Zone</label>
          <select id="timezone" value={formData.timezone} onChange={handleChange}
            className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
            <option value="">Select your preferred time zone</option>
            <option>UTC</option>
            <option>IST (India)</option>
            <option>PST (US)</option>
            <option>EST (US)</option>
          </select>
          {errors.timezone && <p className="text-red-500 text-sm mt-1">{errors.timezone}</p>}
        </div>

        <div>
          <label className="block mb-1 text-[17px] font-medium" htmlFor="industry">Industry</label>
          <select id="industry" value={formData.industry} onChange={handleChange}
            className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
            <option value="">Select Industry</option>
            <option>Healthcare</option>
            <option>Education</option>
            <option>Finance</option>
            <option>Technology</option>
          </select>
          {errors.industry && <p className="text-red-500 text-sm mt-1">{errors.industry}</p>}
        </div>

        <div>
          <label className="block mb-1 text-[17px] font-medium" htmlFor="annotationCategory">Annotation Category</label>
          <select id="annotationCategory" value={formData.annotationCategory} onChange={handleChange}
            className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
            <option value="">Select Annotation Category</option>
            <option>Text</option>
            <option>Image</option>
            <option>Video</option>
            <option>Audio</option>
          </select>
          {errors.annotationCategory && <p className="text-red-500 text-sm mt-1">{errors.annotationCategory}</p>}
        </div>

        <div className="flex items-center space-x-4">
          <div className="w-1/2">
            <label className="block mb-1 text-[17px] font-medium" htmlFor="fromTime">From</label>
            <select id="fromTime" value={formData.fromTime} onChange={handleChange}
              className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
              <option value="">Select From Time</option>
              <option>00:00</option>
              <option>06:00</option>
              <option>12:00</option>
              <option>18:00</option>
            </select>
            {errors.fromTime && <p className="text-red-500 text-sm mt-1">{errors.fromTime}</p>}
          </div>

          <div className="w-1/2">
            <label className="block mb-1 text-[17px] font-medium" htmlFor="toTime">To</label>
            <select id="toTime" value={formData.toTime} onChange={handleChange}
              className="w-full p-3 border border-[#FF577F] rounded-lg bg-white">
              <option value="">Select To Time</option>
              <option>00:00</option>
              <option>06:00</option>
              <option>12:00</option>
              <option>18:00</option>
              <option>23:59</option>
            </select>
            {errors.toTime && <p className="text-red-500 text-sm mt-1">{errors.toTime}</p>}
          </div>
        </div>

        <div>
          <label className="block mb-1 text-[17px] font-medium" htmlFor="description">Description (optional)</label>
          <textarea
            id="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Description (optional)"
            className="w-full p-3 border border-[#FF577F] rounded-lg min-h-[120px]"
          />
        </div>

        <div className="text-center pt-4 flex justify-center">
          <button
          onClick={() => navigate("/dashboard")}
            type="submit"
            className="flex flex-row gap-x-2 px-5 py-2 items-center justify-center text-white text-lg font-medium rounded-lg transition-all bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2]"
          >
            Process for payment
          </button>
        </div>
      </form>
    </div>
  );
};

export default QuestionnaireForm;
