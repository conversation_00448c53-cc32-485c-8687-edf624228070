import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FaCheck } from "react-icons/fa6";
import { IoMdClose } from "react-icons/io";
import { Annotator } from "@/types/onboarding.types";
import { Row } from "@tanstack/react-table";
import { useUpdateCoWorkerPermissionMutation } from "../api/mutation";

export const PermissionSelector = ({ row }: { row: Row<Annotator> }) => {
  const { mutate: updatePermission } = useUpdateCoWorkerPermissionMutation();
  const [value, setValue] = React.useState(row.original.coworkerPermission);
  const [savedValue, setSavedValue] = React.useState(row.original.coworkerPermission); // for undo

  const handleSubmit = () => {
    updatePermission({
      id: row.original.id,
      permission: value as "VIEW" | "EDIT",
    });
    setSavedValue(value); // save current value
  };

  return (
    <div className="flex items-center gap-x-2">
      <Select
        value={value}
        onValueChange={(val) => setValue(val as "VIEW" | "EDIT")}
      >
        <SelectTrigger className="border-gradient">
          <SelectValue placeholder="Select Permission" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="VIEW">View</SelectItem>
          <SelectItem value="EDIT">Editor</SelectItem>
          <SelectItem value="edit">Remove</SelectItem>
        </SelectContent>
      </Select>

      <button
        className="w-[25px] h-[25px] rounded-full flex justify-center items-center bg-[#5AB24A]"
        onClick={handleSubmit}
      >
        <FaCheck className="text-[15px] text-white" />
      </button>

      <button
        className="w-[25px] h-[25px] rounded-full flex justify-center items-center bg-[#F44336]"
        onClick={() => setValue(savedValue)} // undo
      >
        <IoMdClose className="text-[15px] text-white" />
      </button>
    </div>
  );
};
