import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ClientLogin } from "@/features/auth/api/client-api";
import { AuthCommonComponent } from "./common/AuthCommon";
import { Eye, EyeOff, MoveRight } from "lucide-react";
import ReCAPTCHAComponent from "./common/ReCAPTCHA";
import Logo from "@/assets/darklogo.png";

const Login: React.FC = () => {
  const navigate = useNavigate();
  // const dispatch = useDispatch();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState({ email: "", password: "" });
  const [loginError, setLoginError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Added loading state

  const handleLogin = async () => {
    let newErrors = { email: "", password: "" };
    setLoginError("");

    if (!email) newErrors.email = "Email or Username is required";
    if (!password) newErrors.password = "Password is required";
    setErrors(newErrors);

    if (!newErrors.email && !newErrors.password) {
      try {
        setIsLoading(true); // Set loading state to true
        const response = await ClientLogin(email, password);
        console.log("Login API Response:", response);

        // ✅ If OTP was sent successfully
        if (response?.message) {
          navigate("/auth/otp-verification", {
            state: { email, type: "login" },
          });

          return;
        }

        // ❌ If none matched, assume something unexpected
        setLoginError("Unexpected server response. Please try again.");
      } catch (error: any) {
        console.error("Login failed:", error.message);
        setLoginError("Invalid email or password");
      } finally {
        setIsLoading(false); // Set loading state to false
      }
    }
  };

  return (
    <div className="w-full flex items-center justify-center">
      <div className="w-full  transition-transform duration-300">
        <div className="flex flex-col md:flex-row  justify-between   w-full">
          <div className="h-screen p-3 ">
            {/* <button
              className="text-pink-500 hover:text-pink-600   text-lg font-medium flex items-center"
            >
              <MoveLeft />
            </button> */}
            <img src={Logo} alt="Logo" className="w-44" />
          </div>

          <div className="flex flex-row w-full gap-14">
            <div className="w-[45%] flex flex-col justify-center">
              <div className="w-full">
                <div className="mx-auto min-h-[350px] shadow-[0px_3px_48px_10px_#0000000F] px-6 py-3 rounded-2xl">
                  <h2 className="text-[36px] font-bold font-inter text-[#282828] mb-6">
                    Login
                  </h2>

                  {loginError && (
                    <p className="text-red-500 text-sm mb-4">{loginError}</p>
                  )}

                  <div className="space-y-6">
                    <div>
                      <label className="block text-[14px] font-medium text-[#757575] mb-1">
                        Email or Username
                      </label>
                      <div className="border-gradient rounded-xl">
                        <input
                          type="text"
                          className={`w-full p-3 bg-[#F9EFEF] outline-none ${errors.email ? "border-red-500" : "border-gray-300"
                            }`}
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Enter your email or username"
                        />
                      </div>
                      {errors.email && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="block text-[18px] font-medium text-gray-700 mb-1">
                        Password
                      </label>

                      <div className="relative border-gradient rounded-xl">
                        <input
                          type={showPassword ? "text" : "password"}
                          className={`w-full p-3 hide-edge-password-toggle bg-[#F9EFEF] pr-11 outline-none ${errors.password
                              ? "border-red-500"
                              : "border-gray-300"
                            }`}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="Enter your password"
                          autoComplete="new-password"
                          
                        />

                        <button
                          type="button"
                          onClick={() => setShowPassword((prev) => !prev)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 opacity-50 hover:text-gray-700"
                        >
                          {showPassword ? (
                            <Eye size={20} />
                          ) : (
                            <EyeOff size={20} />
                          )}
                        </button>
                      </div>
                      {errors.password && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.password}
                        </p>
                      )}
                    </div>

                    {/* ReCAPTCHA */}
                    <ReCAPTCHAComponent />
                  </div>

                  <Link
                    to="/auth/forget-password"
                    className="text-[16px] text-[#D53148] hover:text-[#ec5166] mt-2 inline-block"
                  >
                    Forgotten Password?
                  </Link>

                  <button
                    onClick={handleLogin}
                    disabled={isLoading} // Disable button when loading
                    className="mt-6 w-[30%] text-[16px] p-4 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg font-semibold flex items-center justify-center gap-2 disabled:opacity-70"
                  >
                    {isLoading ? (
                      "Loading..."
                    ) : (
                      <>
                        Login <MoveRight className="w-[18px] h-[18px]" />
                      </>
                    )}
                  </button>

                  <p className="text-[16px] text-gray-600 mt-4">
                    Didn&apos;t have an account?{" "}
                    <Link
                      to="/auth/signup"
                      className="text-red-500 hover:underline"
                    >
                      Sign Up
                    </Link>
                  </p>
                </div>
              </div>
            </div>

            <div className="w-[48%]">
              <AuthCommonComponent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
