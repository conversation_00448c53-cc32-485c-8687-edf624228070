// import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive card styles
export const useCardStyles = () => {
  // const { isLaptopMd, isLaptopLg } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Common styles for all card types
  const commonStyles = {
    container: "relative",
    card: "border border-[#FF577F] rounded-lg shadow-md bg-white flex flex-col h-full",
    profileSection: "flex flex-col",
    statusContainer: "flex justify-between items-center",
    profileContainer: "flex items-center",
    profileImage: "rounded-full",
    nameContainer: "flex flex-col",
    name: "font-semibold",
    starIcon: "text-[#FFC107]",
    email: "text-gray-500",
    description: "text-gray-500",
    statusBadge: "bg-[#5AB24A] text-white rounded-full",
    infoContainer: "flex-grow",
    infoRow: "flex items-center justify-between border-b border-gray-100 last:border-b-0",
    infoLabel: "text-gray-600 font-medium",
    infoValue: "text-gray-800",
    actionContainer: "flex justify-between mt-auto",
  };

  // Get annotator card styles
  const getAnnotatorStyles = () => {
    // Specific grid layout for 1024px
    if (windowWidth <= 1024) {
      return {
        ...commonStyles,
        // 1024px - 3 cards per row
        grid: "grid grid-cols-3 gap-4 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-3`,
        profileSection: `${commonStyles.profileSection} mb-2`,
        statusContainer: `${commonStyles.statusContainer} mb-1.5`,
        profileContainer: `${commonStyles.profileContainer} gap-2`,
        profileImage: `${commonStyles.profileImage} w-10 h-10`,
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        email: `${commonStyles.email} text-[11px]`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[10px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-1.5`,
        infoRow: `${commonStyles.infoRow} text-[11px] py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1 text-[10px] rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-2 py-1 text-white text-[10px] rounded-lg flex-1 text-center"
      };
    } else if (windowWidth >= 1440) {
      // 1440px and above (including 2560px)
      return {
        ...commonStyles,
        grid: "grid grid-cols-4 gap-6 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-4`,
        profileSection: `${commonStyles.profileSection} mb-3`,
        statusContainer: `${commonStyles.statusContainer} mb-2`,
        profileContainer: `${commonStyles.profileContainer} gap-2`,
        profileImage: `${commonStyles.profileImage} w-10 h-10`,
        name: `${commonStyles.name} text-base`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        email: `${commonStyles.email} text-xs`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[10px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2`,
        infoRow: `${commonStyles.infoRow} text-sm py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[100px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-xs rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-2 py-1.5 text-white text-xs rounded-lg flex-1 text-center"
      };
    } else {
      // 1080px to 1440px
      return {
        ...commonStyles,
        grid: "grid grid-cols-4 gap-4 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-3.5`,
        profileSection: `${commonStyles.profileSection} mb-2.5`,
        statusContainer: `${commonStyles.statusContainer} mb-1.5`,
        profileContainer: `${commonStyles.profileContainer} gap-2`,
        profileImage: `${commonStyles.profileImage} w-9 h-9`,
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        email: `${commonStyles.email} text-xs`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[9px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2`,
        infoRow: `${commonStyles.infoRow} text-xs py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-2.5`,
        shiftButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-[9px] rounded-lg flex-1 text-center",
        attendanceButton: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] px-2 py-1.5 text-white text-[9px] rounded-lg flex-1 text-center"
      };
    }
  };

  // Get project card styles
  const getProjectStyles = () => {
    // Specific grid layout for 1024px
    if (windowWidth <= 1024) {
      return {
        ...commonStyles,
        // 1024px - 3 cards per row
        grid: "grid grid-cols-3 gap-4 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-3`,
        profileSection: `${commonStyles.profileSection} mb-2`,
        statusContainer: `${commonStyles.statusContainer} mb-1.5`,
        projectContainer: `${commonStyles.profileContainer} gap-2`,
        projectImage: "w-10 h-10 rounded-md",
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        description: `${commonStyles.description} text-[11px]`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[10px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2 space-y-1.5`,
        infoRow: `${commonStyles.infoRow} text-[11px] py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: "mt-2",
        progressBar: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] h-2.5 rounded-full",
        progressText: "text-[11px] text-gray-600 mt-1",
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1 text-[10px] rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-2 py-1 text-white text-[10px] rounded-lg flex-1 text-center"
      };
    } else if (windowWidth >= 1440) {
      // 1440px and above (including 2560px)
      return {
        ...commonStyles,
        grid: "grid grid-cols-4 gap-6 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-4`,
        profileSection: `${commonStyles.profileSection} mb-3`,
        statusContainer: `${commonStyles.statusContainer} mb-2`,
        projectContainer: `${commonStyles.profileContainer} gap-2`,
        projectImage: "w-10 h-10 rounded-md",
        name: `${commonStyles.name} text-base`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        description: `${commonStyles.description} text-xs`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[10px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2`,
        infoRow: `${commonStyles.infoRow} text-sm py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[100px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: "mt-3",
        progressBar: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] h-2.5 rounded-full",
        progressText: "text-sm text-gray-600 mt-1",
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-3`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-xs rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-2 py-1.5 text-white text-xs rounded-lg flex-1 text-center"
      };
    } else {
      // 1080px to 1440px
      return {
        ...commonStyles,
        grid: "grid grid-cols-4 gap-4 w-full mx-auto max-w-[98%]",
        card: `${commonStyles.card} p-3.5`,
        profileSection: `${commonStyles.profileSection} mb-2.5`,
        statusContainer: `${commonStyles.statusContainer} mb-1.5`,
        projectContainer: `${commonStyles.profileContainer} gap-2`,
        projectImage: "w-9 h-9 rounded-md",
        name: `${commonStyles.name} text-sm`,
        starIcon: `${commonStyles.starIcon} text-lg`,
        description: `${commonStyles.description} text-xs`,
        statusBadge: `${commonStyles.statusBadge} px-2 py-0.5 text-[9px]`,
        infoContainer: `${commonStyles.infoContainer} mt-2`,
        infoRow: `${commonStyles.infoRow} text-xs py-1`,
        infoLabel: `${commonStyles.infoLabel} w-[90px] inline-block`,
        infoValue: `${commonStyles.infoValue} text-right`,
        progressContainer: "mt-2.5",
        progressBar: "bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] h-2.5 rounded-full",
        progressText: "text-xs text-gray-600 mt-1",
        actionContainer: `${commonStyles.actionContainer} gap-2 mt-2.5`,
        editButton: "border border-[#FF577F] text-[#FF577F] px-2 py-1.5 text-[9px] rounded-lg flex-1 text-center",
        deleteButton: "bg-red-500 px-2 py-1.5 text-white text-[9px] rounded-lg flex-1 text-center"
      };
    }
  };

  return {
    annotatorStyles: getAnnotatorStyles(),
    projectStyles: getProjectStyles()
  };
};
