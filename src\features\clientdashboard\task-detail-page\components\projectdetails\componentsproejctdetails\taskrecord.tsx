import React, { useState, useEffect } from 'react';
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { getProjectTaskStats } from '../../../api/add-project-api';

interface TaskRecordProps {
  projectId: string;
}

interface TaskStats {
  total: number;
  completed: number;
  pending: number;
  toDo: number;
}

const TaskRecord: React.FC<TaskRecordProps> = ({ projectId }) => {
  const [taskStats, setTaskStats] = useState<TaskStats>({
    total: 0,
    completed: 0,
    pending: 0,
    toDo: 0
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTaskStats = async () => {
      if (!projectId) return;
      
      try {
        setLoading(true);
        const response = await getProjectTaskStats(projectId);
        
        if (response && response.success && response.data) {
          setTaskStats(response.data);
        } else {
          setError("Failed to load task statistics");
        }
      } catch (error) {
        console.error("Error loading task statistics:", error);
        setError("An error occurred while loading task statistics");
      } finally {
        setLoading(false);
      }
    };

    fetchTaskStats();
  }, [projectId]);

  if (loading) {
    return (
      <div className="bg-[#F3F3F3] shadow-md rounded-lg p-2 lg:p-3 xl:p-4 2xl:p-5 w-full flex justify-center items-center">
        <BrandedGlobalLoader isLoading={true} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-[#F3F3F3] shadow-md rounded-lg p-2 lg:p-3 xl:p-4 2xl:p-5 w-full">
        <div className="flex justify-center items-center h-full">
          <p className="text-red-500 text-xs lg:text-sm xl:text-base">
            {error}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg p-2 lg:p-3 xl:p-4 2xl:p-5 w-full">
      <div className="flex flex-col px-4 lg:px-6 xl:px-4 2xl:px-10 py-2 lg:py-3 xl:py-1 2xl:py-5">
        <div className="flex justify-between items-center mb-1 lg:mb-1 xl:mb-1 2xl:mb-4">
          <h1 className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium">All Task:</h1>
          <p className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-normal">{taskStats.total}</p>
        </div>
        <div className="flex justify-between items-center mb-1 lg:mb-1 xl:mb-1 2xl:mb-4">
          <h1 className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium">Done:</h1>
          <p className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-normal">{taskStats.completed}</p>
        </div>
        <div className="flex justify-between items-center mb-1 lg:mb-1 xl:mb-1 2xl:mb-4">
          <h1 className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium">Pending:</h1>
          <p className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-normal">{taskStats.pending}</p>
        </div>
        <div className="flex justify-between items-center">
          <h1 className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-medium">To Do:</h1>
          <p className="text-[#000000] text-xs lg:text-sm xl:text-base 2xl:text-lg font-normal">{taskStats.toDo}</p>
        </div>
      </div>
    </div>
  );
};

export default TaskRecord;