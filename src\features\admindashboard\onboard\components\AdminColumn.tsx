"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { HiDotsHorizontal } from "react-icons/hi";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ResetPasswordModal from "./modal/resetpasswordmodal";
// import SuspendModal from "./modal/suspendmodal";
import DeleteModal from "./modal/deletemodal";
import ReactiveModal from "./modal/reactivemodal";
import { Annotator } from "@/types/onboarding.types";
import EditModal from "./modal/editmodal";
import SuspendModalAdminlist from "./modal/suspendmodal";

// useAdminColumns hook definition, with optional pageIndex and pageSize
export const useAdminColumns = ({
  pageIndex = 0,
  pageSize = 10,
}: {
  pageIndex?: number;
  pageSize?: number;
} = {}): ColumnDef<Annotator>[] => {
  return [
    {
      accessorKey: "id",
      header: () => (
        <div className="w-[50px] cursor-pointer text-[14px] font-medium">
          S.No.
        </div>
      ),
      cell: ({ row }) => {
        // Calculate continuous serial number: (pageIndex * pageSize) + row.index + 1
        const serialNumber = pageIndex * pageSize + row.index + 1;
        // Debug log to verify serial number calculation
        console.log(
          `Row ${row.index}: serialNumber = (${pageIndex} * ${pageSize}) + ${row.index} + 1 = ${serialNumber}`
        );
        return (
          <div className="text-[14px] font-normal pl-4">{serialNumber}</div>
        );
      },
    },
    {
      accessorKey: "name",
      header: () => (
        <div className="w-[150px] text-[14px] font-medium cursor-pointer">
          First Name
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[14px] font-normal">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "lastname",
      header: () => (
        <div className="w-[100px] text-[14px] font-medium cursor-pointer">
          Last Name
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-[14px] font-normal">{row.getValue("lastname")}</div>
      ),
    },
    {
      accessorKey: "role",
      header: () => (
        <Button variant="ghost">Role</Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4 text-[14px] font-normal">{row.getValue("role")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Email
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4 text-[14px] font-normal">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "package",
      header: () => (
        <Button variant="ghost" className="text-[14px] w-[80px] font-medium">
          Package
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-4 text-[14px] font-normal">
          {row.original.Package?.name || "-"}
        </div>
      ),
    },
 {
      accessorKey: "accountStatus",
      header: () => (
        <Button variant="ghost" className="text-[14px] w-[80px] font-medium">
          Status
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue("accountStatus") as string;
        const statusStyles = {
          active: "bg-green-500 text-white px-[33px]",
          suspended: "bg-red-500 text-white ",
        };
        const baseStyles = "px-4 py-1  rounded-md text-center w-[80px]"; // Consistent width

        return (
          <div className=" text-[14px] font-normal">
            <span
              className={`${baseStyles} ${
                status.toLowerCase() === "active"
                  ? statusStyles.active
                  : statusStyles.suspended
              }`}
            >
              {status}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: () => (
        <Button variant="ghost" className="text-[14px] font-medium">
          Joined on
        </Button>
      ),
      cell: ({ row }) => {
        // Format date as dd/mm/yy
        const dateValue = row.getValue("createdAt");
        let formattedDate = (dateValue as string) || "";

        if (dateValue && typeof dateValue === "string") {
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            // Format as dd/mm/yy
            const day = date.getDate().toString().padStart(2, "0");
            const month = (date.getMonth() + 1).toString().padStart(2, "0");
            const year = date.getFullYear().toString().slice(-2);
            formattedDate = `${day}/${month}/${year}`;
          }
        }

        return (
          <div className="pl-4 text-[14px] font-normal">{formattedDate}</div>
        );
      },
    },
  {
  accessorKey: "actions",
  header: () => <div className="text-[14px] font-medium">Actions</div>,
  cell: ({ row }) => {
    const rowData = row.original;
    console.log("Row data:", rowData);
    const isActive = rowData.accountStatus.toLowerCase() === "active";

    return (
      <div className="pl-4 text-[14px] font-normal">
        <DropdownMenu>
          <DropdownMenuTrigger asChild className="outline-none">
            <Button variant="ghost" className="px-7 rounded-xl outline-none">
              <HiDotsHorizontal className="text-xl" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-[12.3rem] flex flex-col gap-y-3 p-3 font-poppins font-bold rounded-2xl">
            <EditModal userData={row.original} />
            <ResetPasswordModal userId={row.original.id} />
            {isActive ? (
              <SuspendModalAdminlist userId={row.original.id} />
            ) : (
              <ReactiveModal userId={row.original.id} />
            )}
            <DeleteModal userId={row.original.id} />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  },
}
  ];
};