import { DataTable } from "@/components/globalfiles/data.table";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate, useLocation } from "react-router-dom";
import { useClientColumns } from "./clientcolumn";
import { useState, useEffect } from "react";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useCoordinatorClientProjects } from "../api/useCoordinatorClientProjects";

const CoordinatorClientProjects = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [clientName, setClientName] = useState<string>("Client");

  // Get the columns configuration
  const columns = useClientColumns();

  // Get client ID from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const clientId = queryParams.get('id');
  const clientNameParam = queryParams.get('name');

  // Set client name if available in URL params
  if (clientNameParam && clientName === "Client") {
    setClientName(clientNameParam);
  }

  // Fetch projects data using React Query
  const { data, isLoading, error } = useCoordinatorClientProjects(clientId || "");

  // Process the data
  let projects: any[] = [];

  if (data) {
    console.log("Client projects response:", data);

    // Check different possible response structures
    if (data?.data?.data && Array.isArray(data.data.data)) {
      console.log("Found data in data.data.data");
      projects = data.data.data;
    } else if (data?.data && Array.isArray(data.data)) {
      console.log("Found data in data.data");
      projects = data.data;
    } else if (Array.isArray(data)) {
      console.log("Found data in data");
      projects = data;
    } else {
      console.log("No projects data found in response");
    }
  }

  // Auto-redirect to project details if there's only one project
  useEffect(() => {
    if (!isLoading && projects.length === 1 && projects[0].id) {
      console.log("Only one project found, redirecting to project details");
      const id = projects[0].id;
      console.log("Navigating to project details with ID:", id);
      navigate(`/coordinator/coordinatorproject-details?id=${id}`);
    }
  }, [projects, navigate, isLoading]);

  // If client ID is missing, show error
  if (!clientId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>Missing client ID. Please select a client first.</p>
          <button
            onClick={() => navigate('/coordinator/projectdetails/clients')}
            className="mt-4 px-4 py-2 bg-[#FF577F] text-white rounded-md hover:bg-[#ff3c6a]"
          >
            Go to Clients
          </button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  // Don't show error state, just treat it as empty data
  // This ensures the table headers are always shown
  if (error) {
    console.error("Error fetching client projects:", error);
  }

  // If there's only one project, redirect to project details
  if (projects.length === 1) {
    const projectId = projects[0].id;
    if (projectId) {
      console.log("Only one project found, redirecting to project details");
      navigate(`/coordinator/coordinatorproject-details?id=${projectId}`);
      return <BrandedGlobalLoader isLoading={true} />;
    }
  }

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />

        <h1 className="text-[#282828] text-[24px]">{clientName}'s Projects</h1>
      </div>

      <DataTable
        title="Projects"
        columns={columns}
        data={projects.length === 0 ? [{}] : projects}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default CoordinatorClientProjects;
