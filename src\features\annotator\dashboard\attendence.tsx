// import { useState } from "react";
// import Break from "./component/common/breakandleave";
// import TimeInOut from "./component/common/TimeIn";
// import { ChevronDown } from "lucide-react";
// import {
//   useGetAttendenceLog,
//   useInfiniteAttendanceLogs,
// } from "../annonator_api/annotator.query";
// import BrandedGlobalLoader from "@/components/loaders/loaders.one";

// const AttendanceAnnotator = () => {
//   const { data, isLoading } = useGetAttendenceLog();
//   const {
//     data: attendanceLog,
//     fetchNextPage,
//     hasNextPage,
//     isFetchingNextPage,
//     isLoading: attendanceLoading,
//   } = useInfiniteAttendanceLogs({
//     limit: 10,
//   });

//   const [fromDate, setFromDate] = useState("");
//   const [toDate, setToDate] = useState("");
//   const attendanceLogs =
//     attendanceLog?.pages.flatMap((page) => page.data.data) ?? [];

//   if (isLoading || attendanceLoading) {
//     return <BrandedGlobalLoader isLoading={attendanceLoading || isLoading} />;
//   }

//   return (
//     <div className="p-6 text-sm">
//       <h1 className="text-2xl font-bold mb-6">Welcome, Emanuel John</h1>

//       <div className="flex justify-between gap-6 mb-8">
//         <div className="p-4 rounded-xl w-[328px] border border-blue-400 shadow">
//           <h2 className="text-lg font-semibold  text-red-500">Enamuel Sah</h2>
//           <p className="text-gray-700 text-sm font-semibold mt-2">
//             Lorem ipsum dolor sit amet consectetur. Tellus mi quam cras etiam
//             volutpat mauris tincidunt aenean.
//           </p>
//         </div>

//         <Break data={data} />
//         <TimeInOut Tittle="Clock In-Out" data={data} />
//       </div>
//       <div className="flex items-center justify-end space-x-2 text-sm">
//         <p className="font-medium font-poppins text-[15px] text-[#1F4062]">
//           Show Entries
//         </p>

//         <div className="flex items-center space-x-2 border border-[#FF577F] rounded-md px-1 py-1">
//           <span className="text-slate-800">From:</span>
//           <div className="relative">
//             <input
//               type="date"
//               className="px-2 py-1 w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
//               placeholder="dd/mm/yyyy"
//               value={fromDate}
//               onChange={(e) => setFromDate(e.target.value)}
//             />
//             <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
//           </div>

//           <span className="text-slate-800">To:</span>
//           <div className="relative">
//             <input
//               type="date"
//               className="px-2 py-1 w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
//               placeholder="dd/mm/yyyy"
//               value={toDate}
//               onChange={(e) => setToDate(e.target.value)}
//             />
//             <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
//           </div>
//         </div>
//       </div>

//       <div className="overflow-auto rounded-xl border border-pink-300">
//         <table className="min-w-full text-center">
//           <thead className="bg-pink-100 text-pink-700">
//             <tr>
//               <th className="p-3">Date</th>
//               <th className="p-3">Time In</th>
//               <th className="p-3">Time Out</th>
//               <th className="p-3">Arrival</th>
//               <th className="p-3">Break Hours</th>
//               <th className="p-3">Working Hours</th>
//             </tr>
//           </thead>
//           <tbody>
//             {attendanceLogs.map((entry, index) => (
//               <tr
//                 key={index}
//                 className="border-t text-gray-800 hover:bg-gray-50 transition"
//               >
//                 <td className="p-3">{entry.date}</td>
//                 <td className="p-3 flex items-center justify-center gap-2">
//                   <span
//                     className={`w-2 h-2 rounded-full ${
//                       entry.arrival ? "bg-red-500" : "bg-green-500"
//                     }`}
//                   ></span>
//                   {entry.timeIn}
//                 </td>
//                 <td className="p-3 text-green-600">{entry.timeOut ?? "N/A"}</td>
//                 <td className="p-3 text-green-600">{entry.arrival}</td>
//                 <td className="p-3 text-pink-500">{entry.breakHours}</td>
//                 <td className="p-3 text-red-500">{entry.workingHours}</td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//     </div>
//   );
// };

// export default AttendanceAnnotator;

import { useEffect, useRef, useState } from "react";
import Break from "./component/common/breakandleave";
import TimeInOut from "./component/common/TimeIn";
import { ChevronDown } from "lucide-react";
import {
  useGetAttendenceLog,
  useInfiniteAttendanceLogs,
} from "../annonator_api/annotator.query";
import { useUserProfile } from "../annonator_api/user.query";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useDebounce } from "@/components/globalfiles/debounce";
import { FaEdit } from "react-icons/fa";
import DescriptionModal from "./component/modal/descriptionmodal";

// Format date to dd/mm/yyyy
const formatDate = (dateString: string) => {
  if (!dateString) return "N/A";

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "Invalid Date";

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

// Format time to hours:minutes:seconds
const formatTime = (timeString: string) => {
  if (!timeString) return "N/A";

  const date = new Date(timeString);
  if (isNaN(date.getTime())) return "Invalid Time";

  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
};

const AttendanceAnnotator = () => {
  const { data, isLoading } = useGetAttendenceLog();
const { data: userProfileData, isLoading: userProfileLoading, refetch: refetchUserProfile } = useUserProfile();
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Debounced values
  const debouncedFromDate = useDebounce(fromDate, 500);
  const debouncedToDate = useDebounce(toDate, 500);
  const {
    data: attendanceLog,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: attendanceLoading,
    refetch,
  } = useInfiniteAttendanceLogs({
    limit: 10,
    from: debouncedFromDate,
    to: debouncedToDate,
  });

  const observerRef = useRef<HTMLDivElement | null>(null);

  const attendanceLogs =
    attendanceLog?.pages.flatMap((page) => page.data.data) ?? [];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage();
        }
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) observer.unobserve(observerRef.current);
    };
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
      console.warn("Invalid date range");
    }
  }, [debouncedFromDate, debouncedToDate]);

  useEffect(() => {
    if (
      debouncedFromDate &&
      debouncedToDate &&
      new Date(debouncedFromDate) <= new Date(debouncedToDate)
    ) {
      refetch();
    }
  }, [debouncedFromDate, debouncedToDate, refetch]);

  if (isLoading || attendanceLoading || userProfileLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  return (
    <div className="p-4 lg:p-6 xl:p-6 2xl:p-6 text-sm">
      <h1 className="text-2xl font-bold mb-6">Welcome, {userProfileData?.data?.name || "User"}</h1>

      <div className="flex flex-col lg:flex-row xl:flex-row 2xl:flex-row justify-between gap-6 mb-8">
        <div className="p-4 rounded-xl w-full lg:w-[328px] xl:w-[328px] 2xl:w-[328px] border-gradient shadow-lg">
          <div className="flex flex-row justify-between items-center">
            <h2 className="text-lg font-semibold text-red-500">{userProfileData?.data?.name.split(" ").slice(0, 2).join(" ")}</h2>
            <p className="text-sm text-gray-700">
              <FaEdit
                className="text-xl hover:text-[#FF577F] cursor-pointer"
                onClick={() => setIsModalOpen(true)} // Open modal on click
              />
            </p>
          </div>
          <p className="text-gray-700 text-sm font-semibold mt-2">
            {userProfileData?.data?.description || "Add your description"}
          </p>
        </div>

        <Break data={data} />
        <TimeInOut Tittle="Clock In-Out" data={data} />
      </div>
      {/* Modal for updating description */}
      <DescriptionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currentDescription={userProfileData?.data?.description || ""}
        refetch={refetchUserProfile} // Pass refetch function
      />

      {/* Date Filter */}
      <div className="flex flex-col lg:flex-row xl:flex-row 2xl:flex-row items-start lg:items-center xl:items-center 2xl:items-center justify-between lg:justify-end xl:justify-end 2xl:justify-end space-y-2 lg:space-y-0 xl:space-y-0 2xl:space-y-0 lg:space-x-2 xl:space-x-2 2xl:space-x-2 text-sm">
        <p className="font-medium font-poppins text-[15px] text-[#1F4062]">
          Show Entries
        </p>

        <div className="flex flex-wrap lg:flex-nowrap xl:flex-nowrap 2xl:flex-nowrap items-center space-x-2 border border-[#FF577F] rounded-md px-1 py-1">
          <span className="text-slate-800">From:</span>
          <div className="relative">
            <input
              type="date"
              className="px-2 py-1 w-[120px] lg:w-[150px] xl:w-[150px] 2xl:w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
            />
            <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
          </div>

          <span className="text-slate-800">To:</span>
          <div className="relative">
            <input
              type="date"
              className="px-2 py-1 w-[120px] lg:w-[150px] xl:w-[150px] 2xl:w-[150px] text-slate-700 placeholder:text-slate-400 focus:outline-none"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
            />
            <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 text-slate-400 w-4 h-4 pointer-events-none" />
          </div>
        </div>
      </div>

      {/* Attendance Table */}
      <div className="overflow-auto rounded-xl border border-pink-300 mt-4">
        <table className="min-w-full text-center text-xs lg:text-sm xl:text-sm 2xl:text-sm">
          <thead className="bg-pink-100 text-pink-700">
            <tr>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Date</th>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Time In</th>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Time Out</th>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Arrival</th>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Break Hours</th>
              <th className="p-2 lg:p-3 xl:p-3 2xl:p-3">Working Hours</th>
            </tr>
          </thead>
          <tbody>
            {attendanceLogs.map((entry, index) => (
              <tr
                key={index}
                className="border-t text-gray-800 hover:bg-gray-50 transition"
              >
                {/* Format date as dd/mm/yyyy */}
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3">{formatDate(entry.date)}</td>
                {/* Format time as hours:minutes:seconds */}
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3 flex items-center justify-center gap-2">
                  <span
                    className={`w-2 h-2 rounded-full ${entry.arrival ? "bg-red-500" : "bg-green-500"
                      }`}
                  ></span>
                  {formatTime(entry.timeIn)}
                </td>
                {/* Format time as hours:minutes:seconds */}
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3 text-green-600">{entry.timeOut ? formatTime(entry.timeOut) : "N/A"}</td>
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3 text-green-600">{entry.arrival}</td>
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3 text-pink-500">{entry.breakHours}</td>
                <td className="p-2 lg:p-3 xl:p-3 2xl:p-3 text-red-500">{entry.workingHours}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Infinite Loader Trigger */}
        <div ref={observerRef} className="p-4 text-center text-gray-500">
          {isFetchingNextPage
            ? "Loading more entries..."
            : hasNextPage
              ? "Scroll to load more"
              : "No more entries"}
        </div>
      </div>
    </div>
  );
};

export default AttendanceAnnotator;
