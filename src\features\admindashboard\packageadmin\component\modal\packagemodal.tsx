import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import { useState } from "react";
import { createPackageApi } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package";
import { BillingType } from "../package/api_package/packagetype";
import { toast } from "react-toastify";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

const billingOptions = [
  { label: "Monthly", value: "MONTHLY" },
  //   { label: "Yearly", value: "YEARLY" },
];

//Manage the state the status in the new package


const PackageModal = () => {

  const [isSelectOpen, setIsSelectOpen] = useState(false);
  const [featureTitle, setFeatureTitle] = useState("");
  const [originalPrice, setOriginalPrice] = useState("");
  const [discountPrice, setDiscountPrice] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [billingType, setBillingType] = useState<BillingType>("MONTHLY");

  // Query client for data invalidation
  const queryClient = useQueryClient();

  // Create package mutation
  const { mutate: createPackage, isPending: isSubmitting } = useMutation({
    mutationFn: (packageData: any) => createPackageApi(packageData),
    onMutate: () => {
      // Show loading toast with a progress bar
      const toastId = toast.loading(
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#FF577F]"></div>
          <span className="font-medium">Creating package...</span>
        </div>,
        {
          position: "top-right",
          closeOnClick: false,
          closeButton: false,
          draggable: false,
          progressClassName: "bg-[#FF577F]",
          className: "bg-white shadow-lg rounded-md border border-gray-100",
        });
      return toastId;
    },
    onSuccess: (_, __, toastId) => {
      if (toastId) {
        // Update toast to success
        toast.update(toastId, {
          render: (
            <div className="flex items-center">
              <div className="flex items-center justify-center rounded-full  p-1">
                <svg className="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                </svg>
              </div>
              <span className="font-medium">Package created successfully!</span>
            </div>
          ),
          type: "success",
          isLoading: false,
          autoClose: 3000,
          closeButton: true,
          closeOnClick: true,
          draggable: true,
          progressClassName: "bg-green-500",
          className: "bg-white shadow-lg rounded-md border border-gray-100",
        });
      }

      // Refresh the package list data
      queryClient.invalidateQueries({ queryKey: ["packageList"] });

      // Close the dialog automatically
      const closeButton = document.querySelector('[data-dialog-close]') as HTMLElement;
      if (closeButton) closeButton.click();
    },
    onError: (error, _, toastId) => {
      console.error("Error creating package", error);

      if (toastId) {
        // Update toast to error
        toast.update(toastId, {
          render: (
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center rounded-full bg-red-100 p-1">
                <svg className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <span className="font-medium">Failed to create package. Please try again.</span>
            </div>
          ),
          type: "error",
          isLoading: false,
          autoClose: 3000,
          closeButton: true,
          closeOnClick: true,
          draggable: true,
          progressClassName: "bg-red-500",
          className: "bg-white shadow-lg rounded-md border border-gray-100",
        });
      }
    }
  });

  const handleSave = () => {
    if (!featureTitle || !originalPrice || !selectedCategory) return;

    // Call the mutation
    createPackage({
      name: featureTitle,
      description: featureTitle,
      price: Number(originalPrice), // Always use original price
      billingType,
      isActive: selectedCategory === "active",
    });
  };

  return (
    <div className="w-full flex flex-col lg-only:gap-3 xl-only:gap-5 2xl-only:gap-6 ">
      <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4">Add New Package</h2>

      <div className="flex flex-col justify-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4">
        <div className="flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Package Name*</Label>
          <div className="border-gradient rounded-lg outline-none pb-[1px]">
            <Input
              placeholder="Enter feature title"
              value={featureTitle}
              onChange={(e) => setFeatureTitle(e.target.value)}
              className="bg-[#F9EFEF] outline-none text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
            />
          </div>
        </div>

        <div className="flex lg-only:gap-x-3 xl-only:gap-x-4 2xl-only:gap-x-5">
          <div className="w-full flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2">
            <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Original Price*</Label>
            <div className="border-gradient rounded-lg">
              <Input
                placeholder="Enter original price"
                value={originalPrice}
                onChange={(e) => setOriginalPrice(e.target.value)}
                className="bg-[#F9EFEF] no-spinner outline-none focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 text-[#5E5E5E] lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
                type="number"
              />
            </div>
          </div>

          <div className="w-full flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2">
            <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Discount Price (optional)</Label>
            <div className="border-gradient rounded-lg">
              <Input
                placeholder="Enter discount price"
                value={discountPrice}
                onChange={(e) => setDiscountPrice(e.target.value)}
                className="no-spinner bg-[#F9EFEF] text-[#5E5E5E] outline-none focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
                type="number"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Billing Type</Label>
          <div className="border-gradient rounded-lg p-[1px]">
            <Select
              value={billingType}
              onValueChange={(value) => setBillingType(value as BillingType)}
            >
              <SelectTrigger className="w-full bg-[#F9EFEF] text-[#5E5E5E] outline-none focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2">
                <SelectValue placeholder="Select Billing Type" />
              </SelectTrigger>
              <SelectContent className="z-50 lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
                {billingOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex flex-col lg-only:gap-y-1 xl-only:gap-y-1.5 2xl-only:gap-y-2">
          <Label className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base">Status</Label>
          <div
            className="modal-body border-gradient rounded-lg"              // ← your existing modal wrapper class
            onClick={() => isSelectOpen && setIsSelectOpen(false)}
          >
            {/* 3️⃣  unchanged Select, but now it’s a *controlled* component   */}
            <Select
              value={selectedCategory || ""}
              onValueChange={(v) => {
                setSelectedCategory(v);          // keep your existing behaviour
                setIsSelectOpen(false);          // also close after choosing
              }}
              open={isSelectOpen}               // controlled
              onOpenChange={setIsSelectOpen}    // controlled
            >
              {/* 4️⃣  stop the click that *opens* Select from bubbling up     */}
              <SelectTrigger
                onClick={(e) => e.stopPropagation()}
                className="w-full bg-[#F9EFEF] text-[#5E5E5E] outline-none focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 lg-only:text-xs lg-only:h-8 xl-only:text-sm 2xl-only:text-base lg-only:py-1 xl-only:py-1.5 2xl-only:py-2"
              >
                <SelectValue placeholder="Select Status" />
              </SelectTrigger>

              {/* 5️⃣  same menu; no structural changes                         */}
              <SelectContent
                onClick={(e) => e.stopPropagation()}    // so menu clicks don’t bubble
                className="z-50 lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
              >
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="lg-only:py-2 xl-only:py-2.5 2xl-only:py-3 flex justify-end lg-only:gap-1 xl-only:gap-1.5 2xl-only:gap-2">
        <Button
          variant="gradient"
          onClick={handleSave}
          className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 lg-only:text-xs xl-only:text-sm 2xl-only:text-base"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Save"}
        </Button>
        <DialogClose data-dialog-close>
          <Button variant="ghost" className="lg-only:px-[2rem] lg-only:py-3 xl-only:px-[3rem] xl-only:py-5 2xl-only:px-[4rem] 2xl-only:py-6 border-gradient lg-only:text-xs xl-only:text-sm 2xl-only:text-base">
            Cancel
          </Button>
        </DialogClose>
      </div>
    </div>
  );
};

export default PackageModal;
