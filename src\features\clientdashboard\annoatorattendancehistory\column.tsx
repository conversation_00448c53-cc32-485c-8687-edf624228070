import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { AttendanceType } from "./attendace.type";

export const columns: ColumnDef<AttendanceType>[] = [
  {
    accessorKey: "date",
    header: () => {
      return (
        <div
          className="flex flex-row text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
        </div>
      );
    },
    cell: ({ row }) => <div className="text-[14px] font-normal">{row.getValue("date")}</div>,
  },
  {
    accessorKey: "timein",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Time In
        </Button>
      );
    },
    cell: ({ row }) => (
      <div className="pl-4 text-[14px] font-normal">{row.getValue("timein")}</div>
    ),
  },
  {
    accessorKey: "timeout",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
          // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Time Out
        </Button>
      );
    },
    cell: ({ row }) => <div className="pl-4 text-[14px] font-normal">{row.getValue("timeout")}</div>,
  },
  {
    accessorKey: "arrival",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Arrival
        </Button>
      );
    },
    cell: ({ row }) => {
      const arrival = row.getValue("arrival") as string;
      const isOnTime = arrival === "On time";

      return (
        <div className="pl-4 flex items-center gap-2 text-[14px] font-normal">
          <div className={`w-3 h-3 rounded-full ${isOnTime ? 'bg-green-500' : 'bg-orange-500'}`} />
          {arrival}
        </div>
      );
    },
  },
  {
    accessorKey: "breakhours",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Break Hours
        </Button>
      );
    },
    cell: ({ row }) => <div className="pl-4 text-[14px] font-normal">{row.getValue("breakhours")}</div>,
  },
  {
    accessorKey: "workinghours",
    header: () => {
      return (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Working Hours
        </Button>
      );
    },
    cell: ({ row }) => <div className="pl-4 text-[14px] font-normal">{row.getValue("workinghours")}</div>,
  }
];