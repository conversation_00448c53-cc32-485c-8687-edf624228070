import React from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { countries, timeZones, formDefaultValues } from "./formprofiledata";
import { BackButton } from "@/_components/common";
import { ClockIcon } from "lucide-react";
import { FormProfileCreate } from "../formauth_api_all/formprofile_api";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const formSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  website: z
    .string()
    .transform((val) => {
      if (val.trim() === "") return "";
      return /^https?:\/\//i.test(val) ? val : `https://${val}`;
    })
    .refine(
      (val) => {
        if (val === "") return true;
        try {
          new URL(val);
          return true;
        } catch {
          return false;
        }
      },
      { message: "Please enter a valid URL" }
    )
    .refine(
      (val) =>
        val === "" ||
        /\.(com|in|org|net|io|co|ai|edu|gov|mil|biz|info|me|tv|us|uk|ca|au|nz|jp|cn|de|fr|ru|br|es|it|nl|se|ch|mx)(\/.*)?$/i.test(
          val
        ),
      { message: "URL must contain a valid domain extension" }
    ),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  phoneNumber: z
    .string()
    .min(1, "Phone number is required")
    .transform((val) => val.replace(/\D/g, "").trim())
    .refine((val) => /^\d+$/.test(val), {
      message: "Phone number must contain only numbers",
    })
    .refine((val) => val.length >= 8 && val.length <= 15, {
      message: "Phone number must be between 8-15 digits",
    }),
  timeZone: z.string().min(1, "Time zone is required"),
  address: z.string().min(1, "Address is required"),
  postalCode: z
    .string()
    .min(1, "Postal code is required")
    .refine((val) => /^\d+$/.test(val), {
      message: "Postal code must contain only numbers",
    }),
});

const ProfileForm = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [apiError, setApiError] = React.useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefaultValues,
    mode: "onChange", // Changed from "onBlur" to "onChange"
  });

  React.useEffect(() => {
    console.log("📝 ProfileForm component mounted");
    console.log("🔍 Current location state:", window.location);
    console.log("🔍 localStorage token:", !!localStorage.getItem("token"));
    console.log("🔍 localStorage user:", !!localStorage.getItem("user"));
  }, []);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setApiError(null);
      setIsSubmitting(true);

      const selectedCountry = countries.find((c) => c.value === values.country);
      const selectedTimeZone = timeZones.find(
        (tz) => tz.value === values.timeZone
      );

      const apiData = {
        companyName: values.companyName,
        phoneNumber: `${selectedCountry?.phoneCode || ""}${values.phoneNumber}`,
        website: values.website,
        timezone: selectedTimeZone?.label || values.timeZone,
        country: selectedCountry?.label || values.country,
        stateProvince: values.state,
        address: values.address,
        postalCode: values.postalCode,
      };

      console.log("Submitting to API:", apiData);
      const response = await FormProfileCreate(apiData);
      console.log("API Response:", response);
      navigate("/auth/plan-select");
    } catch (error: any) {
      console.error("API Error:", error);
      setApiError(
        error.response?.data?.message ||
          "Failed to save profile. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto lg-only:px-6 lg:py-8 xl-only:p-6">
      <div className="flex items-center mb-2">
        <BackButton />
        <h1 className="text-2xl font-semibold">Complete Profile Details</h1>
      </div>

      {apiError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {apiError}
        </div>
      )}

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="lg:space-y-1 xl-only:space-y-4"
          noValidate
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Left Column */}
            <div className="lg:space-y-2 xl:space-y-4">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <div
                        className={`border-gradient rounded-lg ${
                          fieldState.error ? "border-red-500" : ""
                        }`}
                      >
                        <Input
                          placeholder="Enter company name"
                          {...field}
                          className="bg-[#F9EFEF]"
                          disabled={isSubmitting}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        form.setValue("phoneNumber", "");
                      }}
                      value={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger
                          className={`w-full border-gradient  bg-[#F9EFEF] ${
                            fieldState.error ? "border-red-500" : ""
                          }`}
                        >
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[180px] overflow-y-auto">
                        {countries.map((country) => (
                          <SelectItem key={country.value} value={country.value}>
                            {country.label} ({country.phoneCode})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timeZone"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Time Zone</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger
                          className={`w-full border-gradient bg-[#F9EFEF] ${
                            fieldState.error ? "border-red-500" : ""
                          }`}
                        >
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[180px] overflow-y-auto">
                        {timeZones.map((tz) => (
                          <SelectItem key={tz.value} value={tz.value}>
                            <div className="flex items-center">
                              <ClockIcon className="mr-2 h-4 w-4" />
                              {tz.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="state"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>State/Province</FormLabel>
                    <FormControl>
                      <div
                        className={`border-gradient rounded-lg ${
                          fieldState.error ? "border-red-500" : ""
                        }`}
                      >
                        <Input
                          placeholder="Enter state/province"
                          {...field}
                          className="bg-[#F9EFEF]"
                          disabled={isSubmitting}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Right Column */}

            <div className="lg:space-y-2 xl:space-y-4">
              <FormField
                control={form.control}
                name="website"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <div
                        className={`border-gradient rounded-lg ${
                          fieldState.error ? "border-red-500" : ""
                        }`}
                      >
                        <Input
                          placeholder="https://example.com"
                          {...field}
                          className="bg-[#F9EFEF]"
                          disabled={isSubmitting}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <div className="flex items-center gap-2">
                      <div className="w-24 border-gradient rounded-lg">
                        <Input
                          value={
                            countries.find(
                              (c) => c.value === form.watch("country")
                            )?.phoneCode || "+"
                          }
                          disabled
                          className="bg-[#F9EFEF]"
                        />
                      </div>
                      <FormControl>
                        <div
                          className={`border-gradient rounded-lg w-full ${
                            fieldState.error ? "border-red-500" : ""
                          }`}
                        >
                          <Input
                            placeholder="Enter Contact Number"
                            {...field}
                            className="bg-[#F9EFEF]"
                            disabled={isSubmitting}
                            onChange={(e) => {
                              const value = e.target.value.replace(/\D/g, "");
                              field.onChange(value);
                            }}
                          />
                        </div>
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <div
                        className={`border-gradient rounded-lg ${
                          fieldState.error ? "border-red-500" : ""
                        }`}
                      >
                        <Input
                          placeholder="Enter full address"
                          {...field}
                          className="bg-[#F9EFEF]"
                          disabled={isSubmitting}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="postalCode"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Postal Code</FormLabel>
                    <FormControl>
                      <div
                        className={`border-gradient rounded-lg ${
                          fieldState.error ? "border-red-500" : ""
                        }`}
                      >
                        <Input
                          placeholder="Enter postal code"
                          {...field}
                          className="bg-[#F9EFEF]"
                          disabled={isSubmitting}
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, "");
                            field.onChange(value);
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <Button
              variant={"gradient"}
              type="submit"
              className="px-14 py-5"
              disabled={isSubmitting || !form.formState.isValid}
            >
              {isSubmitting ? "Submitting..." : "Submit →"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ProfileForm;
