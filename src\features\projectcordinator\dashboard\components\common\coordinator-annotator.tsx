import React, { useEffect, useState } from "react";
import { getCoordinatorAnnonators } from "@/features/projectcordinator/api/api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { Link } from "react-router-dom";

type AnnotatorStatus = "active" | "inactive" | "pending" | "suspended";

type Annotator = {
  id: string;
  name: string;
  projects: number;
  status: AnnotatorStatus;
};

const statusColors: Record<string, string> = {
  active: "bg-green-500",
  inactive: "bg-red-500",
  pending: "bg-blue-500",
  suspended: "bg-yellow-500"
};

const CoordinatorAnnotator: React.FC = () => {
  const [annotators, setAnnotators] = useState<Annotator[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnnotators = async () => {
      try {
        const response = await getCoordinatorAnnonators();

        // Transform the API response to match our component's data structure
        const transformedAnnotators = response.data.data.map((annotator: any) => ({
          id: annotator.id,
          name: annotator.developer?.name || "Unknown Annotator",
          projects: annotator.developer?._count?.projectsAssigned || 0,
          status: (annotator.developer?.accountStatus?.toLowerCase() || "inactive") as AnnotatorStatus
        }));

        setAnnotators(transformedAnnotators);
      } catch (error) {
        console.error("Error fetching annotators:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnotators();
  }, []);

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg lg-only:p-3 xl-only:p-4 2xl-only:p-5 lg-only:w-full xl-only:w-full 2xl-only:w-full lg-only:h-[150px] xl-only:h-[186px] 2xl-only:h-[200px] flex flex-col">
      <div className="flex justify-between items-center lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4">
        <h2 className="lg-only:text-base xl-only:text-lg 2xl-only:text-xl font-semibold font-poppins">Annotators</h2>
       <Link to="/coordinator/projectdetails/annotators">
        <button className="lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm font-semibold lg-only:px-2 lg-only:py-0.5 xl-only:px-3 xl-only:py-1 2xl-only:px-4 2xl-only:py-1.5 border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all">
          View All
        </button>
        </Link>
      </div>

      <ul className="overflow-y-auto pr-1">
        {loading ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">Loading annotators...</p>
        ) : annotators.length === 0 ? (
          <p className="lg-only:text-xs xl-only:text-sm 2xl-only:text-base text-gray-500">No annotators found</p>
        ) : (
          annotators.map((annotator, index) => (
            <li key={annotator.id || index} className="flex items-center lg-only:gap-2 xl-only:gap-3 2xl-only:gap-4 lg-only:mb-2 xl-only:mb-3 2xl-only:mb-4 last:mb-0">
              <img
                src={getAvatarUrl(annotator.name)}
                alt="annotator avatar"
                className="lg-only:w-5 lg-only:h-5 xl-only:w-6 xl-only:h-6 2xl-only:w-7 2xl-only:h-7 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="font-medium lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm text-[#282828]">
                  {annotator.name.split(" ").slice(0, 1).join(" ")}
                </p>
                <p className="lg-only:text-[8px] xl-only:text-[10px] 2xl-only:text-xs text-[#727272]">
                  {annotator.projects} {annotator.projects === 1 ? "project" : "projects"}
                </p>
              </div>
              <span
                className={`text-white lg-only:text-[8px] xl-only:text-[10px] 2xl-only:text-xs lg-only:px-1.5 lg-only:py-0.5 xl-only:px-2 xl-only:py-0.5 2xl-only:px-2.5 2xl-only:py-1 rounded-full capitalize ${
                  statusColors[annotator.status]
                }`}
              >
                {annotator.status}
              </span>
            </li>
          ))
        )}
      </ul>
    </div>
  );
};

export default CoordinatorAnnotator;
