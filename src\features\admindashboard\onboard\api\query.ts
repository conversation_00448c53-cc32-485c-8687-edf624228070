// hooks/useProductQuery.ts
import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { getOnboardingList, getPackageList } from "./onboarding.api";
// import { fetchProducts, fetchProductById } from "../queries/query";

export const useOnboardingList = ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ["onboarding", { page, limit }],
    queryFn: () =>
      getOnboardingList({
        page,
        limit,
      }),
    // staleTime: 1000 * 60 * 5, // 5 minutes
    // cacheTime: 1000 * 60 * 10, // 10 minutes
    // refetchOnWindowFocus: false,
    // refetchOnMount: false,
    // refetchInterval: 1000 * 60, // every minute
  });
};

export const usePackageList = () => {
  return useInfiniteQuery({
    queryKey: ["packages"],
    queryFn: ({ pageParam = 1 }) => getPackageList({ pageParam }),
    initialPageParam: 1, // ✅ must pass this
    getNextPageParam: (lastPage) => {
      if (lastPage.currentPage < lastPage.totalPages) {
        return lastPage.currentPage + 1;
      }
      return undefined;
    },
  });
};
