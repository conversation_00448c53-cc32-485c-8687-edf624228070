import { useState, useEffect } from "react";
import { SquarePlus } from "lucide-react";
import ProjectCreate from "./components/project-create";
import CustomToast from "@/_components/common/customtoast";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { useNavigate } from "react-router-dom";
import {
  getAllClientCooworkerProjects,
  getAllClientProjects,
} from "./project_api/Project_api";
import calender from "@/assets/icons/clenderclient.svg";
import clock from "@/assets/icons/clockclient.svg";
import profile from "@/assets/icons/clientprofile.svg";
import checked from "@/assets/icons/checkedcleint.svg";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { RootState } from "@/store";
// import { useHasAccess } from "@/utils/role-checks";

// Define the Project type used in the component
interface Project {
  id: string; // Changed from number to string to match API response
  title: string;
  description: string;
  startDate: string;
  duration: string;
  postedBy: string;
  status: "Ongoing" | "Completed" | "Upcoming";
  level: "low" | "medium" | "hard";
}

// Define the API response project type
interface ApiProject {
  id: string;
  name: string;
  description: string;
  priority: "LOW" | "MEDIUM" | "HIGH" | string;
  status: string;
  startDate: string;
  dueDate: string;
  createdBy: {
    name: string;
  };
}

export default function ClientProjects() {
  const user = useAppSelector((state: RootState) => state.auth.user?.role);
  const [showToast, setShowToast] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0); // Used to trigger a refresh
  const navigate = useNavigate();

  console.log(user, "mangaread");

  // Log any errors to the console
  if (error) {
    console.error("Project loading error:", error);
  }

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null); // Clear any previous errors

        console.log("Fetching projects from API...");
        const response = await (user === "CLIENT"
          ? getAllClientProjects()
          : getAllClientCooworkerProjects());
        console.log("API Response:", response);

        if (response && response.data && Array.isArray(response.data)) {
          // Map API response to our Project type with additional error handling
          const formattedProjects = response.data
            .filter((project: ApiProject) => project && project.id) // Filter out any null or undefined projects or those without IDs
            .map((project: ApiProject) => {
              try {
                return {
                  id:
                    project.id ||
                    `project-${Math.random().toString(36).substring(2, 11)}`, // Ensure ID is never empty
                  title: project.name || "Untitled Project",
                  description:
                    project.description || "No description available",
                  startDate: formatDate(project.startDate),
                  duration: calculateDuration(
                    project.startDate,
                    project.dueDate
                  ),
                  postedBy: project.createdBy?.name || "Unknown",
                  status: formatStatus(project.status),
                  level: getPriorityLevel(project.priority),
                };
              } catch (err) {
                console.error("Error formatting project:", err, project);
                // Return a fallback project with the ID if possible
                return {
                  id:
                    project.id ||
                    `error-${Math.random().toString(36).substring(2, 11)}`,
                  title: project.name || "Error Loading Project",
                  description:
                    "There was an error loading this project's details",
                  startDate: "Unknown",
                  duration: "Unknown",
                  postedBy: "Unknown",
                  status: "Upcoming" as const,
                  level: "medium" as const,
                };
              }
            });

          console.log("Formatted projects:", formattedProjects);
          setProjects(formattedProjects);
        } else {
          console.error("Invalid API response format:", response);
          setError("Invalid data format received from API");
        }
      } catch (error) {
        console.error("Failed to fetch projects:", error);
        setError("Failed to load projects. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [refreshTrigger]); // Re-fetch when refreshTrigger changes

  // Helper functions for data formatting
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "Not set";

    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "Invalid date";
      }

      const day = String(date.getDate()).padStart(2, "0");
      const month = date.toLocaleString("default", { month: "long" }); // Full month name
      const year = date.getFullYear().toString().slice(2, 4);
      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error("Error formatting date:", e, dateString);
      return "Invalid date";
    }
  };

  const calculateDuration = (
    startDate: string | null | undefined,
    endDate: string | null | undefined
  ) => {
    if (!startDate || !endDate) return "Not set";

    try {
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Check if dates are valid
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return "Unknown";
      }

      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays <= 0) {
        return "Same day";
      } else if (diffDays <= 7) {
        return `${diffDays} day${diffDays > 1 ? "s" : ""}`;
      } else if (diffDays <= 30) {
        const weeks = Math.ceil(diffDays / 7);
        return `${weeks} week${weeks > 1 ? "s" : ""}`;
      } else {
        const months = Math.ceil(diffDays / 30);
        return `${months} month${months > 1 ? "s" : ""}`;
      }
    } catch (e) {
      console.error("Error calculating duration:", e, { startDate, endDate });
      return "Unknown";
    }
  };

  const formatStatus = (
    status: string | null | undefined
  ): "Ongoing" | "Completed" | "Upcoming" => {
    if (!status) return "Upcoming";

    const statusMap: Record<string, "Ongoing" | "Completed" | "Upcoming"> = {
      IN_PROGRESS: "Ongoing",
      COMPLETED: "Completed",
      PENDING: "Upcoming",
      ASSIGNED: "Upcoming",
    };

    return statusMap[status.toUpperCase()] || "Ongoing";
  };

  const getPriorityLevel = (
    priority: string | null | undefined
  ): "low" | "medium" | "hard" => {
    if (!priority) return "medium";

    const priorityMap: Record<string, "low" | "medium" | "hard"> = {
      LOW: "low",
      MEDIUM: "medium",
      HIGH: "hard",
    };

    return priorityMap[priority.toUpperCase()] || "medium";
  };

  // No longer need getStyles function as we'll use Tailwind's responsive classes directly

  const getLevelStyle = (level: Project["level"]) => {
    switch (level) {
      case "low":
        return "bg-blue-800";
      case "medium":
        return "bg-orange-500";
      case "hard":
        return "bg-red-500";
      default:
        return "bg-gray-400";
    }
  };

  const getLevelLetter = (level: Project["level"]) => {
    switch (level) {
      case "low":
        return "low";
      case "medium":
        return "Medium";
      case "hard":
        return "High";
      default:
        return "?";
    }
  };

  const handleSuccess = () => {
    // Show success toast
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
    }, 4000); // auto-close after 4s

    // Trigger a refresh of the projects list
    setRefreshTrigger((prev) => prev + 1);
  };

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="bg-red-50 border border-red-200 text-red-600 px-6 py-4 rounded-md">
          <h3 className="text-lg font-semibold mb-2">Error Loading Projects</h3>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  return (
    <div className="w-full  px-4 lg:px-6 xl:px-8 2xl:px-10">
      {/* Create Project Button */}
      {
        <div className="w-full flex justify-end items-end mb-4 lg:mb-4 xl:mb-5 2xl:mb-6">
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex flex-row items-center justify-center text-white font-medium rounded-lg transition-all bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] lg:gap-x-2 lg:px-5 lg:py-2 lg:text-lg xl:gap-x-2.5 xl:px-6 xl:py-2.5 xl:text-lg 2xl:gap-x-3 2xl:px-7 2xl:py-3 2xl:text-xl"
            disabled={loading}
          >
            <SquarePlus className="text-white lg:text-[50px] xl:text-[55px] 2xl:text-[60px]" />
            Create Project
          </button>
        </div>
      }

      {/* Projects List */}
      {projects.length > 0 ? (
        <div className="grid grid-cols-1 gap-4 py-2 lg:grid-cols-3 lg:gap-5 lg:py-2 xl:grid-cols-4 xl:gap-4 xl:py-2.5 2xl:grid-cols-5 2xl:gap-5 2xl:py-3">
          {projects.map((project) => (
            <div
              key={project.id}
              className="border border-[#FF577F] rounded-lg shadow-md w-full h-full lg:p-2 xl:p-3 2xl:p-4"
            >
              <div className="rounded-2xl flex flex-col lg:gap-y-3 lg:p-1 xl:gap-y-3 xl:p-1.5 2xl:gap-y-4 2xl:p-4">
                <div className=" flex flex-row justify-between lg:gap-x-6 xl:gap-x-7 2xl:gap-x-10">
                  <h2 className="font-semibold w-[90%] lg:text-[16px] xl:text-[17px] 2xl:text-[18px]">
                    {project.title.split(" ").slice(0, 2).join(" ")}
                  </h2>
                  {/* Level Circle */}

                  <div
                    className={`rounded-full w-[20%] flex items-center justify-center text-white font-bold lg:w-[50px] lg:h-6 lg:text-[9px] xl:w-[55px] xl:h-6.5 xl:text-[9.5px] 2xl:w-[60px] 2xl:h-7 2xl:text-[10px] ${getLevelStyle(
                      project.level
                    )}`}
                  >
                    {getLevelLetter(project.level)}
                  </div>
                </div>
                <p className="font-normal line-clamp-1 text-[#282828] lg:text-xs xl:text-xs 2xl:text-sm">
                  {project.description}
                </p>
                <div className="grid grid-cols-[auto_1fr] lg:text-xs lg:gap-1 xl:text-xs xl:gap-1.5 2xl:text-sm 2xl:gap-2">
                  <img src={calender} alt="calenders" className="w-5 h-5" />
                  <div className="grid grid-cols-2 w-full lg:pr-1 lg:gap-1 xl:pr-4 xl:gap-1.5 2xl:pr-4 2xl:gap-2">
                    <span className="font-medium text-[#282828] lg:text-xs xl:text-xs 2xl:text-sm">
                      Started:
                    </span>
                    <span className="text-right">{project.startDate}</span>
                  </div>
                </div>

                <div className="grid grid-cols-[auto_1fr] lg:text-xs lg:gap-1 xl:text-xs xl:gap-1.5 2xl:text-sm 2xl:gap-2">
                  <img src={clock} alt="clock" className="w-5 h-5" />
                  <div className="grid grid-cols-2 w-full lg:pr-1 lg:gap-1 xl:pr-4 xl:gap-1.5 2xl:pr-4 2xl:gap-2">
                    <span className="font-medium text-[#282828] lg:text-xs xl:text-xs 2xl:text-sm">
                      Duration:
                    </span>
                    <span className="text-right">{project.duration}</span>
                  </div>
                </div>

                <div className="grid grid-cols-[auto_1fr] lg:text-xs lg:gap-1 xl:text-xs xl:gap-1.5 2xl:text-sm 2xl:gap-2">
                  <img src={profile} alt="profile" className="w-5 h-5" />
                  <div className="grid grid-cols-2 w-full lg:pr-1 lg:gap-1 xl:pr-4 xl:gap-1.5 2xl:pr-4 2xl:gap-2">
                    <span className="font-medium text-[#282828] lg:text-xs xl:text-xs 2xl:text-sm">
                      Posted by:
                    </span>
                    <span className="text-right">{project.postedBy}</span>
                  </div>
                </div>
                <div className="grid grid-cols-[auto_1fr] lg:text-xs lg:gap-1 xl:text-xs xl:gap-1.5 2xl:text-sm 2xl:gap-2">
                  <img src={checked} alt="checked" className="w-5 h-5" />
                  <div className="grid grid-cols-2 w-full lg:pr-1 lg:gap-1 xl:pr-4 xl:gap-1.5 2xl:pr-4 2xl:gap-2">
                    <span className="font-medium text-[#282828] lg:text-xs xl:text-xs 2xl:text-sm">
                      Status:
                    </span>
                    <span className="text-right">
                      <span className="inline-block border border-[#E96B1C] text-[#E96B1C] rounded-xl lg:px-3 lg:py-[1px] xl:px-3.5 xl:py-[2.5px] 2xl:px-4 2xl:py-[3px]">
                        {project.status}
                      </span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="w-full text-center lg:mt-3 xl:mt-3.5 2xl:mt-4">
                <button
                  onClick={() =>
                    navigate(`/dashboard/project-details?id=${project.id}`)
                  }
                  className="w-full mx-1 bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-lg lg:py-2 lg:text-xs xl:py-2.5 xl:text-xs 2xl:py-3 2xl:text-sm"
                >
                  Project Details
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-lg font-medium  mt-10 text-gray-500 flex items-center justify-center text-center ">
          <span>There are no Projects available at the moment.</span>
        </div>
      )}

      {/* Modal */}
      {isModalOpen && (
        <ProjectCreate
          onClose={() => setIsModalOpen(false)}
          onSuccess={handleSuccess}
        />
      )}

      {/* Custom Toast */}
      {showToast && (
        <div className="fixed top-5 right-5 z-50">
          <CustomToast
            title="Project Created"
            message="Your project has been successfully created!"
            type="success"
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
