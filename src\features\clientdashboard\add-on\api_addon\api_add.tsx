// import { customAxios } from "@/utils/axio-interceptor"; // Adjust the path as necessary
// import { FormValues } from "../component/commonpackageplan/questioniare"; // Assuming you are importing FormValues

// export const submitQuestionnaire = async (data: FormValues) => {
//     try {
//         const formattedData = {
//             packageId: data.packageCategory, // Mapping your form data to the API request
//             availableFrom: data.fromTime,
//             availableTo: data.toTime,
//             timezone: data.timeZone?.value,
//             industry: data.industry,
//             category: data.annotationCategory,
//         };

//         // Use PATCH instead of POST
//         const response = await customAxios.patch("/v1/clients/details", formattedData); // This sends a PATCH request
//         return response.data; // Handle the response accordingly
//     } catch (error) {
//         console.error("Error submitting the form:", error);
//         throw error; // Handle the error appropriately
//     }
// };
