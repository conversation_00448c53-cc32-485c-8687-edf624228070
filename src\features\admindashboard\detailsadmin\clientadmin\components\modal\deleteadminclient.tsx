import { adminClientDeleteApi } from "../adminclientsuspenddelete_api/adminclientssuspenddelete_api";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface DeleteAdminClientProps {
  client: {
    id: string;
    name: string;
  };
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export default function DeleteAdminClient({ client, open, onOpenChange }: DeleteAdminClientProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    try {
      setIsLoading(true);
      await adminClientDeleteApi(client.id);
      if (onOpenChange) onOpenChange(false);
    } catch (error) {
      console.error("Error deleting client:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="py-5 flex flex-col gap-6 justify-center items-center sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl">
            Do you want to delete this account?
          </DialogTitle>
        </DialogHeader>
        
        <DialogFooter className="w-full flex justify-center gap-4">
          <Button
            variant="ghost"
            className="border-gradient bg-white hover:bg-[#faf5f5] px-14 py-6 text-black"
            onClick={() => onOpenChange?.(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            className="px-14 py-6"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}