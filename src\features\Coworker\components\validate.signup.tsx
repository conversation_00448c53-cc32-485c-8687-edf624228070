// types.ts (if you need to share types)
import { CoWorkerInviteAccept } from "@/features/auth/api/client-api";
import { toast } from "react-toastify";

export interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreePolicy: boolean;
}

export interface ValidationErrors {
  [key: string]: string;
}

// validation.ts
export const validateSignupForm = (
  formData: SignupFormData
): ValidationErrors => {
  const newErrors: ValidationErrors = {};
  const passwordPattern =
    /^(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (!formData.firstName.trim())
    newErrors.firstName = "First name is required.";
  if (!formData.lastName.trim()) newErrors.lastName = "Last name is required.";

  if (!formData.email.trim()) {
    newErrors.email = "Email is required.";
  } else if (!emailPattern.test(formData.email)) {
    newErrors.email = "Please enter a valid email address.";
  }

  if (!formData.password.trim()) {
    newErrors.password = "Password is required.";
  } else if (!passwordPattern.test(formData.password)) {
    newErrors.password =
      "Password must be at least 8 characters with 1 uppercase letter, 1 number, and 1 special character.";
  }

  if (!formData.confirmPassword.trim()) {
    newErrors.confirmPassword = "Please confirm your password.";
  } else if (formData.password !== formData.confirmPassword) {
    newErrors.confirmPassword = "Passwords do not match.";
  }

  if (!formData.agreePolicy) {
    newErrors.agreePolicy = "You must agree to the terms and conditions.";
  }

  return newErrors;
};

interface HandleCoworkerSignupParams {
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
  };
  tokenFromUrl: string;
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  setIsSubmitting: (isSubmitting: boolean) => void;
  onSuccess: () => void;
}

export const handleCoworkerSignup = async ({
  formData,
  tokenFromUrl,
  setErrors,
  setIsSubmitting,
  onSuccess,
}: HandleCoworkerSignupParams): Promise<void> => {
  if (!tokenFromUrl) {
    setErrors((prev) => ({
      ...prev,
      apiError: "Invalid invitation link. Please request a new invitation.",
    }));
    toast.error("Invalid invitation link");
    setIsSubmitting(false);
    return;
  }

  try {
    const name = `${formData.firstName} ${formData.lastName}`;

    await CoWorkerInviteAccept(
      formData.email,
      formData.password,
      name,
      tokenFromUrl
    );

    // Call the success handler
    onSuccess();
  } catch (error: any) {
    console.error("❌ Coworker signup error:", error);
    let errorMessage = "Failed to accept invite. Please try again.";

    if (error.response) {
      if (error.response.status === 400) {
        errorMessage =
          error.response.data.message || "Invalid invitation token";
      } else if (error.response.status === 409) {
        errorMessage = "This invitation has already been accepted";
      }
    }

    setErrors((prev) => ({
      ...prev,
      apiError: errorMessage,
    }));

    toast.error(errorMessage);
    setIsSubmitting(false);
  }
};
