import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ClockInOutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  actionType: "clockIn" | "clockOut" | null;
}

const ClockInOutModal: React.FC<ClockInOutModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  actionType,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Confirm {actionType === "clockIn" ? "Clock In" : "Clock Out"}
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to {actionType === "clockIn" ? "clock in" : "clock out"}?
            {actionType === "clockOut" && " Your working time will be recorded."}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            className="bg-gradient-to-r from-[#E91C24] via-[#FF6ABF] to-[#45ADE2] text-white"
          >
            Confirm {actionType === "clockIn" ? "Clock In" : "Clock Out"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClockInOutModal;