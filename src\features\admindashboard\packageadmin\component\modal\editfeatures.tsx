import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { DialogClose } from "@/components/ui/dialog";
import { useEffect, useRef, useState } from "react";
import { ChevronDown } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { fetchPackages, updateFeature } from "../../api/api";
import { useMutation, useQueryClient } from "@tanstack/react-query"; // Import useMutation and useQueryClient

type PackageType = {
  id: string;
  name: string;
};

type FormValues = {
  featureTitle: string;
  featureCategory: string[];
};

type EditFeaturesProps = {
  feature: {
    id: string;
    rule: string;
    packageIds: string[];
  };
  onSuccess?: () => void;
};

const EditFeatures = ({ feature, onSuccess }: EditFeaturesProps) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [packages, setPackages] = useState<PackageType[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dialogCloseRef = useRef<HTMLButtonElement>(null);
  const queryClient = useQueryClient(); // Initialize queryClient

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      featureTitle: feature.rule,
      featureCategory: feature.packageIds || [],
    },
  });

  const selectedCategories = watch("featureCategory");

  // Mutation for updating feature
  const updateMutation = useMutation({
    mutationFn: (data: { rule: string; packageIds: string[] }) =>
      updateFeature(feature.id, data),
    onSuccess: async () => {
      // Close the dialog programmatically
      if (dialogCloseRef.current) {
        dialogCloseRef.current.click();
      }

      // Show success toast
      toast.success("Feature updated successfully!");

      // Trigger refetch of features query
      await queryClient.refetchQueries({
        queryKey: ["features"],
        exact: true,
        type: "active",
      });

      // Call onSuccess if provided
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error) => {
      console.error("Error updating feature:", error);
      toast.error("Failed to update feature");
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  // Fetch Packages on mount
  useEffect(() => {
    const getPackages = async () => {
      try {
        const data = await fetchPackages();
        setPackages(data);
      } catch (error) {
        console.error("Error fetching packages:", error);
        toast.error("Failed to load packages");
      }
    };

    getPackages();
  }, []);

  // Reset form when feature prop changes
  useEffect(() => {
    reset({
      featureTitle: feature.rule,
      featureCategory: feature.packageIds || [],
    });
  }, [feature, reset]);

  const toggleCategory = (value: string) => {
    const currentValues = selectedCategories || [];
    const updatedValues = currentValues.includes(value)
      ? currentValues.filter((v) => v !== value)
      : [...currentValues, value];

    setValue("featureCategory", updatedValues, { shouldValidate: true });
  };

  const onSubmit = async (formData: FormValues) => {
    if (formData.featureCategory.length === 0) {
      toast.error("Please select at least one category");
      return;
    }
    setIsSubmitting(true);

    const featureData = {
      rule: formData.featureTitle,
      packageIds: formData.featureCategory,
    };

    // Trigger the mutation
    updateMutation.mutate(featureData);
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full flex flex-col gap-6"
    >
      <h2 className="text-xl font-semibold mb-4">Edit Feature</h2>
      <div className="flex flex-col justify-center gap-4">
        {/* Feature Title */}
        <div>
          <Label>Feature Title *</Label>
          <div className="border-gradient rounded-lg">
            <Input
              placeholder="Enter feature title"
              autoComplete="off"
              {...register("featureTitle", {
                required: "Feature title is required",
              })}
              className="bg-[#F9EFEF] text-[#5E5E5E]"
              disabled={isSubmitting}
            />
          </div>
          {errors.featureTitle && (
            <p className="text-red-500 text-sm mt-1">
              {errors.featureTitle.message}
            </p>
          )}
        </div>

        {/* Multi-select Category */}
        <div>
          <Label>Feature Category *</Label>
          <div className="relative w-full mt-2 border-blue-400 border rounded-md">
            <button
              type="button"
              onClick={() => setDropdownOpen(!dropdownOpen)}
              disabled={isSubmitting}
              className={`w-full py-1 px-2 rounded-md text-left flex items-center justify-between bg-[#F9EFEF] text-[#5E5E5E] ${
                isSubmitting ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {selectedCategories.length > 0
                ? packages
                    .filter((pkg) => selectedCategories.includes(pkg.id))
                    .map((pkg) => pkg.name)
                    .join(", ")
                : "Select category"}
              <ChevronDown
                className={`ml-2 h-4 w-4 transition-transform ${
                  dropdownOpen ? "rotate-180" : "rotate-0"
                }`}
              />
            </button>

            {dropdownOpen && (
              <div className="absolute w-full max-h-[200px] overflow-y-auto bg-white border-blue-400 border mt-1 rounded-md shadow-lg z-10">
                {packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className="flex items-center justify-between cursor-pointer py-2 px-3 hover:bg-gray-100"
                    onClick={() => !isSubmitting && toggleCategory(pkg.id)}
                  >
                    <div className="flex items-center w-full">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(pkg.id)}
                        onClick={(e) => e.stopPropagation()}
                        onChange={() => toggleCategory(pkg.id)}
                        disabled={isSubmitting}
                        className="mr-2 accent-brown-san-x cursor-pointer"
                      />
                      <p className="truncate">{pkg.name}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          {errors.featureCategory && (
            <p className="text-red-500 text-sm mt-1">
              Please select at least one category
            </p>
          )}
        </div>
      </div>

      {/* Footer Buttons */}
      <div className="mt-6 flex justify-end gap-2">
        <Button
          type="submit"
          variant="gradient"
          className="px-[4rem] py-6"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save"}
        </Button>
        <DialogClose asChild>
          <Button
            ref={dialogCloseRef}
            variant="ghost"
            className="px-[4rem] py-6 border-gradient"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </DialogClose>
      </div>
    </form>
  );
};

export default EditFeatures;