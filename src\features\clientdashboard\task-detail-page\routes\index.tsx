import { Routes, Route, Navigate } from "react-router-dom";
import TaskDetails from "../components/taskdetails";
import Coworkers from "../components/coworker";
import ClientAnnotators from "../components/annonator";
import ClientProjects from "../components/project";
// import ProjectDetails from "../components/project/projectdetails";

export const DetailsRoutes = () => {
  return (
    <Routes>
      {/* Redirect to projects by default */}
      <Route path="/taskdetails" element={<TaskDetails />}>
        <Route index element={<Navigate to="projects" replace />} />
        <Route path="projects" element={<ClientProjects />} />
        <Route path="annotators" element={<ClientAnnotators />} />
        <Route path="coworker" element={<Coworkers />} />
      </Route>

      <Routes />
      {/* <Route path="/proejct-details" element={<ProjectDetails />} /> */}
    </Routes>
  );
};
