// import * as React from "react";
// import Tabs from "@mui/material/Tabs";
// import Tab from "@mui/material/Tab";
// import Box from "@mui/material/Box";
// import Clientsadmin from "@/features/admindashboard/detailsadmin/clientadmin";
// import Annotators from "@/features/admindashboard/detailsadmin/annonatoradmin";
// import Coworkers from "@/features/admindashboard/detailsadmin/coodinatoradmin";
// import Projects from "@/features/admindashboard/detailsadmin/projectadmin";

// interface TabPanelProps {
//   children?: React.ReactNode;
//   index: number;
//   value: number;
// }

// function CustomTabPanel(props: TabPanelProps) {
//   const { children, value, index, ...other } = props;

//   return (
//     <div
//       role="tabpanel"
//       hidden={value !== index}
//       id={`simple-tabpanel-${index}`}
//       aria-labelledby={`simple-tab-${index}`}
//       {...other}
//     >
//       {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
//     </div>
//   );
// }

// function a11yProps(index: number) {
//   return {
//     id: `simple-tab-${index}`,
//     "aria-controls": `simple-tabpanel-${index}`,
//   };
// }

// export default function ProjectCoardiantorDetails() {
//   const [value, setValue] = React.useState(0);

//   const handleChange = (event: React.SyntheticEvent, newValue: number) => {
//     setValue(newValue);
//     console.log(event);
//   };

//   return (
//     <Box sx={{ width: "100%" }}>
//       <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
//         <Tabs
//           value={value}
//           onChange={handleChange}
//           aria-label="projects, annotators, co-workers lists"
//           TabIndicatorProps={{
//             style: {
//               color: "#000000",
//               backgroundColor: "#FF577F", // custom indicator color
//               height: "4px",
//             },
//           }}
//         >
//           <Tab
//             label="Clients"
//             {...a11yProps(0)}
//             sx={{
//               color: value === 0 ? "#FF577F" : "#000000A8",
//               backgroundColor: value === 2 ? "transparent" : "transparent",
//               borderRadius: "8px 8px 0 0",
//               textTransform: "none",
//               fontWeight: 600,
//               mx: 1,
//               fontSize: "18px",
//             }}
//           />
//           <Tab
//             label="Annotators"
//             {...a11yProps(1)}
//             sx={{
//               color: value === 1 ? "#FF577F" : "#000000A8",
//               backgroundColor: value === 1 ? "transparent" : "transparent",
//               borderRadius: "8px 8px 0 0",
//               textTransform: "none",
//               fontWeight: 600,
//               mx: 1,
//               fontSize: "18px",
//             }}
//           />
//           <Tab
//             label="Clients"
//             {...a11yProps(2)}
//             sx={{
//               color: value === 2 ? "#FF577F" : "#000000A8",
//               backgroundColor: value === 2 ? "transparent" : "transparent",
//               borderRadius: "8px 8px 0 0",
//               textTransform: "none",
//               fontWeight: 600,
//               mx: 1,
//               fontSize: "18px",
//             }}
//           />
//         </Tabs>
//       </Box>
//       <CustomTabPanel value={value} index={0}>
//         <Clientsadmin />
//       </CustomTabPanel>
//       <CustomTabPanel value={value} index={1}>
//         <Annotators />
//       </CustomTabPanel>
//       <CustomTabPanel value={value} index={2}>
//         <Coworkers />
//       </CustomTabPanel>
//       <CustomTabPanel value={value} index={3}></CustomTabPanel>
//     </Box>
//   );
// }
