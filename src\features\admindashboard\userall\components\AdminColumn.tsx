"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { AttendanceType } from "./attendancetype";
import { useNavigate } from "react-router-dom";

export const useAdminColumns = (): ColumnDef<AttendanceType>[] => {
  const navigate = useNavigate(); // ✅ Top-level only

  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <div
          className="w-[350px] text-[14px]  font-medium cursor-pointer"
          onClick={() =>
            column.toggleSorting(column.getIsSorted() === "asc")
          }
        >
          User Name
        </div>
      ),
      cell: ({ row }) => (
        <div className="pl-2 text-[14px] font-normal">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "role",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Role
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-6 text-[14px] font-normal">{row.getValue("role")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Email
        </Button>
      ),
      cell: ({ row }) => (
        <div className="pl-6 text-[14px] font-normal">{row.getValue("email")}</div>
      ),
    },
    {
      id: "actions",
      header: () => <div className="pl-4 text-[14px] font-medium">Actions</div>,
      cell: ({ row }) => {
        const id = row.original.id;
        
        console.log("Row data:", id);
        return (
          <div className="pl-4">
            <Button
              variant="ghost"
              onClick={() => navigate(`/admin/total-users/user-details/${id}`)}
              className="px-7 rounded-xl border border-[#FF577F] text-[#FF577F]"
            >
              View
            </Button>
          </div>
        );
        // return (
        //   <div className="pl-4">
        //     <Button
        //       variant="ghost"
        //       onClick={() => navigate(`/admin/total-users/user-details`)}
        //       className="px-7 rounded-xl border border-[#FF577F] text-[#FF577F]"
        //     >
        //       View
        //     </Button>
        //   </div>
        // );
      },
    },
  ];
};
