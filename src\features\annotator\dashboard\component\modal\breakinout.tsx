import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface BreakInOutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isOnBreak: boolean;
}

const BreakInOutModal: React.FC<BreakInOutModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isOnBreak,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="flex flex-col justify-center items-center w-full pt-10">
        <DialogHeader>
          <DialogTitle className="text-[25px]">
            {isOnBreak ? "Are you sure you want to end your break?" : "Are you sure you want to start your break?"}
          </DialogTitle>
        
        </DialogHeader>
        <DialogFooter className="flex gap-x-2">
          <Button variant="outline" onClick={onClose} className="px-10 border-gradient">
            Cancel
          </Button>
          <Button
          variant={"gradient"}
            onClick={onConfirm}
            className="px-10 text-white"
          >
            {isOnBreak ? "End Break" : "Start Break"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BreakInOutModal;