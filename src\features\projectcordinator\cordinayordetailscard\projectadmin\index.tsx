import { useState, useEffect } from "react";
import CustomToast from "@/_components/common/customtoast";
import { useNavigate } from "react-router-dom";
import calender from "@/assets/icons/clenderclient.svg";
import clock from "@/assets/icons/clockclient.svg";
import profile from "@/assets/icons/clientprofile.svg";
import checked from "@/assets/icons/checkedcleint.svg";

// Define the Project type used in the component
interface Project {
  id: string;
  title: string;
  description: string;
  startDate: string;
  duration: string;
  postedBy: string;
  status: string;
  level: "low" | "medium" | "hard";
}

export default function CoordirProjects() {
  const [showToast, setShowToast] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        // Import the API function directly
        const { getCoordinatorProjects } = await import("@/features/projectcordinator/api/api");
        const response = await getCoordinatorProjects();

        if (response && response.data && Array.isArray(response.data)) {
          // Map API response to our Project type
          const formattedProjects = response.data.map((project: any) => ({
            id: project.id,
            title: project.name,
            description: project.description,
            startDate: formatDate(project.startDate),
            duration: project.duration || "N/A",
            postedBy: project.postedBy || "Unknown",
            status: project.status,
            level: mapPriorityToLevel(project.priority)
          }));

          setProjects(formattedProjects);
        } else {
          console.error("Invalid API response format:", response);
          setError("Invalid data format received from API");
        }
      } catch (error) {
        console.error("Failed to fetch projects:", error);
        setError("Failed to load projects. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Helper functions for data formatting
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear().toString().slice(2, 4);
      return `${day}/${month}/${year}`;
    } catch (e) {
      return "Invalid date";
    }
  };

  const mapPriorityToLevel = (priority: string): Project["level"] => {
    switch (priority?.toUpperCase()) {
      case "LOW":
        return "low";
      case "MEDIUM":
        return "medium";
      case "HIGH":
        return "hard";
      default:
        return "medium";
    }
  };

  const getLevelStyle = (level: Project["level"]) => {
    switch (level) {
      case "low":
        return "bg-blue-800";
      case "medium":
        return "bg-orange-500";
      case "hard":
        return "bg-red-500";
      default:
        return "bg-gray-400";
    }
  };

  const getLevelLetter = (level: Project["level"]) => {
    switch (level) {
      case "low":
        return "Low";
      case "medium":
        return "Medium";
      case "hard":
        return "High";
      default:
        return "?";
    }
  };

  const handleSuccess = () => {
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
    }, 4000); // auto-close after 4s
  };
  console.log("handlesuccess nont use  in proejctdmin", handleSuccess)
  return (
    <div className="w-full">
      {/* Projects List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FF577F]"></div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-red-500 text-center">
            <p className="text-xl font-semibold">Error</p>
            <p>{error}</p>
          </div>
        </div>
      ) : projects.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500 text-center">
              <div className="text-lg font-medium  mt-10 text-gray-500 flex items-center justify-center text-center "><span>
          There are no coordinator available at the moment.</span></div>
          </div>
        </div>
      ) : (
        <div className="grid lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5 gap-3 py-2 mx-auto max-w-[98%]">
          {projects.map((project) => (
            <div
              key={project.id}
              className="border border-[#FF577F] lg-only:p-1.5 xl-only:p-2 2xl-only:p-2.5 rounded-lg shadow-md"
            >
              <div className="flex flex-col lg-only:gap-y-1.5 xl-only:gap-y-2 2xl-only:gap-y-2.5 rounded-2xl lg-only:p-3 xl-only:p-4 2xl-only:p-5">
                <div className="flex flex-row justify-between items-center">
                  <h2 className="lg-only:text-sm xl-only:text-[15px] 2xl-only:text-base font-semibold">{project.title.split(' ').slice(0, 2).join(' ')}{project.title.split(' ').length > 2 ? '...' : ''}</h2>
                  {/* Level Circle */}

                  <div
                    className={`lg-only:w-[45px] lg-only:h-5 xl-only:w-[50px] xl-only:h-6 2xl-only:w-[55px] 2xl-only:h-7 rounded-full flex items-center justify-center text-white lg-only:text-[8px] xl-only:text-[9px] 2xl-only:text-[10px] font-bold ${getLevelStyle(
                      project.level
                    )}`}
                  >
                    {getLevelLetter(project.level)}
                  </div>
                </div>
                <p className="lg-only:text-[10px] xl-only:text-[16px] 2xl-only:text-sm line-clamp-1">{project.description}</p>
                <p className=" flex items-center lg-only:gap-0.5 xl-only:gap-1 2xl-only:gap-1.5">
                  <img src={calender} alt="calender" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                  <div className="flex flex-row items-center w-full lg-only:px-1 xl-only:px-1.5 2xl-only:px-2 justify-between gap-1">
                    <span className="font-semibold">Started:</span>{" "}
                    <span>{project.startDate}</span>
                  </div>
                </p>

                <p className=" flex items-center lg-only:gap-0.5 xl-only:gap-1 2xl-only:gap-1.5">
                  <img src={clock} alt="clock" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                  <div className="flex flex-row w-full justify-between lg-only:px-1 xl-only:px-1.5 2xl-only:px-2 gap-1">
                    <span className="font-semibold">Duration:</span>
                    <span>{project.duration}</span>
                  </div>
                </p>

                <p className="l flex items-center lg-only:gap-0.5 xl-only:gap-1 2xl-only:gap-1.5">
                  <img src={profile} alt="profile" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                  <div className="flex flex-row w-full justify-between lg-only:px-1 xl-only:px-1.5 2xl-only:px-2 gap-1">
                    <span className="font-semibold">Posted by:</span>
                    <span>{project.postedBy}</span>
                  </div>
                </p>
                <p className=" flex flex-row justify-between items-center lg-only:gap-0.5 xl-only:gap-1 2xl-only:gap-1.5">
                  <div className="flex flex-row justify-center items-center lg-only:gap-x-0.5 xl-only:gap-x-1 2xl-only:gap-x-1.5">
                    <img src={checked} alt="checked" className="lg-only:w-3 lg-only:h-3 xl-only:w-3.5 xl-only:h-3.5 2xl-only:w-4 2xl-only:h-4 text-[#7a7a7a] dark:text-white" />
                    <span className="font-semibold">Status:</span>
                  </div>
                  <div className="flex flex-row gap-1">
                    <span className="border border-[#E96B1C] text-[#E96B1C] lg-only:px-1.5 xl-only:px-2 2xl-only:px-2.5 rounded-xl">
                      {project.status}
                    </span>
                  </div>
                </p>
              </div>

              <div className="w-full flex justify-center items-center lg-only:mt-2 xl-only:mt-2.5 2xl-only:mt-3">
                <button
                  onClick={() => {
                    console.log("Navigating to project details with ID:", project.id);
                    navigate(`/coordinator/coordinatorproject-details?id=${project.id}`);
                  }}
                  className="w-full mx-1 lg-only:py-1 xl-only:py-1.5 2xl-only:py-2 lg-only:text-[10px] xl-only:text-xs 2xl-only:text-sm bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded"
                >
                  Project Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal
      {isModalOpen && (
        <ProjectCreate
          onClose={() => setIsModalOpen(false)}
          onSuccess={handleSuccess}
        />
      )} */}

      {/* Custom Toast */}
      {showToast && (
        <div className="fixed top-5 right-5 z-50">
          <CustomToast
            title="Project Created"
            message="Your project has been successfully created!"
            type="success"
            onClose={() => setShowToast(false)}
          />
        </div>
      )}
    </div>
  );
}
