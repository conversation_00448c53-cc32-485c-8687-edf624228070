// src\features\admindashboard\detailsadmin\clientadmin\api\useClientProjectsQuery.ts
import { useQuery } from "@tanstack/react-query";
import { getAdminClientById } from "../../admindetails_api/admindetails_api";

export const useClientProjectsList = (clientId: string) => {
  return useQuery({
    queryKey: ["clientProjects", clientId],
    queryFn: () => getAdminClientById(clientId),
    enabled: !!clientId, // Only run the query if clientId is provided
  });
};
