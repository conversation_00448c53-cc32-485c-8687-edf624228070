// utils/sidebarData.tsx
import { USER_ROLES } from "@/utils/constants";

// images
import home from "@/assets/icons/dashboard/home.svg";
import bell from "@/assets/icons/dashboard/bell.svg";
import bill from "@/assets/icons/dashboard/bill.svg";
import add from "@/assets/icons/dashboard/add.svg";
import kanban from "@/assets/icons/dashboard/canban.svg";
import chat from "@/assets/icons/dashboard/chat.svg";
import halfprofile from "@/assets/icons/dashboard/halfprofile.svg";
import faq from "@/assets/icons/dashboard/faq.svg";
import match from "@/assets/icons/dashboard/match.svg";
import users from "@/assets/icons/dashboard/users.svg";
import packageIcon from "@/assets/icons/dashboard/package.svg";
import admin from "@/assets/icons/dashboard/admin.svg";
import setting from "@/assets/icons/dashboard/setting.svg";
import calender from "@/assets/icons/dashboard/calender.svg";
import bank from "@/assets/icons/dashboard/bank.svg";

export const sidebarData = [
  // CLIENT & COWORKER
  {
    roles: [USER_ROLES.CLIENT, USER_ROLES.COWORKER],
    items: [
      {
        section: "Main",
        items: [
          {
            name: "Dashboard",
            path: "/dashboard",
            icon: <img src={home} alt="home" className="w-5 h-5" />,
          },
          {
            name: "Chat",
            path: "/dashboard/chat",
            icon: <img src={chat} alt="chat" className="w-6 h-6" />,
          },
          {
            name: "Details",
            path: "/dashboard/task-details",
            icon: <img src={kanban} alt="kanban" className="w-5 h-5" />,
          },
          {
            name: "Add on",
            path: "/dashboard/addon",
            icon: <img src={add} alt="add" className="w-6 h-6" />,
          },
          {
            name: "Notifications",
            path: "/dashboard/notification",
            icon: <img src={bell} alt="bell" className="w-6 h-5" />,
          },
          {
            name: "Invite Co-worker",
            path: "/dashboard/invite-coworker",
            icon: (
              <img src={halfprofile} alt="halfprofile" className="w-6 h-7" />
            ),
          },

          {
            name: "Billing",
            icon: <img src={bill} alt="bill" className="w-5 h-5" />,
            children: [
              {
                name: "Transaction",
                path: "/dashboard/billing",
                icon: <img src={bill} alt="bill" className="w-6 h-7" />,
              },
              // {
              //   name: "Payment Method",
              //   path: "/dashboard/billing/payment-method",
              //   icon: <img src={bill} alt="bill" className="w-6 h-7" />,
              // },
              {
                name: "Subscription",
                path: "/dashboard/billing/subscription",
                icon: <img src={bill} alt="bill" className="w-6 h-7" />,
              },
            ],
          },
        ],
      },
      {
        section: "More",
        items: [
          {
            name: "Settings",
            path: "/dashboard/settings",
            icon: <img src={setting} alt="setting" className="w-5 h-5" />,
          },
          {
            name: "FAQs",
            path: "/dashboard/faq",
            icon: <img src={faq} alt="faq" className="w-6 h-7" />,
          },
        ],
      },
    ],
  },

  // ANNOTATOR
  {
    roles: [USER_ROLES.ANNOTATOR],
    items: [
      {
        section: "Main",
        items: [
          {
            name: "Dashboard",
            path: "/annotator/list",
            icon: <img src={home} alt="home" className="w-5 h-5" />,
          },
          {
            name: "Project",
            path: "/annotator/annotatordetail",
            icon: <img src={kanban} alt="kanban" className="w-5 h-5" />,
          },
          {
            name: "Chat",
            path: "/annotator/chat",
            icon: <img src={chat} alt="chat" className="w-6 h-6" />,
          },
          {
            name: "Attendance",
            path: "/annotator/attendance",
            icon: <img src={calender} alt="calender" className="w-6 h-7" />,
          },
          {
            name: "Notifications",
            path: "/annonator/notification",
            icon: <img src={bell} alt="bell" className="w-6 h-5" />,
          },
        ],
      },
      {
        section: "More",
        items: [
          {
            name: "Settings",
            path: "/annotator/settings",
            icon: <img src={setting} alt="setting" className="w-5 h-5" />,
          },
        ],
      },
    ],
  },

  // PROJECT COORDINATOR
  {
    roles: [USER_ROLES.PROJECT_COORDINATOR],
    items: [
      {
        section: "Main",
        items: [
          {
            name: "Dashboard",
            path: "/coordinator/dashboardlist",
            icon: <img src={home} alt="home" className="w-5 h-5" />,
          },
          {
            name: "Chat",
            path: "/coordinator/chat",
            icon: <img src={chat} alt="chat" className="w-6 h-6" />,
          },
          {
            name: "Details",
            path: "/coordinator/projectdetails",
            icon: <img src={kanban} alt="kanban" className="w-5 h-5" />,
          },
          {
            name: "Notifications",
            path: "/coordinator/notification",
            icon: <img src={bell} alt="bell" className="w-6 h-5" />,
          },
        ],
      },
      {
        section: "More",
        items: [
          {
            name: "Settings",
            path: "/coordinator/settings",
            icon: <img src={setting} alt="setting" className="w-5 h-5" />,
          },
        ],
      },
    ],
  },

  // ADMIN
  {
    roles: [USER_ROLES.ADMIN],
    items: [
      {
        section: "Main",
        items: [
          {
            name: "Dashboard",
            path: "/admin/dashboard",
            icon: <img src={home} alt="home" className="w-5 h-5" />,
          },
          {
            name: "Chat",
            path: "/admin/chat",
            icon: <img src={chat} alt="chat" className="w-6 h-6" />,
          },
          {
            name: "Details",
            path: "/admin/admindetails",
            icon: <img src={kanban} alt="kanban" className="w-5 h-5" />,
          },
          {
            name: "Onboard",
            path: "/admin/onboard",
            icon: <img src={add} alt="add" className="w-6 h-6" />,
          },
          {
            name: "Notifications",
            path: "/admin/notifications",
            icon: <img src={bell} alt="bell" className="w-6 h-5" />,
          },
          {
            name: "Match Making",
            path: "/admin/match-making",
            icon: <img src={match} alt="match" className="w-6 h-7" />,
          },
          {
            name: "Users",
            path: "/admin/total-users",
            icon: <img src={users} alt="users" className="w-7 h-7" />,
          },
          {
            name: "Payments",
            path: "/admin/confirm-others-payment",
            icon: <img src={bank} alt="users" className="w-6 h-6" />,
          },
        ],
      },
      {
        section: "More",
        items: [
          {
            name: "Settings",
            path: "/admin/adminsettings",
            icon: <img src={setting} alt="setting" className="w-5 h-5" />,
          },
          {
            name: "FAQs Admin",
            path: "/admin/faqsadmin",
            icon: <img src={faq} alt="faq" className="w-6 h-7" />,
          },

          {
            name: "Package Admin",
            path: "/admin/package-admin",
            icon: <img src={packageIcon} alt="package" className="w-6 h-7" />,
          },
          {
            name: "Admins",
            path: "/admin/admins-list",
            icon: <img src={admin} alt="admin" className="w-6 h-7" />,
          },
        ],
      },
    ],
  },
];
