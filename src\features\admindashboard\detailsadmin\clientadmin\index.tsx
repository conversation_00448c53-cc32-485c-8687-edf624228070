import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ClientAdminCard from "./components/ClientAdminCard";
import { getAdminClient } from "../admindetails_api/admindetails_api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

interface ClientData {
  id: string;
  name: string;
  lastname: string | null;
  email: string;
  createdAt: string;
  status: "active" | "suspended";
  Subscription: Array<{
    id: string;
    packageId: string;
    startDate: string;
    endDate: string;
    status: string;
  }>;
  _count?: {
    projectsOwned?: number;
    coWorkers?: number;
    annotatorProjects?: number;
  };
  projectCount?: number;
  coworkerCount?: number;
  annotatorCount?: number;
}

interface ClientCardProps {
  id: string;
  name: string;
  email: string;
  annotators: string;
  projects: string;
  joiningDate: string;
  expiringon: string;
  coworker: string[];
  image?: string;
  status: "active" | "suspended"; // Made status required
}

export default function Clientsadmin() {
  const [clients, setClients] = useState<ClientCardProps[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        const response = await getAdminClient();

        if (!response?.data || !Array.isArray(response.data)) {
          console.error("Unexpected API response structure:", response);
          toast.error("Failed to load clients: Invalid data format");
          setClients([]);
          return;
        }

        const processedClients = response.data.map((client: ClientData) => {
          const latestSubscription =
            client.Subscription?.[client.Subscription.length - 1];
          const expiryDate = latestSubscription?.endDate
            ? formatDate(latestSubscription.endDate)
            : "0";
          const joiningDate = formatDate(client.createdAt);

          const projectCount =
            client.projectCount ?? client._count?.projectsOwned ?? 0;
          const annotatorCount =
            client.annotatorCount ?? client._count?.annotatorProjects ?? 0;
          const coworkerCount =
            client.coworkerCount ?? client._count?.coWorkers ?? 0;

          const coworkerArray =
            coworkerCount > 0
              ? [`${coworkerCount} coworker${coworkerCount > 1 ? "s" : ""}`]
              : [];

          // Check localStorage for any updated status first
          const savedStatus = localStorage.getItem(
            `client-status-${client.id}`
          );
          const status = savedStatus
            ? (savedStatus as "active" | "suspended")
            : client.status || "active";

          return {
            id: client.id,
            name: client.name || "",
            email: client.email || "",
            annotators: annotatorCount.toString(),
            projects: projectCount.toString(),
            joiningDate: joiningDate,
            expiringon: expiryDate,
            coworker: coworkerArray,
            image: "",
            status: status, // Use the determined status
          };
        });

        setClients(processedClients);
      } catch (error) {
        console.error("Error fetching clients:", error);
        toast.error("Failed to load clients. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchClients();
  }, []);

  const formatDate = (dateStr: string): string => {
    if (!dateStr) return "0";
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "2-digit",
      });
    } catch (error) {
      return "0";
    }
  };

  const handleStatusChange = (
    clientId: string,
    newStatus: "active" | "suspended"
  ) => {
    // Update localStorage
    localStorage.setItem(`client-status-${clientId}`, newStatus);

    // Update UI state
    setClients((prevClients) =>
      prevClients.map((client) =>
        client.id === clientId ? { ...client, status: newStatus } : client
      )
    );

    const action = newStatus === "active" ? "reactivated" : "suspended";
    toast.success(`Client ${action} successfully`, {
      position: "top-right",
      autoClose: 3000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
    });
  };

  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  return (
    <div className="relative">
      <div className="grid gap-4 lg-only:grid-cols-3 xl-only:grid-cols-4 2xl-only:grid-cols-5 mx-auto max-w-[98%]">
        {clients.length > 0 ? (
          clients.map((client) => (
            <ClientAdminCard
              key={client.id}
              client={client}
              onStatusChange={handleStatusChange}
            />
          ))
        ) : (
          <div className="text-lg font-medium mt-10 text-gray-500 flex items-center justify-center text-center col-span-full">
            <span>There are no clients available at the moment.</span>
          </div>
        )}
      </div>
    </div>
  );
}
