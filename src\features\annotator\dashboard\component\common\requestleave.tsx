import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { AnnotatorLeaveApi } from "../annonatorleave_api/annonatorleaveapi";

interface ShiftChangeProps {
  onClose: () => void;
  onSuccess: () => void;
}

export default function RequestLeave({ onClose, onSuccess }: ShiftChangeProps) {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [reason, setReason] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    setIsOpen(true);
  }, []);

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(onClose, 300);
  };

  const handleSubmit = async () => {
    if (!startDate || !endDate || !reason) {
      toast.error("Please fill in all fields: Start Date, End Date, and Reason.");
      return;
    }

    if (new Date(endDate) < new Date(startDate)) {
      toast.error("End Date cannot be before Start Date.");
      return;
    }

    try {
      const leaveData = { startDate, endDate, reason };
      await AnnotatorLeaveApi.requestLeave(leaveData);
      toast.success("Leave request submitted successfully!");
      handleClose();
      setTimeout(onSuccess, 300);
    } catch (error) {
      toast.error("Failed to submit leave request. Please try again.");
    }
  };

  // Get today's date in YYYY-MM-DD format for min attribute
  const today = new Date().toISOString().split("T")[0];

  return (
    <>
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={handleClose}
      ></div>

      <div
        className={`fixed top-0 right-0 h-screen w-[25rem] p-7 flex flex-col justify-between bg-white shadow-lg z-50 transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div>
          <div className="flex flex-row justify-between text-lg font-bold text-gray-800 mb-4 border-b pb-2">
            <h2>Request Leave</h2>
            <span
              onClick={handleClose}
              className="cursor-pointer hover:text-[#585858] text-[#727272]"
            >
              <X />
            </span>
          </div>

          {/* Date and Note Section */}
          <div className="mb-4 flex flex-col items-start justify-start gap-2">
            <div className="w-full flex flex-row items-center justify-center gap-3">
              <div className="flex flex-col w-full">
                <label className="block text-sm font-medium">Start Date:</label>
                <div className="border-gradient rounded-lg">
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    min={today}
                    className="bg-[#F9EFEF] text-[#5E5E5E] text-[13px] p-2 rounded-md w-full focus:outline-none"
                  />
                </div>
              </div>

              <div className="flex flex-col w-full">
                <label className="block text-sm font-medium">End Date:</label>
                <div className="border-gradient rounded-lg">
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    min={today}
                    className="bg-[#F9EFEF] text-[#5E5E5E] text-[13px] p-2 rounded-md w-full focus:outline-none"
                  />
                </div>
              </div>
            </div>

            <label className="block text-sm font-medium">Reason:</label>
            <div className="border-gradient rounded-[8px] w-full">
              <div className="bg-[#F9EFEF] rounded-[6px] p-1">
                <textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  className="w-full p-2 placeholder:text-[#5E5E5E] text-[13px] bg-transparent rounded-md h-20 focus:outline-none"
                  placeholder="Enter reason*"
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-row items-end justify-end gap-4 mt-4">
          <button
            onClick={handleClose}
            className="px-6 py-2 border-gradient rounded-md text-gray-600"
          >
            Cancel
          </button>
          <Button
            onClick={handleSubmit}
            variant={"gradient"}
            className="px-6 py-2 to-[#45ADE2] text-white rounded-md"
          >
            Request
          </Button>
        </div>
      </div>
    </>
  );
}