import React, { RefObject, useEffect, useState } from "react";
import { Video } from "lucide-react";
import { IoIosArrowBack } from "react-icons/io";

interface VideoCallDropdownProps {
  dropdownRef: RefObject<HTMLDivElement>;
  videoDropdownOpen: boolean;
  setVideoDropdownOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleGoogleMeet: (type: 'create' | 'instant' | 'schedule') => Promise<void>;
  handleZoomMeeting: (type: 'create' | 'instant' | 'schedule') => Promise<void>;
}

const VideoCallDropdown: React.FC<VideoCallDropdownProps> = ({
  dropdownRef,
  videoDropdownOpen,
  setVideoDropdownOpen,
  handleGoogleMeet,
  handleZoomMeeting,
}) => {
  const [googleSubmenuOpen, setGoogleSubmenuOpen] = useState(false);
  const [zoomSubmenuOpen, setZoomSubmenuOpen] = useState(false);
  const [loading, setLoading] = useState<{ google?: string, zoom?: string }>({});

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setVideoDropdownOpen(false);
      }
    };

    if (videoDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [videoDropdownOpen, dropdownRef, setVideoDropdownOpen]);

  // Handle meeting creation
  const handleOptionClick = async (
    handler: (type: 'create' | 'instant' | 'schedule') => Promise<void>,
    type: 'create' | 'instant' | 'schedule',
    service: 'google' | 'zoom'
  ) => {
    setLoading({ ...loading, [service]: type });
    try {
      await handler(type);
    } catch (error) {
      console.error(`Error creating ${service} meeting:`, error);
    } finally {
      setLoading(prev => ({ ...prev, [service]: undefined }));
      setVideoDropdownOpen(false);
    }
  };

  return (
    <div className="relative">
      {/* Video Call Button */}
      <div
        onClick={() => setVideoDropdownOpen(!videoDropdownOpen)}
        className="cursor-pointer hover:bg-gray-100 p-2 rounded"
      >
        <Video className="w-5 h-5 text-gray-700" />
      </div>

      {/* Dropdown Menu */}
      {videoDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md p-2 z-20 border border-gray-200"
        >
          {/* Google Meet Option */}
          <div className="relative">
            <div
              className="p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex justify-between items-center"
              onMouseEnter={() => {
                setGoogleSubmenuOpen(true);
                setZoomSubmenuOpen(false);
              }}
              onMouseLeave={() => setGoogleSubmenuOpen(false)}
            >
              <span className="ml-2 text-gray-500"><IoIosArrowBack /></span>
              <span className="text-gray-700">Google Meet</span>
            </div>

            {googleSubmenuOpen && (
              <div
                className="absolute right-[9rem] top-0 w-48 bg-white shadow-lg rounded-md p-3 z-30 border border-gray-200"
                onMouseEnter={() => setGoogleSubmenuOpen(true)}
                onMouseLeave={() => setGoogleSubmenuOpen(false)}
              >
                <div
                  className={`p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex items-center ${loading.google === 'instant' ? 'opacity-70' : ''}`}
                  onClick={() => handleOptionClick(handleGoogleMeet, 'instant', 'google')}
                >
                  {loading.google === 'instant' && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                  )}
                  <span className="text-gray-700">Instant meeting</span>
                </div>
                <div
                  className={`p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex items-center ${loading.google === 'schedule' ? 'opacity-70' : ''}`}
                  onClick={() => handleOptionClick(handleGoogleMeet, 'schedule', 'google')}
                >
                  {loading.google === 'schedule' && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                  )}
                  <span className="text-gray-700">Schedule in calendar</span>
                </div>
              </div>
            )}
          </div>

          {/* Zoom Option */}
          <div className="relative mt-1">
            <div
              className="p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex justify-between items-center"
              onMouseEnter={() => {
                setZoomSubmenuOpen(true);
                setGoogleSubmenuOpen(false);
              }}
              onMouseLeave={() => setZoomSubmenuOpen(false)}
            >
              <span className="ml-2 text-gray-500"><IoIosArrowBack /></span>
              <span className="text-gray-700">Zoom Meet</span>
            </div>

            {zoomSubmenuOpen && (
              <div
                className="absolute right-[9rem] top-0 ml-1 w-48 bg-white shadow-lg rounded-md p-1 z-30 border border-gray-200"
                onMouseEnter={() => setZoomSubmenuOpen(true)}
                onMouseLeave={() => setZoomSubmenuOpen(false)}
              >
                <div
                  className={`p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex items-center ${loading.zoom === 'instant' ? 'opacity-70' : ''}`}
                  onClick={() => handleOptionClick(handleZoomMeeting, 'instant', 'zoom')}
                >
                  {loading.zoom === 'instant' && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                  )}
                  <span className="text-gray-700">Instant meeting</span>
                </div>
                <div
                  className={`p-2 text-sm hover:bg-gray-100 rounded cursor-pointer flex items-center ${loading.zoom === 'schedule' ? 'opacity-70' : ''}`}
                  onClick={() => handleOptionClick(handleZoomMeeting, 'schedule', 'zoom')}
                >
                  {loading.zoom === 'schedule' && (
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin mr-2"></div>
                  )}
                  <span className="text-gray-700">Schedule meeting</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCallDropdown;