"use client";

import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { TransactionType } from "../../billing/transaction/transactiontype";

export const columns: ColumnDef<TransactionType>[] = [
  {
    accessorKey: "name",
    header: () => (
      <div
        className="w-[200px] text-[14px]   font-medium"
        //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Contact Name
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-[14px] font-normal">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "email",
    header: () => (
      <Button
        variant="ghost"
        className="text-base text-[14px] font-medium"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Account Name
      </Button>
    ),
    cell: ({ row }) => (
      <div className="pl-6 text-[14px] font-normal">
        {row.getValue("email")}
      </div>
    ),
  },
  {
    accessorKey: "name",
    header: () => (
      <div
        className="w-[200px] text-[14px]   font-medium"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Email
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-[14px] font-normal">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "name",
    header: () => (
      <div
        className="w-[200px] text-[14px]   font-medium"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Subject
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-[14px] font-normal">{row.getValue("name")}</div>
    ),
  },
  {
    accessorKey: "name",
    header: () => (
      <div
        className="w-[200px] text-[14px]   font-medium"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Description
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-[14px] font-normal">{row.getValue("name")}</div>
    ),
  },

  {
    accessorKey: "name",
    header: () => (
      <div
        className="w-[200px] text-[14px]   font-medium"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Product Name
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-[14px] font-normal">{row.getValue("name")}</div>
    ),
  },
];
