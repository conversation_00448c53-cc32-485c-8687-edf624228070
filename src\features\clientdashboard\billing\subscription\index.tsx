import { BackButton } from "@/_components/common";
import { useEffect, useState } from "react";
import { getSubscriptionDetails, getUserProfile } from "../billing_api/billingapi";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";

export default function Subscription() {
  const [subscriptions, setSubscriptions] = useState<any[]>([]);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(true);
  const [selectedSubscription, setSelectedSubscription] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setProfileLoading(true);
        
        // First fetch subscriptions (which was working previously)
        const subscriptionsResponse = await getSubscriptionDetails();
        if (subscriptionsResponse?.status === 1) {
          setSubscriptions(subscriptionsResponse.data);
        }

        // Then fetch user profile (new addition)
        const profileResponse = await getUserProfile();
        if (profileResponse?.status === 1) {
          setUserProfile(profileResponse.data);
        }
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading(false);
        setProfileLoading(false);
      }
    };

    fetchData();
  }, []);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy");
  };

  const openDetailsModal = (subscription: any) => {
    setSelectedSubscription(subscription);
    setIsDialogOpen(true);
  };

  return (
    <div className="max-w-full mx-8">
      <div className="flex flex-row mb-6 items-center">
        <BackButton />
        <h2 className="text-2xl font-bold text-[#292929]">
          Subscription Details
        </h2>
      </div>

      {/* Payment Details Section - New Addition */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Payment Details</h3>
        <div className="bg-red-50 border border-[#FF577F] rounded-md p-6 flex justify-between items-start">
          <div>
            <p className="font-semibold text-[#5E5E5E] text-[18px]">
              {profileLoading ? "Loading..." : 
               userProfile ? `${userProfile.name}${userProfile.lastname ? ` ${userProfile.lastname}` : ''}` : "User Name"}
            </p>
            <p className="text-[#5E5E5E] text-sm">View Visa Card</p>
          </div>
          <button className="border border-[#FF577F] text-[#FF577F] px-4 py-2 rounded-md hover:bg-pink-100">
            Update Payment Details
          </button>
        </div>
      </div>

      {/* Current Plan Section - From Working Version */}
      <div className="mb-8">
        <h3 className="text-[20px] font-semibold mb-4 text-[#282828]">
          Current Plans
        </h3>
        
        {loading ? (
          <div>Loading...</div>
        ) : subscriptions.length > 0 ? (
          <div className="space-y-4">
            {subscriptions.map((subscription, index) => (
              <div
                key={index}
                className="bg-white border border-[#FF577F] rounded-md p-6 flex justify-between items-start shadow-sm"
              >
                <div>
                  <p className="font-semibold lg:text-[18px] text-[15px] text-[#5E5E5E]">
                    {subscription.package.name}
                    <span className="font-bold ml-3">
                      ₹{subscription.package.price}
                    </span>{" "}
                    | 1 member
                  </p>
                  <p className="text-sm text-gray-600 mt-2">
                    Active until {formatDate(subscription.endDate)}
                    <span className="text-[#FF577F] ml-2 cursor-pointer">
                      Cancel Subscription
                    </span>
                  </p>
                </div>
                <button
                  onClick={() => openDetailsModal(subscription)}
                  className="border border-[#FF577F] text-[#FF577F] px-8 py-2 rounded-md hover:bg-pink-100"
                >
                  View Plan Details
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div>No active subscriptions found</div>
        )}
      </div>

      {/* Details Dialog Modal - From Working Version */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-[#FF577F] text-[25px]">Subscription Details</DialogTitle>
          </DialogHeader>
          {selectedSubscription && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Package Name
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {selectedSubscription.package.name}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Price</h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    ₹{selectedSubscription.package.price}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Start Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.startDate)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    End Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.endDate)}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Next Billing Date
                  </h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {formatDate(selectedSubscription.next_billing_date)}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-500">Status</h4>
                  <p className="text-sm mt-1 py-3 px-3 rounded-xl border-gradient bg-[#F9EFEF]">
                    {selectedSubscription.status}
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}