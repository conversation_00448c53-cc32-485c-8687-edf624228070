

// annoantor history
import { customAxios } from "@/utils/axio-interceptor";

export const getAnnonatorHistory = async (id: string, fromDate?: string, toDate?: string) => {
  // Add query parameters for date filtering if provided
  let url = `/v1/attendance/history/${id}`;
  const params: Record<string, string> = {};

  if (fromDate) {
    params.from = fromDate;
  }

  if (toDate) {
    params.to = toDate;
  }

  const response = await customAxios.get(url, { params });
  return response.data;
};
