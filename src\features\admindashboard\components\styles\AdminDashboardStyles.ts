import { useResponsive } from "@/hooks/use-responsive";
import { useState, useEffect } from "react";

// Hook to get responsive admin dashboard styles
export const useAdminDashboardStyles = () => {
  const { isLaptopMd } = useResponsive();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  // Update window width on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Common styles for all screen sizes
  const commonStyles = {
    container: "w-full flex flex-col mx-auto gap-4",
    topSection: "grid justify-center gap-5",
    statCard: "flex flex-col rounded-md px-3 py-3 border-gradient gap-2",
    statIconContainer: "flex items-center gap-3",
    statIcon: "text-[#D53148]",
    statTitle: "text-[#545454] font-medium",
    statValue: "text-[#545454] font-semibold",
    contentSection: "flex justify-between gap-12 mt-5",
  };

  // Get styles based on screen size
  const getStyles = () => {
    // 1024px
    if (windowWidth <= 1024) {
      return {
        ...commonStyles,
        container: `${commonStyles.container} px-6`,
        topSection: `${commonStyles.topSection} grid-cols-2`,
        statCard: `${commonStyles.statCard} w-full h-[90px]`,
        statTitle: `${commonStyles.statTitle} text-base`,
        statValue: `${commonStyles.statValue} text-xl`,
        contentSection: `${commonStyles.contentSection} flex-col`,
      };
    }
    // 1440px
    else if (isLaptopMd) {
      return {
        ...commonStyles,
        container: `${commonStyles.container} px-8`,
        topSection: `${commonStyles.topSection} grid-cols-4 gap-x-8`,
        statCard: `${commonStyles.statCard} w-[260px] h-[94px]`,
        statTitle: `${commonStyles.statTitle} text-lg`,
        statValue: `${commonStyles.statValue} text-2xl`,
        contentSection: `${commonStyles.contentSection}`,
      };
    }
    // 2560px (4K)
    else {
      return {
        ...commonStyles,
        container: `${commonStyles.container} px-10`,
        topSection: `${commonStyles.topSection} grid-cols-4 gap-x-[5rem]`,
        statCard: `${commonStyles.statCard} w-[400px] h-[110px]`,
        statTitle: `${commonStyles.statTitle} text-xl`,
        statValue: `${commonStyles.statValue} text-3xl`,
        contentSection: `${commonStyles.contentSection} gap-16`,
      };
    }
  };

  return getStyles();
};
