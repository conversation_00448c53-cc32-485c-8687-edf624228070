"use client";

import {  ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useInfiniteCoordinators, useInfiniteUsers } from "../api/query";
import { useUpdateAnnotatorCoordinatorMutation } from "../api/mutation";
import { useSelection } from "@/context/matchmaking.context";
import { ClientData } from "@/types/matchmaking.types";
import { toast } from "react-toastify";
import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";

const formatDate = (dateStr?: string | null) => {
  if (!dateStr) return "-";
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "-"; // Invalid date check

    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = String(date.getFullYear()).slice(-2);
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error("Error formatting date:", error, dateStr);
    return "-";
  }
};

const formatShiftTiming = (
  availableFrom: string | null,
  availableTo: string | null
): string => {
  if (!availableFrom || !availableTo) {
    return "Not assigned";
  }

  // Convert to 12-hour format
  const formatTime = (timeString: string) => {
    try {
      // Check if timeString is already in HH:MM format
      if (timeString.includes(":")) {
        const [hours, minutes] = timeString.split(":");
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? "PM" : "AM";
        const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM
        return `${hour12}:${minutes} ${ampm}`;
      }

      // If it's a full ISO date string
      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        return "Invalid time";
      }

      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return "Invalid time";
    }
  };

  const fromFormatted = formatTime(availableFrom);
  const toFormatted = formatTime(availableTo);

  return `${fromFormatted} - ${toFormatted}`;
};

// useAdminColumns hook definition
export const useAdminColumns = () => {
  const navigate = useNavigate()
   const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<ClientData | null>(null);
  
  const columns = useMemo<ColumnDef<ClientData>[]>(() => [
  
   {
      accessorKey: "client",
      header: () => (
        <div
          className="w-[120px] text-[14px] font-medium cursor-pointer"
          //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Client
        </div>
      ),
      cell: ({ row }) => (
        <div
          className="pl-2 text-[14px] font-normal cursor-pointer hover:underline"
          onClick={() => {
            if (!row.original.subscriptionId) {
              toast.error("No subscription available for this client");
              return;
            }
            setSelectedClient(row.original);
            setIsModalOpen(true);
            navigate(`?subscriptionId=${row.original.subscriptionId}`);
          }}
        >
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "startDate",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Started on
        </Button>
      ),
      cell: ({ row }) => {
        // Access startDate directly from the row data if available
        const startDate =
          row.original?.startDate ||
          (row.original?.Subscription &&
            (Array.isArray(row.original.Subscription)
              ? row.original.Subscription[0]?.startDate
              : row.original.Subscription?.startDate));

        return (
          <div className="pl-6 text-[14px] font-normal">
            {formatDate(startDate)}
          </div>
        );
      },
    },
    {
      accessorKey: "shifttiming",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Shift Timing
        </Button>
      ),
      cell: ({ row }) => {
        // Check if row.original exists
        if (!row.original) {
          return <div className="pl-4 text-[12px] font-normal">-</div>;
        }

        // First try to get from clientPackageDetails if available
        let availableFrom = null;
        let availableTo = null;

        // Use type assertion to handle clientPackageDetails
        const clientData = row.original as ClientData;

        if (
          clientData.clientPackageDetails &&
          clientData.clientPackageDetails.length > 0
        ) {
          availableFrom = clientData.clientPackageDetails[0].availableFrom;
          availableTo = clientData.clientPackageDetails[0].availableTo;
        } else {
          // Fallback to direct properties
          availableFrom = clientData.availableFrom;
          availableTo = clientData.availableTo;
        }

        return (
          <div className=" pl-2 text-[14px] font-normal">
            {formatShiftTiming(availableFrom, availableTo)}
          </div>
        );
      },
    },
    {
      accessorKey: "enddate",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          End Date
        </Button>
      ),
      cell: ({ row }) => {
        // Access endDate directly from the row data if available
        const endDate =
          row.original?.endDate ||
          (row.original?.Subscription &&
            (Array.isArray(row.original.Subscription)
              ? row.original.Subscription[0]?.endDate
              : row.original.Subscription?.endDate));

        return (
          <div className="pl-6 text-[14px] font-normal">
            {formatDate(endDate)}
          </div>
        );
      },
    },
    {
      accessorKey: "subscription",
      header: () => (
        <Button
          variant="ghost"
          className="text-[14px] font-medium"
        //  onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Subscription
        </Button>
      ),
      cell: ({ row }) => {
        // Access package name directly from the row data if available
        const packageName =
          row.original?.packageName ||
          (row.original?.Subscription &&
            (Array.isArray(row.original.Subscription)
              ? row.original.Subscription[0]?.package?.name
              : row.original.Subscription?.package?.name));

        return (
          <div className="pl-8 text-[14px] font-normal">
            {packageName || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "annotator",
      header: () => <div className="text-[14px] font-medium">Annotator</div>,
      cell: ({ row }) => {
        // Add null check for row.original
        if (!row.original) {
          console.error("Row original is undefined in annotator cell");
          return <div>Error: Invalid data</div>;
        }

        const { selectedAnnotators, setSelectedAnnotators } = useSelection();

        // Safely access assignmentsAsClient with null check
        const assignmentsAsClient = row.original.assignmentsAsClient || [];
        const id = assignmentsAsClient[0]?.developerId;

        // Safely access client ID with null check
        const clientId = row.original.id;

        // Safely access Subscription array and package
        // Subscription can be an array or a single object based on API response
        const subscription = Array.isArray(row.original.Subscription)
          ? row.original.Subscription[0]
          : row.original.Subscription;

        // Only use clientId for lookup if it's defined
        const selectedAnnotator = clientId
          ? selectedAnnotators[clientId] || ""
          : "";

        const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
          useInfiniteUsers();

        // Get client's package ID from subscription or packageId field with null checks
        const packageId = subscription?.package?.id || row.original.packageId;

        // Get all annotators with null check
        const allAnnotators = data?.pages?.flatMap((page) => page.data) || [];

        // Filter annotators to only show those with matching packageId
        // If no annotators match, show all annotators
        let annotators = packageId
          ? allAnnotators.filter(
              (annotator) => annotator.packageId === packageId
            )
          : allAnnotators;

        // If no annotators match the package, show all annotators
        if (annotators.length === 0 && allAnnotators.length > 0) {
          annotators = allAnnotators;
        }

        const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
          const target = e.currentTarget;
          const isBottom =
            target.scrollHeight - target.scrollTop === target.clientHeight;

          if (isBottom && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        };

        // If clientId is undefined, we can't properly set the selected annotator
        if (!clientId) {
          return (
            <div className="text-sm text-red-500 ">Client ID is missing</div>
          );
        }

        // Log the client ID and selected annotator for debugging
        console.log("Annotator cell - Client ID:", clientId);
        console.log("Annotator cell - Selected Annotator:", selectedAnnotator);

        return (
          <Select
            onValueChange={(value) => {
              console.log(
                "Setting annotator for client ID:",
                clientId,
                "to value:",
                value
              );
              setSelectedAnnotators((prev) => ({ ...prev, [clientId]: value }));
            }}
            defaultValue={selectedAnnotator || id || ""}
          >
            <SelectTrigger className="border-gradient bg-[#F9EFEF]">
              <SelectValue placeholder="Select Annotator" />
              {/* select assign annonator ka name add rahe */}
            </SelectTrigger>

            <SelectContent
              onScroll={handleScroll}
              className="max-h-60 overflow-y-auto"
            >
              {annotators.length > 0 ? (
                annotators.map((annotator) => (
                  <SelectItem key={annotator.id} value={annotator.id}>
                    {annotator.name}
                    {packageId && annotator.packageId !== packageId && (
                      <span className="ml-2 text-xs text-yellow-500">
                        (Plan mismatch)
                      </span>
                    )}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-sm text-center text-muted-foreground">
                  {allAnnotators.length > 0
                    ? "No annotators with matching package found"
                    : "No annotators available"}
                </div>
              )}
              {isFetchingNextPage && (
                <div className="p-2 text-sm text-center text-muted-foreground">
                  Loading more...
                </div>
              )}
            </SelectContent>
          </Select>
        );
      },
    },
    {
      accessorKey: "coordinator",
      header: () => (
        <div className="text-[14px] font-semibold">Coordinator</div>
      ),
      cell: ({ row }) => {
        // Add null check for row.original
        if (!row.original) {
          console.error("Row original is undefined in coordinator cell");
          return <div>Error: Invalid data</div>;
        }

        const { selectedCoordinators, setSelectedCoordinators } =
          useSelection();

        // Safely access assignmentsAsClient with null check
        const id = row.original.assignmentsAsClient?.[0]?.coordinatorId || "";

        // Safely access client ID with null check
        const clientId = row.original.id;

        // Add null check for clientId
        if (!clientId) {
          console.error("Client ID is undefined in coordinator cell");
          return <div>Error: Invalid client ID</div>;
        }

        const selectedCoordinator = selectedCoordinators[clientId] || "";
        const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
          useInfiniteCoordinators();

        // Safely access coordinators with null check
        const coordinators = data?.pages?.flatMap((page) => page.data) || [];

        const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
          const target = e.currentTarget;
          const isBottom =
            target.scrollHeight - target.scrollTop === target.clientHeight;

          if (isBottom && hasNextPage && !isFetchingNextPage) {
            fetchNextPage();
          }
        };

        return (
          <Select
            onValueChange={(value) =>
              setSelectedCoordinators((prev) => ({
                ...prev,
                [clientId]: value,
              }))
            }
            defaultValue={selectedCoordinator || id || ""}
          >
            <SelectTrigger className="border-gradient bg-[#F9EFEF]">
              <SelectValue placeholder="Select Coordinator" />
              {/* select assign coordinator ka name add rahe */}
            </SelectTrigger>

            <SelectContent
              onScroll={handleScroll}
              className="max-h-60 overflow-y-auto"
            >
              {coordinators.map((coordinator) => (
                <SelectItem key={coordinator.id} value={coordinator.id}>
                  {coordinator.name}
                </SelectItem>
              ))}
              {isFetchingNextPage && (
                <div className="p-2 text-sm text-center text-muted-foreground">
                  Loading more...
                </div>
              )}
            </SelectContent>
          </Select>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-[14px] font-medium">Actions</div>, //actionsss
      cell: ({ row }) => {
        // Add null check for row.original
        if (!row.original) {
          console.error("Row original is undefined");
          return <div>Error: Invalid data</div>;
        }

        const id = row.original.id;

        // Add null check for id
        if (!id) {
          console.error("Client ID is undefined");
          return <div>Error: Invalid client ID</div>;
        }

        const { selectedAnnotators, selectedCoordinators } = useSelection();
        const { mutate: updateAnnotatorCoordinator } =
          useUpdateAnnotatorCoordinatorMutation();
        const { data: annotatorData } = useInfiniteUsers();

        // Use safe access with nullish coalescing
        // Get the selected annotator and coordinator for this client
        const annotator = selectedAnnotators[id] || "";
        const coordinator = selectedCoordinators[id] || "";

        // Log the selected values for debugging
        console.log("Action cell - Client ID:", id);
        console.log(
          "Action cell - Selected Annotators object:",
          selectedAnnotators
        );
        console.log(
          "Action cell - Selected Annotator for this client:",
          annotator
        );
        console.log(
          "Action cell - Selected Coordinator for this client:",
          coordinator
        );

        // Get client's package ID (ensure it's a string)
        // Handle Subscription as array or object
        const subscription = Array.isArray(row.original.Subscription)
          ? row.original.Subscription[0]
          : row.original.Subscription;
        const packageId: string =
          subscription?.package?.id || row.original.packageId || "";

        // Find the selected annotator's data to check package match
        const allAnnotators =
          annotatorData?.pages?.flatMap((page) => page.data) || [];
        const selectedAnnotatorData = annotator
          ? allAnnotators.find((a) => a.id === annotator)
          : null;

        // Log package IDs for debugging
        console.log("Action column - Client Package ID:", packageId);
        console.log(
          "Action column - Selected Annotator:",
          selectedAnnotatorData
        );

        // Check if packages match (handle empty string case)
        // If client has no package, allow any annotator
        // If annotator has no package, don't allow assignment
        let packagesMatch = false;

        if (!packageId) {
          // If client has no package, any annotator is fine
          packagesMatch = true;
          console.log("Client has no package, any annotator is allowed");
        } else if (selectedAnnotatorData && selectedAnnotatorData.packageId) {
          // Both have packages, check if they match
          packagesMatch = selectedAnnotatorData.packageId === packageId;
          console.log("Package match result:", packagesMatch);
        } else {
          // Annotator has no package, don't allow assignment
          packagesMatch = false;
          console.log("Annotator has no package, assignment not allowed");
        }

        const handleSave = () => {
          // Double-check that we have a valid row and ID
          if (!row.original || !id) {
            toast.error("Invalid client data");
            return;
          }

          // Log the client ID and row data for debugging
          console.log("Save button clicked for client ID:", id);
          console.log("Row data:", row.original);

          // Get client's availability times
          const clientData = row.original as ClientData;
          let availableFrom = null;
          let availableTo = null;

          // First try to get from clientPackageDetails if available
          if (
            clientData.clientPackageDetails &&
            clientData.clientPackageDetails.length > 0
          ) {
            availableFrom = clientData.clientPackageDetails[0].availableFrom;
            availableTo = clientData.clientPackageDetails[0].availableTo;
          } else {
            // Fallback to direct properties
            availableFrom = clientData.availableFrom;
            availableTo = clientData.availableTo;
          }

          // Make sure packageId is not null
          if (!packageId) {
            toast.error("Client has no package assigned");
            return;
          }

          // Get the selected annotator and coordinator directly from the context
          const currentAnnotator = selectedAnnotators[id] || "";
          const currentCoordinator = selectedCoordinators[id] || "";

          console.log("Current selected annotator:", currentAnnotator);
          console.log("Current selected coordinator:", currentCoordinator);

          // Create formData with the correct types according to the API requirements
          const formData = {
            clientId: id,
            developerId: currentAnnotator, // Use the current selected annotator
            coordinatorId: currentCoordinator, // Use the current selected coordinator
            packageId: packageId, // Include the package ID
            availableFrom: availableFrom || null, // null if not available
            availableTo: availableTo || null, // null if not available
          };

          // Log the form data being sent to the API
          console.log("Sending form data to API:", formData);

          // Check if developerId is empty and show a warning
          if (!formData.developerId) {
            console.warn("Warning: developerId is empty in form data");
          }

          // Check if coordinatorId is empty and show a warning
          if (!formData.coordinatorId) {
            console.warn("Warning: coordinatorId is empty in form data");
          }

          updateAnnotatorCoordinator(formData);
        };

        // Get the current selected values directly from the context
        const currentAnnotator = selectedAnnotators[id] || "";
        const currentCoordinator = selectedCoordinators[id] || "";
        const isSaveDisabled = !currentAnnotator && !currentCoordinator;

        return (
          <div className="flex flex-row gap-2">
            <Button
              variant="gradient"
              onClick={handleSave}
              className="px-8 text-[14px] font-normal"
              disabled={isSaveDisabled}
              title={
                isSaveDisabled
                  ? "Please select an annotator or coordinator"
                  : ""
              }
            >
              Save
            </Button>
            {/* <Button
              variant="ghost"
              className="border-[#FF577F] text-[#FF577F] border hover:text-[#e7476c] px-8"
            >
              Close
            </Button> */}
          </div>
        );
      },
    },
  

  ], [])

 return {
    columns,
    isModalOpen,
    selectedClient,
    openModal: (client: ClientData) => {
      setSelectedClient(client);
      setIsModalOpen(true);
      if (client.subscriptionId) {
        navigate(`?subscriptionId=${client.subscriptionId}`);
      } else {
        navigate("");
      }
    },
    closeModal: () => {
      setIsModalOpen(false);
      setSelectedClient(null);
      navigate("");
    },
  };
  
};
