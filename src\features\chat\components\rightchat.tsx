import { useState } from "react";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";
import { FiFileText } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import GroupMembersName from "./rightchatcomponent/groupmembersname";

type Media = {
  id: string;
  fileUrl: string;
  fileType: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
  };
};

type RightChatProps = {
  selectedUser: {
    id: string;
    name: string;
    avatar?: string;
    isGroup?: boolean;
  } | null;
  imageMedia: Media[];
  pdfMedia: Media[];
  scrollToMessage: (text: string) => void;
};

// Format date like WhatsApp (e.g., "24/05/2025")
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

// Extract and truncate filename from URL
const getFileName = (url: string) => {
  const parts = url.split("/");
  const fullName = parts[parts.length - 1];
  // Show first 7 characters + extension
  if (fullName.length > 10) {
    const extension = fullName.split(".").pop();
    const nameWithoutExtension = fullName.replace(`.${extension}`, "");
    return `${nameWithoutExtension.substring(0, 7)}...${extension}`;
  }
  return fullName;
};

const RightChat = ({
  selectedUser,
  imageMedia,
  pdfMedia,
  scrollToMessage,
}: RightChatProps) => {
  const [showAllDocs, setShowAllDocs] = useState(false);
  const [showAllMedia, setShowAllMedia] = useState(false);

  if (!selectedUser) return null;

  // Determine how many items to display based on toggle state
  const displayedImages = showAllMedia ? imageMedia : imageMedia.slice(0, 3);
  const displayedPdfs = showAllDocs ? pdfMedia : pdfMedia.slice(0, 2);

  return (
    <div className="p-1 border  h-full overflow-y-auto px-1">
      <div className="flex flex-col items-center gap-2">
        <img
          src={selectedUser.avatar || getAvatarUrl(selectedUser.name)}
          className="w-20 h-20 rounded-full"
        />
        <p className="font-semibold">{selectedUser.name}</p>
        <p className="text-sm text-gray-400">Profile</p>
      </div>

      {/* Media photos files show */}
      <div className="mt-6">
        <p className="font-bold mb-2">Media Files</p>
        <div className="grid grid-cols-3 gap-2 px-1">
          {displayedImages.map((media: Media) => (
            <img
              key={media.id}
              src={media.fileUrl}
              className="w-full h-20 object-cover cursor-pointer rounded-md"
              onClick={() => scrollToMessage(media.fileUrl)}
            />
          ))}
        </div>
        {imageMedia.length > 3 && (
          <Button
            variant={"gradient"}
            className="mt-2 text-xs px-4 text-white "
            onClick={() => setShowAllMedia(!showAllMedia)}
          >
            {showAllMedia ? "See less" : "See all"}
          </Button>
        )}
      </div>

      {/* Documents files show */}
      <div className="mt-6">
        <p className="font-bold mb-2">Documents</p>
        <div className="flex flex-col gap-2">
          {displayedPdfs.map((media: Media) => (
            <button
              key={media.id}
              onClick={() => scrollToMessage(media.fileUrl)}
              className="flex items-center gap-3 p-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              <FiFileText className="text-gray-500 text-xl" />
              <div className="flex-1 text-left">
                <p
                  className="text-sm text-gray-800 truncate"
                  title={getFileName(media.fileUrl)}
                >
                  {getFileName(media.fileUrl)}
                </p>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Unknown size</span>
                  <span>{formatDate(media.createdAt)}</span>
                </div>
              </div>
            </button>
          ))}
        </div>
        {pdfMedia.length > 2 && (
          <Button
            variant={"gradient"}
            className="mt-2 text-xs text-white  "
            onClick={() => setShowAllDocs(!showAllDocs)}
          >
            {showAllDocs ? "See less" : "See all"}
          </Button>
        )}
      </div>

      {/* Only show GroupMembersName for group chats */}
      <GroupMembersName
        groupId={selectedUser.id}
        isGroup={selectedUser.isGroup || false}
      />
    </div>
  );
};

export default RightChat;
