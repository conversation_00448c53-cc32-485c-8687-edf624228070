import React from "react";
// import { Task } from "@/types/adminkanbantype";
import { useDraggable } from "@dnd-kit/core";
import { Column } from "./adminkanban";
import { motion } from "framer-motion";
import { CheckCircle2, Circle, Clock, Trash2, Pencil } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import AdminUpdateTask from "./updatetask";
import { DetailsTask } from "@/types/kanbantasktype";

interface DraggableTaskProps {
  task: DetailsTask;
  column: Column;
  onDelete: (task: DetailsTask) => void;
  fetchTasks: () => void;
}

export function DraggableTask({
  task,
  column,
  onDelete,
  fetchTasks,
}: DraggableTaskProps) {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: task.id,
    data: { task, column },
  });

  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);

  // Get icon based on column
  const getTaskIcon = (columnId: string) => {
    switch (columnId) {
      case "todo":
        return <Circle className="h-4 w-4 text-blue-500" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "done":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default:
        return null;
    }
  };

  // Get indicator color based on column
  // const getTitleIndicatorColor = (columnId: string) => {
  //   switch (columnId) {
  //     case "todo":
  //       return "bg-[#2525AB]";
  //     case "in-progress":
  //       return "bg-[#E96B1C]";
  //     case "done":
  //       return "bg-[#E91C24]";
  //     default:
  //       return "bg-gray-400";
  //   }
  // };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
    >
      {/* Task Card with colored border */}
      <Card
        ref={setNodeRef}
        {...attributes}
        {...listeners}
        className={`mb-3 shadow-sm transition-shadow hover:shadow-md relative cursor-move ${
          isDragging ? "opacity-50" : ""
        }`}
        style={{
          borderLeft: `4px solid ${task.color || "#60A5FA"}`,
          borderTop: `1px solid ${task.color || "#60A5FA"}20`,
          borderRight: `1px solid ${task.color || "#60A5FA"}20`,
          borderBottom: `1px solid ${task.color || "#60A5FA"}20`,
        }}
      >
        <CardHeader className="p-3">
          {/* Due Date */}
          <div className="flex justify-end">
            <h1 className="font-normal text-xs">
              Due Date:{" "}
              {task.dueDate
                ? new Date(task.dueDate).toLocaleDateString()
                : "N/A"}
            </h1>
          </div>

          {/* Task Title and Actions */}
          <div className="flex items-center justify-between overflow-hidden">
            <div className="flex items-center flex-grow">
              {getTaskIcon(column.id)}
              <CardTitle className="text-sm font-medium ml-1 line-clamp-4 w-28">
                {task.title?.trim().split(/\s+/).slice(0, 6).join(" ")}
                {task.title?.trim().split(/\s+/).length > 6 ? "..." : ""}
              </CardTitle>
            </div>
            <div
              className="flex ml-2 z-50"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onPointerDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              {/* Edit Button */}
              <Button
                variant="ghost"
                size="icon"
                className="cursor-pointer p-1 h-auto w-auto"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setIsEditModalOpen(true);
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onPointerDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <Pencil className="h-4 w-4" />
              </Button>

              {/* Custom Dialog */}
              {isEditModalOpen && (
                <div
                  className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
                  onClick={() => setIsEditModalOpen(false)}
                  style={{ pointerEvents: "auto" }}
                >
                  <div
                    className="bg-white p-4 rounded-lg w-[600px] max-h-[90vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                    onMouseDown={(e) => e.stopPropagation()}
                    onPointerDown={(e) => e.stopPropagation()}
                    style={{ pointerEvents: "auto" }}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <h2 className="text-xl font-semibold">Edit Task</h2>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                        onClick={() => setIsEditModalOpen(false)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <line x1="18" y1="6" x2="6" y2="18"></line>
                          <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                      </Button>
                    </div>

                    <div className="pointer-events-auto">
                      <AdminUpdateTask
                        taskId={task.id}
                        onTaskUpdated={() => {
                          fetchTasks();
                          setIsEditModalOpen(false);
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
              {/* Delete Button */}
              <Button
                variant="ghost"
                size="icon"
                className="cursor-pointer p-1 h-auto w-auto z-30"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onDelete(task);
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onPointerDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                aria-label={`Delete task: ${task.title}`}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Task Description */}
          <CardDescription className="text-xs line-clamp-2 mt-2">
            {task.description}
          </CardDescription>

          {/* Priority Level Indicator */}
          <div className="flex justify-end mt-2">
            <div
              className="w-5 h-5 flex justify-center items-center rounded-full text-white"
              style={{ backgroundColor: task.color || "#60A5FA" }}
            >
              <p className="text-xs">{task.level}</p>
            </div>
          </div>
        </CardHeader>
      </Card>
    </motion.div>
  );
}
