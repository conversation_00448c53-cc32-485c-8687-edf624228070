// @ts-ignore
import React from 'react'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'


const AdminBillingData = () => {
  // Dummy data
  const billingData = {
    sameAsBusiness: true,
    country: 'South Africa',
    state: 'Kingston',
    taxId: '09AA68JOJ118586',
    address: 'Park Street view, Cartersville',
    postalCode: '201301'
  }

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-lg space-y-6">
      <h2 className="text-xl font-semibold">Billing Address</h2>
      
      <div className="flex items-center space-x-2">
        <Checkbox id="same-address" defaultChecked={billingData.sameAsBusiness} className='bg-[#F9EFEF]'/>
        <Label htmlFor="same-address">Same as Business Address</Label>
      </div>

      <div className="flex flex-col gap-6">
        <div className="flex flex-row gap-2">
          <div className=" w-1/2">
            <Label>Country</Label>
            <div className='border-gradient rounded-lg'>
                <Input 
              value={billingData.country} 
              className="bg-[#F9EFEF] text-[#5E5E5E]"
              readOnly
            />
            </div>
          </div>

          <div className="w-1/2">
            <Label>State</Label>
            <div className='border-gradient rounded-lg'>
                <Input 
              value={billingData.state} 
              className="bg-[#F9EFEF] text-[#5E5E5E] "
              readOnly
            />
            </div>
          </div>
        </div>

        <div className="flex-row flex gap-4 w-full">
          <div className="w-1/2">
            <Label>QST/VAT (Optional)</Label>
           <div className='border-gradient rounded-lg'>
             <Input 
              value={billingData.taxId} 
              className="bg-[#F9EFEF] text-[#5E5E5E]"
              readOnly
            />
           </div>
          </div>

           <div className="w-1/2">
            <Label>Address</Label>
           <div className='border-gradient rounded-lg'>
             <Input 
              value={billingData.taxId} 
              className="bg-[#F9EFEF] text-[#5E5E5E]"
              readOnly
            />
           </div>
          </div>

        </div>


        <div className='w-full flex flex-row gap-4'>
            <div className="w-1/2">
          <Label>Postal Code</Label>
        
            <div className='border-gradient rounded-lg'>
                <Input 
            value={billingData.postalCode} 
            className="bg-[#F9EFEF] text-[#5E5E5E] rounded-lg"
            readOnly
          />
            </div>
        
        </div>
        </div>
      </div>

    </div>
  )
}

export default AdminBillingData