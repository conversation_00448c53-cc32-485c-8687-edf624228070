import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate, useLocation } from "react-router-dom";
import { useCoordinatorClientsList } from "../api/useCoordinatorClientsQuery";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import NoData from "@/_components/common/nodata";

const AdminCoodinator = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get coordinator ID and name from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const coordinatorId = queryParams.get('id');
  const coordinatorName = queryParams.get('name');

  // Log the URL parameters for debugging
  console.log("URL Search:", location.search);
  console.log("Coordinator ID:", coordinatorId);
  console.log("Coordinator Name:", coordinatorName);

  // Check if coordinator ID is missing
  if (!coordinatorId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>Missing coordinator ID. Please select a coordinator from the coordinators page.</p>
          <button
            onClick={() => navigate('/admin/coordinators')}
            className="mt-4 px-4 py-2 bg-[#FF577F] text-white rounded-md hover:bg-[#ff3c6a]"
          >
            Go to Coordinators
          </button>
        </div>
      </div>
    );
  }

  const columns = useAdminColumns(); // Call the hook at the component level

  const { data, isLoading, error } = useCoordinatorClientsList(coordinatorId);

  if (isLoading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>{(error as Error).message}</p>
        </div>
      </div>
    );
  }

  const clientsData = data?.data?.data || [];

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />

        <h1 className="text-[#282828] text-[24px]">
          {coordinatorName}'s Clients
        </h1>
      </div>

      {clientsData.length > 0 ? (
        <DataTable
          title="Clients"
          columns={columns}
          data={clientsData}
          loading={false}
          disablePagination
        />
      ) : (
        <NoData />
      )}
    </div>
  );
};

export default AdminCoodinator;
