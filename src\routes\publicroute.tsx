import { useSelector } from "react-redux";
import { Navigate, Outlet } from "react-router-dom";
import { RootState } from "@/store";

const PublicRoute = () => {
  const accessToken = useSelector((state: RootState) => state.auth.accessToken);
  const user = useSelector((state: RootState) => state.auth.user);

  if (accessToken && user) {
    switch (user.role) {
      case "CLIENT":
      case "COWORKER":
        return <Navigate to="/dashboard" />;
      case "ANNOTATOR":
        return <Navigate to="/annotator/list" />;
      case "ADMIN":
        return <Navigate to="/admin/dashboard" />;
      case "PROJECT_COORDINATOR":
        return <Navigate to="/coordinator/dashboard" />;
      default:
        return <Navigate to="/dashboard" />;
    }
  }

  return <Outlet />;
};

export default PublicRoute;
