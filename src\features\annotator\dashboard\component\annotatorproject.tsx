import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getAnnonatorProjects } from "../../annonator_api/annonator_api";
import { getAvatarUrl } from "@/store/dicebearname/getAvatarUrl";

type Project = {
  id: string;
  name: string;
  description: string;
  startDate: string;
  dueDate: string;
  status: string;
  priority: string;
  createdBy: {
    name: string;
  };
  tasks?: any[];
};

const ProjectList: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await getAnnonatorProjects();

        if (response && response.status === 1 && Array.isArray(response.data)) {
          // Only take the first 8 projects for the dashboard widget
          setProjects(response.data.slice(0, 8));
        } else {
          setProjects([]);
        }
      } catch (err) {
        console.error("Error fetching projects:", err);
        setError("Failed to load projects");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Calculate tasks count - if tasks array exists, use its length, otherwise default to 0
  const getTasksCount = (project: Project) => {
    return project.tasks ? project.tasks.length : 0;
  };

  // Truncate project name to 2 words
  const truncateName = (name: string) => {
    const words = name.split(' ');
    return words.slice(0, 2).join(' ') + (words.length > 2 ? '...' : '');
  };

  return (
    <div className="bg-[#F3F3F3] shadow-md rounded-lg
      lg-only:p-3 lg-only:w-full lg-only:h-[220px]
      xl:p-4 xl:w-[261px] xl:h-[246px]
      2xl:p-5 2xl:w-[300px] 2xl:h-[280px]
      flex flex-col">
      <div className="flex justify-between items-center mb-3">
        <h2 className="lg-only:text-base xl:text-lg 2xl:text-xl font-semibold font-poppins">Projects</h2>

        <button
          onClick={() => navigate("/annotator/annotatordetail")}
          className="lg-only:text-[10px] lg-only:px-2 lg-only:py-0.5
            xl:text-xs xl:px-3 xl:py-1
            2xl:text-sm 2xl:px-4 2xl:py-1.5
            font-semibold border border-gradient text-red-500 rounded-full hover:bg-red-50 transition-all"
        >
          View All
        </button>
      </div>

      {loading ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="lg-only:text-xs xl:text-sm 2xl:text-base text-gray-500">Loading projects...</p>
        </div>
      ) : error ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="lg-only:text-xs xl:text-sm 2xl:text-base text-red-500">{error}</p>
        </div>
      ) : projects.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="lg-only:text-xs xl:text-sm 2xl:text-base text-gray-500">No projects available</p>
        </div>
      ) : (
        <ul className="CustomScroll overflow-y-auto pr-1">
          {projects.map((project) => (
            <li key={project.id} className="flex items-center gap-3 mb-3 last:mb-0">
              <img
                src={getAvatarUrl(project.name)}
                alt="project avatar"
                className="lg-only:w-5 lg-only:h-5 xl:w-6 xl:h-6 2xl:w-8 2xl:h-8 rounded-full object-cover"
              />
              <div className="flex-1">
                <p className="font-medium lg-only:text-[10px] xl:text-xs 2xl:text-sm text-[#282828]">
                  {truncateName(project.name)}
                </p>
                <p className="lg-only:text-[8px] xl:text-[10px] 2xl:text-xs text-[#727272]">
                  {getTasksCount(project)} task{getTasksCount(project) !== 1 ? 's' : ''} assigned
                </p>
              </div>
              <button
                onClick={() => navigate(`/annotator/annotatordescription?id=${project.id}`)}
                className="text-white lg-only:text-[10px] lg-only:px-1.5 lg-only:py-0.5
                  xl:text-xs xl:px-2 xl:py-1
                  2xl:text-sm 2xl:px-3 2xl:py-1.5
                  rounded-md bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2]"
              >
                View Project
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ProjectList;
