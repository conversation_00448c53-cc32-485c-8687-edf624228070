import { PaginatedResponse } from "@/types/generics";
import { Annota<PERSON> } from "@/types/onboarding.types";
import { customAxios } from "@/utils/axio-interceptor";

// Get onboarding list with pagination
export const getOnboardingList = async ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}): Promise<PaginatedResponse<Annotator>> => {
  const response = await customAxios.get("/v1/onboarding/getall", {
    params: { page, limit },
  });
  return response.data.data;
};

// Get package list with pagination
export const getPackageList = async ({ pageParam = 1 }) => {
  const res = await customAxios.get(
    `/v1/packages/get-packages?page=${pageParam}&limit=10`
  );
  return res.data.data;
};

// Create new user
export const createUser = async (data: any) => {
  const response = await customAxios.post("/v1/onboarding/create", data);
  return response.data;
};

// Reset user password
export const resetPassword = async (data: {
  id: string;
  password: string | null;
}) => {
  const response = await customAxios.patch(
    `/v1/onboarding/reset-password/${data.id}`,
    { password: data.password }
  );
  return response.data;
};

// Suspend user account
export const suspendUser = async (data: {
  id: string;
  suspendedUntil: string;
}) => {
  const response = await customAxios.patch(
    `/v1/onboarding/suspension/${data.id}`,
    { suspendedUntil: data.suspendedUntil }
  );
  return response.data;
};

// Activate user account
export const activateUser = async (id: string) => {
  const response = await customAxios.patch(`/v1/onboarding/activation/${id}`);
  return response.data;
};

export const deleteOnboard = async (id: string) => {
  const response = await customAxios.delete(`/v1/onboarding/delete/${id}`);
  return response.data;
};