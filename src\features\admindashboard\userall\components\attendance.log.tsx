import { DataTable } from "@/components/globalfiles/data.table";
import { useAdminColumns } from "./AdminColumn";
import { useAdminList } from "../userall_api/useUsersQuery";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate } from "react-router-dom";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";
import { AttendanceType } from "./attendancetype";

const AdminAllUsers = () => {
  const navigate = useNavigate();
  const { data, isLoading } = useAdminList();

  const columns = useAdminColumns(); // ✅ use hook outside render condition

  if (isLoading) {
    return <BrandedGlobalLoader isLoading />;
  }

  // ✅ Type-safe filtering
  const filteredUsers: AttendanceType[] = (data?.data || []).filter(
    (user: AttendanceType) => user.role === "CLIENT" || user.role === "COWORKER"
  );

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />
        <h1 className="text-[#282828] text-[24px]">All Clients</h1>
      </div>

      <DataTable
        title="Clients & Coworkers"
        columns={columns}
        data={filteredUsers}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default AdminAllUsers;
