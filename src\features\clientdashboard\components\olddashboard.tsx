// import {
//   DndContext,
//   DragEndEvent,
//   DragOverlay,
//   DragStartEvent,
//   useDraggable,
//   useDroppable,
// } from "@dnd-kit/core";
// import { AnimatePresence, motion } from "framer-motion";
// import * as React from "react";
// import { CheckCircle2, Circle, Clock, Pencil, Trash2 } from "lucide-react";

// import { Button } from "@/components/ui/button";
// import {
//   Card,
//   CardDescription,
//   CardHeader,
//   CardTitle,
// } from "@/components/ui/card";
// import Createtask from "./createtask";

// type Task = {
//   id: string;
//   title: string;
//   description: string;
//   level: string;
// };

// type Column = {
//   id: string;
//   title: string;
//   tasks: Task[];
// };

// const initialColumns: Column[] = [
//   {
//     id: "todo",
//     title: "To Do",
//     tasks: [
//       { id: "1", title: "Task 1", description: "Description 1", level: "L" },
//       { id: "2", title: "Task 2", description: "Description 2", level: "L" },
//     ],
//   },
//   {
//     id: "in-progress",
//     title: "Progress",
//     tasks: [
//       {
//         id: "2",
//         title: "Task 2",
//         description:
//           "Lorem ipsum dolor sit amet consectetur. Tellus mi quam cras etiam volutpat mauris tincidunt aenean. Dolor ac sed eget nunc amet sit sagittis maecenas eleifend.",
//         level: "M",
//       },
//     ],
//   },
//   {
//     id: "done",
//     title: "Completed",
//     tasks: [
//       {
//         id: "3",
//         title: "Task 3",
//         description:
//           "Lorem ipsum dolor sit amet consectetur. Tellus mi quam cras etiam volutpat mauris tincidunt aenean. Dolor ac sed eget nunc amet sit sagittis maecenas eleifend.Lorem ipsum dolor sit amet consectetur. Tellus mi quam cras etiam volutpat mauris tincidunt aenean. Dolor ac sed eget nunc amet sit sagittis maecenas eleifend.",
//         level: "H",
//       },
//     ],
//   },
// ];

// function DraggableTask({
//   task,
//   column,
//   onDelete,
// }: {
//   task: Task;
//   column: Column;
//   onDelete: (task: Task) => void;
// }) {
//   const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
//     id: task.id,
//     data: { task, column },
//   });

//   const getTaskIcon = (columnId: string) => {
//     switch (columnId) {
//       case "todo":
//         return <Circle className="h-4 w-4 text-blue-500" />;
//       case "in-progress":
//         return <Clock className="h-4 w-4 text-yellow-500" />;
//       case "done":
//         return <CheckCircle2 className="h-4 w-4 text-green-500" />;
//       default:
//         return null;
//     }
//   };

//   const getTitleIndicatorColor = (columnId: string) => {
//     switch (columnId) {
//       case "todo":
//         return "bg-[#2525AB]";
//       case "in-progress":
//         return "bg-[#E96B1C]";
//       case "done":
//         return "bg-[#E91C24]";
//       default:
//         return "bg-gray-400";
//     }
//   };

//   return (
//     <motion.div
//       layout
//       initial={{ opacity: 0, y: 20 }}
//       animate={{ opacity: 1, y: 0 }}
//       exit={{ opacity: 0, y: -20 }}
//       transition={{ duration: 0.2 }}
//     >
//       <Card
//         className={`mb-3 shadow-sm transition-shadow hover:shadow-md ${
//           isDragging ? "opacity-50" : ""
//         }`}
//       >
//         <CardHeader className="p-3">
//           <div className="flex justify-end">
//             <h1 className="font-normal text-xs">Due Date: 10/09/2024</h1>
//           </div>
//           <div className="flex items-center justify-between">
//             <div
//               ref={setNodeRef}
//               {...attributes}
//               {...listeners}
//               className="flex items-center cursor-move flex-grow"
//             >
//               {getTaskIcon(column.id)}
//               <CardTitle className="text-sm font-medium">
//                 @{task.title}
//               </CardTitle>
//             </div>
//             <div className="flex space-x-2 ml-2">
//               <Button
//                 variant="ghost"
//                 size="icon"
//                 onClick={(e) => {
//                   e.stopPropagation();
//                 }}
//                 aria-label={`Edit task: ${task.title}`}
//               >
//                 <Pencil className="h-4 w-4" />
//               </Button>
//               <Button
//                 variant="ghost"
//                 size="icon"
//                 onClick={(e) => {
//                   e.stopPropagation();
//                   onDelete(task);
//                 }}
//                 aria-label={`Delete task: ${task.title}`}
//               >
//                 <Trash2 className="h-4 w-4" />
//               </Button>
//             </div>
//           </div>
//           <CardDescription className="text-xs line-clamp-4">
//             {task.description}
//           </CardDescription>
//           <div className="flex justify-end">
//             <div
//               className={`w-5 h-5 flex justify-center items-center rounded-full text-white ${getTitleIndicatorColor(
//                 column.id
//               )}`}
//             >
//               <p>{task.level}</p>
//             </div>
//           </div>
//         </CardHeader>
//       </Card>
//     </motion.div>
//   );
// }

// function Column({
//   column,
//   tasks,
//   onDeleteTask,
//   onAddTask,
// }: {
//   column: Column;
//   tasks: Task[];
//   onDeleteTask: (task: Task) => void;
//   onAddTask: (task: Task) => void;
// }) {
//   const { setNodeRef } = useDroppable({
//     id: column.id,
//   });

//   const getColumnColor = (columnId: string) => {
//     switch (columnId) {
//       case "To Do":
//         return "bg-[#FBF4F4]";
//       case "Progress":
//         return "bg-[#FBF4F4]";
//       case "Completed":
//         return "bg-[#FBF4F4]";
//       default:
//         return "bg-muted/50";
//     }
//   };

//   const getTitleIndicatorColor = (columnId: string) => {
//     switch (columnId) {
//       case "todo":
//         return "bg-[#2525AB]";
//       case "in-progress":
//         return "bg-[#E96B1C]";
//       case "done":
//         return "bg-[#5AB24A]";
//       default:
//         return "bg-gray-400";
//     }
//   };

//   return (
//     <div>
//       <div className="flex justify-between px-1 mb-4 items-center">
//         <h2 className="font-semibold text-[#545454] text-muted-foreground">
//           {column.title}
//         </h2>
//         <div
//           className={`w-6 h-6 flex justify-center items-center rounded-full ${getTitleIndicatorColor(
//             column.id
//           )}`}
//         >
//           <Createtask onAddTask={onAddTask} columnId={column.id} />
//         </div>
//       </div>
//       <div
//         ref={setNodeRef}
//         className={`rounded-lg p-4 min-h-[510px] ${getColumnColor(column.id)}`}
//       >
//         <AnimatePresence mode="popLayout">
//           {tasks.map((task) => (
//             <DraggableTask
//               key={task.id}
//               task={task}
//               column={column}
//               onDelete={onDeleteTask}
//             />
//           ))}
//         </AnimatePresence>
//       </div>
//     </div>
//   );
// }

// export default function KanbanBoard() {
//   const [columns, setColumns] = React.useState<Column[]>(initialColumns);
//   const [activeTask, setActiveTask] = React.useState<Task | null>(null);

//   const handleDragStart = (event: DragStartEvent) => {
//     const { active } = event;
//     const task = columns
//       .flatMap((col) => col.tasks)
//       .find((t) => t.id === active.id);
//     if (task) setActiveTask(task);
//   };

//   const handleDragEnd = (event: DragEndEvent) => {
//     const { active, over } = event;

//     if (!over) return;

//     const activeTask = active.data.current?.task as Task;
//     const activeColumn = active.data.current?.column as Column;
//     const overColumn = columns.find((col) => col.id === over.id);

//     if (!activeTask || !activeColumn || !overColumn) return;

//     if (activeColumn.id !== overColumn.id) {
//       setColumns((prevColumns) => {
//         return prevColumns.map((col) => {
//           if (col.id === activeColumn.id) {
//             return {
//               ...col,
//               tasks: col.tasks.filter((t) => t.id !== activeTask.id),
//             };
//           } else if (col.id === overColumn.id) {
//             return {
//               ...col,
//               tasks: [...col.tasks, activeTask],
//             };
//           } else {
//             return col;
//           }
//         });
//       });
//     }

//     setActiveTask(null);
//   };

//   const handleDeleteTask = (taskToDelete: Task) => {
//     setColumns((prevColumns) =>
//       prevColumns.map((column) => ({
//         ...column,
//         tasks: column.tasks.filter((task) => task.id !== taskToDelete.id),
//       }))
//     );
//   };

//   return (
//     <div className="h-full w-full">
//       <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
//           {columns.map((column) => (
//             <Column
//               key={column.id}
//               column={column}
//               tasks={column.tasks}
//               onDeleteTask={handleDeleteTask}
//               onAddTask={handleDeleteTask}
//             />
//           ))}
//         </div>
//         <DragOverlay>
//           {activeTask ? (
//             <Card className="w-[250px]">
//               <CardHeader className="p-3">
//                 <div className="flex items-center space-x-2">
//                   <Circle className="h-4 w-4 text-blue-500" />
//                   <CardTitle className="text-sm font-medium">
//                     {activeTask.title}
//                   </CardTitle>
//                 </div>
//                 <CardDescription className="text-xs mt-1">
//                   {activeTask.description}
//                 </CardDescription>
//               </CardHeader>
//             </Card>
//           ) : null}
//         </DragOverlay>
//       </DndContext>
//     </div>
//   );
// }
