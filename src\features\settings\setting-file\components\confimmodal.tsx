import { useState } from "react";
import OTPModal from "./otpmodal";
import { sendDeleteOtp } from "@/features/admindashboard/components/faq_setting_api/setting/otpdelete"; // adjust path

interface ConfirmModalProps {
  onClose: () => void;
}

export default function ConfirmModal({ onClose }: ConfirmModalProps) {
  const [showOTP, setShowOTP] = useState(false);

  const handleProceed = async () => {
    try {
      await sendDeleteOtp();
      setShowOTP(true);
    } catch (err) {
      console.error("Failed to send OTP:", err);
    }
  };

  return (
    <>
      {!showOTP ? (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-xl shadow-lg p-6 w-[380px] text-center">
            <h2 className="text-xl font-semibold mb-2">Are you sure want to Delete?</h2>
            <p className="text-sm text-gray-600 mb-6">Do you want to permanently delete your account?</p>
            <div className="flex flex-row gap-6 justify-center items-center">
              <button onClick={onClose} className="border border-pink-500 text-pink-500 px-12 py-2 rounded-md hover:bg-pink-100 transition">No</button>
              <button onClick={handleProceed} className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] via-[#FF6ABF] via-[#BF73E6] via-[#7D90E9] to-[#45ADE2] text-white px-10 py-2 rounded-md hover:opacity-90 transition">Proceed</button>
            </div>
          </div>
        </div>
      ) : (
        <OTPModal onClose={onClose} />
      )}
    </>
  );
}
