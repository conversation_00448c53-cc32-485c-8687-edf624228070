import { BackButton } from "@/_components/common";
import React from "react";
import { useNavigate } from "react-router-dom";

const ClientForm: React.FC = () => {
  const navigate = useNavigate();


  const handlePayment = () => {
    navigate("/dashboard");
  };

  return (
    <div className="max-w-2xl mx-auto bg-white shadow-lg rounded-xl p-6 mt-10">
      <div className="flex">
        <BackButton/>
      <h2 className="text-2xl font-semibold mb-6 text-center text-gray-800">
        Questionnaire
      </h2>
      </div>

      <div className="space-y-4">
        <select className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400">
          <option>Select Package Category</option>
          <option>Basic</option>
          <option>Premium</option>
        </select>

        <select className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400">
          <option>Select your preferred time zone</option>
          <option>IST</option>
          <option>GMT</option>
        </select>

        <select className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400">
          <option>Enter Industry</option>
          <option>IT</option>
          <option>Healthcare</option>
        </select>

        <select className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-400">
          <option>Enter Annotation Category</option>
          <option>Category A</option>
          <option>Category B</option>
        </select>

        <div>
          <label className="block mb-1 font-medium">Preferred Work Duration</label>
          <div className="flex gap-4">
            <input type="time" className="w-full border border-gray-300 rounded-lg p-2" />
            <input type="time" className="w-full border border-gray-300 rounded-lg p-2" />
          </div>
        </div>

        <textarea
          placeholder="Description (optional)"
          className="w-full border border-gray-300 rounded-lg p-3 resize-none h-32"
        ></textarea>

        <button
          className="w-full bg-gradient-to-r from-pink-500 to-indigo-500 text-white py-3 rounded-lg font-semibold hover:opacity-90 transition"
          onClick={handlePayment}
        >
          Process for payment →
        </button>
      </div>
    </div>
  );
};

export default ClientForm;
