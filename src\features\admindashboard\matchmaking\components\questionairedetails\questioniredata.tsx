export interface QuestionnaireData {
  packageCategory: string;
  timezone: string;
  industry: string;
  annotatorCategory: string;
  shiftTiming: string;
  startOn: string;
  description: string | null;
}

export const dummyQuestionnaireData: QuestionnaireData[] = [
  {
    packageCategory: "Premium",
    timezone: "UTC+5:30",
    industry: "Technology",
    annotatorCategory: "Data Labeling",
    shiftTiming: "10:00 AM - 6:00 PM",
    startOn: "01/01/2025",
    description: "Client requires data annotation for AI model training.",
  },
  {
    packageCategory: "Standard",
    timezone: "UTC-5:00",
    industry: "Healthcare",
    annotatorCategory: "Medical Data Annotation",
    shiftTiming: "9:00 AM - 5:00 PM",
    startOn: "15/02/2025",
    description: null,
  },
  {
    packageCategory: "Enterprise",
    timezone: "UTC+0:00",
    industry: "Finance",
    annotatorCategory: "Financial Data Analysis",
    shiftTiming: "8:00 AM - 4:00 PM",
    startOn: "10/03/2025",
    description: "Client needs financial document annotation.",
  },
];