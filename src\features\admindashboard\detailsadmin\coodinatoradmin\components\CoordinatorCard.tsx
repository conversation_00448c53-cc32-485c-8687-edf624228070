import React from 'react';
import { useNavigate } from "react-router-dom";
import { getAvatarUrl } from '@/store/dicebearname/getAvatarUrl';

// Interface matching the API response structure
interface CoordinatorProps {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  packageId: string | null;
  assignmentsAsCoordinator: {
    id: string;
    clientId: string;
    developerId: string;
    coordinatorId: string;
    packageId: string;
    createdAt: string;
    client: {
      _count: {
        projectsOwned: number;
        coWorkers: number;
      }
    }
  }[];
  totalClients: number;
  totalAnnotators: number;
  totalCoWorkers: number;
  totalProjects: number;
  // Additional props for UI display
  image?: string;
}

interface CoordinatorCardProps {
  coordinator: CoordinatorProps;
}

const CoordinatorCard: React.FC<CoordinatorCardProps> = ({
  coordinator
}) => {
  const navigate = useNavigate();

  // Generate image URL using dicebear - use first 2 words of name
  const nameWords = coordinator.name.split(' ');
  const firstTwoWords = nameWords.slice(0, 2).join(' ');
  const imageUrl = coordinator.image || getAvatarUrl(firstTwoWords);

  // Get the number of projects from the API response
  const projectsCount = coordinator.totalProjects || 0;

  // Get the number of coworkers from the API response
  const coworkersCount = coordinator.totalCoWorkers || 0;

  return (
    <div className="border border-[#FF577F] rounded-lg shadow-md flex flex-col gap-y-3 p-2 lg:px-4 xl:px-7">
      {/* Profile Section */}
      <div className="flex gap-2 items-center">
        <img
          src={imageUrl}
          alt={coordinator.name}
          className="rounded-full lg:w-10 lg:h-10 xl:w-12 xl:h-12"
        />
        <div className="flex flex-col">
          <h3 className="font-semibold text-sm">{coordinator.name}</h3>
        </div>
      </div>

      {/* Info Container */}
      <div className="mt-2 text-xs flex flex-col gap-3">
        <div className="flex flex-row justify-between">
          <strong className="text-[#5B5B5B] font-medium">Clients:</strong>
          <span>{coordinator.totalClients}</span>
        </div>
        <div className="flex flex-row justify-between">
          <strong className="text-[#5B5B5B] font-medium">Co-Workers:</strong>
          <span>{coworkersCount}</span>
        </div>
        <div className="flex flex-row justify-between">
          <strong className="text-[#5B5B5B] font-medium">Annotators:</strong>
          <span>{coordinator.totalAnnotators}</span>
        </div>
        <div className="flex flex-row justify-between">
          <strong className="text-[#5B5B5B] font-medium">Projects:</strong>
          <span>{projectsCount}</span>
        </div>
      </div>

      {/* Button Container */}
      <div className="flex items-center justify-center mt-4 w-full">
        <button
          onClick={() => {
            console.log("Navigating with coordinator:", coordinator.name);
            navigate(`/admin/admincoodinator?id=${coordinator.id}&name=${encodeURIComponent(coordinator.name)}`);
          }}
          className="bg-gradient-to-r from-[#E91C24] via-[#FF577F] to-[#45ADE2] text-white rounded-[8px] w-full py-[8px] text-xs xl:px-[5.6rem]"
        >
          Clients
        </button>
      </div>
    </div>
  );
};

export default CoordinatorCard;
