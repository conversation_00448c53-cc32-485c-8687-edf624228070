// utils/countries.ts
export const countries = [
  { code: 'US', name: 'United States', phoneCode: '+1' },
  { code: 'IN', name: 'India', phoneCode: '+91' },
  { code: 'CN', name: 'China', phoneCode: '+86' },
  { code: 'JP', name: 'Japan', phoneCode: '+81' },
  { code: 'DE', name: 'Germany', phoneCode: '+49' },
  { code: 'GB', name: 'United Kingdom', phoneCode: '+44' },
  { code: 'FR', name: 'France', phoneCode: '+33' },
  { code: 'BR', name: 'Brazil', phoneCode: '+55' },
  { code: 'IT', name: 'Italy', phoneCode: '+39' },
  { code: 'CA', name: 'Canada', phoneCode: '+1' },
  { code: 'KR', name: 'South Korea', phoneCode: '+82' },
  { code: 'RU', name: 'Russia', phoneCode: '+7' },
  { code: 'AU', name: 'Australia', phoneCode: '+61' },
  { code: 'ES', name: 'Spain', phoneCode: '+34' },
  { code: 'MX', name: 'Mexico', phoneCode: '+52' },
  { code: 'ID', name: 'Indonesia', phoneCode: '+62' },
  { code: 'NL', name: 'Netherlands', phoneCode: '+31' },
  { code: 'SA', name: 'Saudi Arabia', phoneCode: '+966' },
  { code: 'TR', name: 'Turkey', phoneCode: '+90' },
  { code: 'CH', name: 'Switzerland', phoneCode: '+41' },
  { code: 'AR', name: 'Argentina', phoneCode: '+54' },
  { code: 'SE', name: 'Sweden', phoneCode: '+46' },
  { code: 'SG', name: 'Singapore', phoneCode: '+65' },
  { code: 'NG', name: 'Nigeria', phoneCode: '+234' },
  { code: 'AE', name: 'United Arab Emirates', phoneCode: '+971' }
];

export const getCountryName = (code: string): string => {
  const country = countries.find(c => c.code === code);
  return country ? country.name : code;
};