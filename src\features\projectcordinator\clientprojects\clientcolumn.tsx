"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

// Define the interface for the API response
interface ProjectType {
  id: string;
  name: string;
  description: string;
  priority: string;
  status: string;
  coordinatorId: string;
  createdById: string;
  annotatorId: string;
  startDate: string;
  dueDate: string;
  attachment: string[];
  createdAt: string;
  updatedAt: string;
  annotator?: {
    id: string;
    name: string;
    email: string;
    accountStatus: string;
    availableFrom: string | null;
    availableTo: string | null;
  };
}

// Format date to dd/mm/yy
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
  });
};

// Calculate duration in weeks
const calculateDuration = (startDate: string, dueDate: string) => {
  if (!startDate || !dueDate) return "-";

  const start = new Date(startDate);
  const end = new Date(dueDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const diffWeeks = Math.ceil(diffDays / 7);

  return `${diffWeeks} Week${diffWeeks !== 1 ? 's' : ''}`;
};

export const useClientColumns = (): ColumnDef<ProjectType>[] => {
  const navigate = useNavigate();

  return [
    {
      accessorKey: "name",
      header: () => (
        <Button
          variant="ghost"
         // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Project Name
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">Not assigned project</div>;
        }

        // Limit project name to 3 words
        const project = row.original;
        const projectName = project.name || "-";
        const words = projectName.split(" ");
        const displayName = words.slice(0, 3).join(" ") + (words.length > 3 ? "..." : "");

        return <div className="pl-4 text-[14px] font-medium">{displayName}</div>;
      },
    },
    {
      accessorKey: "startDate",
      header: () => (
        <Button
          variant="ghost"
         //nClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Start Date
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">-</div>;
        }

        const project = row.original;
        return (
          <div className="pl-4 text-[14px] font-medium">
            {formatDate(project.startDate)}
          </div>
        );
      },
    },
    {
      accessorKey: "duration",
      header: () => (
        <Button
          variant="ghost"
         //nClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Duration
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">-</div>;
        }

        const project = row.original;
        return (
          <div className="pl-4 text-[14px] font-medium">
            {calculateDuration(project.startDate, project.dueDate)}
          </div>
        );
      },
    },
    {
      accessorKey: "priority",
      header: () => (
        <Button
          variant="ghost"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Priority
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">-</div>;
        }

        const project = row.original;
        return <div className="pl-4 text-[14px] font-medium">{project.priority || "-"}</div>;
      },
    },
    {
      accessorKey: "annotator",
      header: () => (
        <Button
          variant="ghost"
       // onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Annotator
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">Not assigned annotator</div>;
        }

        const project = row.original;
        return (
          <div className="pl-4 text-[14px] font-medium">
            {project.annotator?.name || "Not assigned annotator"}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: () => (
        <Button
          variant="ghost"
        //onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="text-center">-</div>;
        }

        const project = row.original;
        return <div className="pl-4 text-[14px] font-medium">{project.status || "-"}</div>;
      },
    },
    {
      accessorKey: "actions",
      header: () => (
        <Button
          variant="ghost"
         //nClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Actions
        </Button>
      ),
      cell: ({ row }) => {
        if (Object.keys(row.original).length === 0) {
          return <div className="pl-4 text-[14px] font-medium">-</div>;
        }

        const project = row.original;
        return (
          <div className="pl-4">
            <Button
              variant={"gradient"}
              className="px-7 rounded-xl"
              onClick={() => {
                if (project.id) {
                  // Navigate directly to the project details page with the project ID
                  console.log("Navigating to project details with ID:", project.id);
                  navigate(`/coordinator/coordinatorproject-details?id=${project.id}`);
                } else {
                  console.log("No project ID available, using default navigation");
                  navigate("/coordinator/coordinatorproject-details");
                }
              }}
            >
              View Details
            </Button>
          </div>
        );
      },
    },
  ];
};
