import { useLocation, useNavigate, Outlet } from "react-router-dom";
import { useEffect } from "react";

export default function AdminTaskDetails() {
  const navigate = useNavigate();
  const location = useLocation();

  const tabs = [
    { label: "Projects", path: "projects" },
    { label: "Annotators", path: "annotators" },
    { label: "Coordinators", path: "coordinators" },
    { label: "Clients", path: "clients" },
  ];

  const currentTab = tabs.findIndex((tab) =>
    location.pathname.includes(tab.path)
  );

  const handleTabClick = (path: string) => {
    navigate(path);
  };

  useEffect(() => {
    if (currentTab === -1) {
      navigate("clients", { replace: true });
    }
  }, [currentTab, navigate]);

  return (
    <div className="w-full">
      <div className="border-b border-gray-200">
        <div className="flex space-x-20 px-4 ">
          {tabs.map((tab, index) => (
            <button
              key={tab.path}
              onClick={() => handleTabClick(tab.path)}
              className={`pb-2 border-b-4 text-xl font-medium transition-all ${
                currentTab === index
                  ? "border-[#FF577F] text-[#FF577F]"
                  : "border-transparent text-gray-500 hover:text-[#FF577F] hover:border-[#ff517a]"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      <div className="p-4">
        <Outlet />
      </div>
    </div>
  );
}
