Stack trace:
Frame         Function      Args
0007FFFF8E70  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF8E70, 0007FFFF7D70) msys-2.0.dll+0x1FEBA
0007FFFF8E70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9148) msys-2.0.dll+0x67F9
0007FFFF8E70  000210046832 (000210285FF9, 0007FFFF8D28, 0007FFFF8E70, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E70  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E70  0002100690B4 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9150  00021006A49D (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE24D20000 ntdll.dll
7FFE23A30000 KERNEL32.DLL
7FFE21FD0000 KERNELBASE.dll
7FFE23D80000 USER32.dll
000210040000 msys-2.0.dll
7FFE22880000 win32u.dll
7FFE23750000 GDI32.dll
7FFE223C0000 gdi32full.dll
7FFE22680000 msvcp_win.dll
7FFE22730000 ucrtbase.dll
7FFE22CD0000 advapi32.dll
7FFE23780000 msvcrt.dll
7FFE23B00000 sechost.dll
7FFE22EE0000 RPCRT4.dll
7FFE21470000 CRYPTBASE.DLL
7FFE21E70000 bcryptPrimitives.dll
7FFE22E10000 IMM32.DLL
