"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { FaDownload, FaSpinner } from "react-icons/fa";
// import { getInvoiceData } from "../billing_api/billingapi";
import { generateInvoicePdf } from "@/utils/generateInvoicePdf";

interface DownloadInvoiceButtonProps {
  paymentId: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  amount: number;
  status: string;
  paymentMethod: string;
  date: string;
}

export const DownloadInvoiceButton = ({
  paymentId,
  customerName,
  customerEmail,
  packageName,
  amount,
  status,
  paymentMethod,
  date
}: DownloadInvoiceButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      // Generate PDF with all real data
      const doc = generateInvoicePdf({
        provider: "PayPal",
        paymentId: paymentId,
        amount: `USD ${amount}`,
        status: status,
        paymentMethod: paymentMethod,
        date: new Date(date).toLocaleDateString(),
        customerName: customerName,
        customerEmail: customerEmail,
        packageName: packageName,
        price: `USD ${amount}`
      });

      doc.save(`invoice_${paymentId}.pdf`);
    } catch (error) {
      console.error("Download error:", error);
      alert(`Failed to download: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      variant="ghost" 
      onClick={handleDownload}
      disabled={isLoading}
      className="text-[20px] text-[#5f5f5f] hover:text-[#37cf6a] cursor-pointer"
    >
      {isLoading ? <FaSpinner className="animate-spin" /> : <FaDownload />}
    </Button>
  );
};