import { DataTable } from "@/components/globalfiles/data.table";
import { FaArrowLeft } from "react-icons/fa6";
import { useNavigate, useLocation } from "react-router-dom";
import { useCoordinatorColumns } from "./annotatorcolumn";
import { useState, useEffect } from "react";
import { getCoordinatorAnnonatorById } from "../api/api";
import BrandedGlobalLoader from "@/components/loaders/loaders.one";

const CoordinatorAnnotatorProjects = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState<any[]>([]);

  // Get the columns configuration
  const columns = useCoordinatorColumns();

  // Get annotator ID from URL parameters
  const queryParams = new URLSearchParams(location.search);
  const annotatorId = queryParams.get('id');
  const annotatorName = queryParams.get('name') || "Annotator";

  // Fetch projects data
  useEffect(() => {
    const fetchProjects = async () => {
      if (!annotatorId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getCoordinatorAnnonatorById(annotatorId);
        console.log("Annotator projects response:", response);

        // Check different possible response structures
        let projectsData = null;

        // Case 1: response.data.data is an array (most common structure)
        if (response?.data?.data && Array.isArray(response.data.data)) {
          console.log("Found data in response.data.data");
          projectsData = response.data.data;
        }
        // Case 2: response.data is an array
        else if (response?.data && Array.isArray(response.data)) {
          console.log("Found data in response.data");
          projectsData = response.data;
        }
        // Case 3: response itself is an array
        else if (Array.isArray(response)) {
          console.log("Found data in response");
          projectsData = response;
        }
        // Case 4: response is an object with a data property that's an array
        else if (response && typeof response === 'object') {
          // Try to find an array in the response
          for (const key in response) {
            if (Array.isArray(response[key])) {
              console.log(`Found data in response.${key}`);
              projectsData = response[key];
              break;
            }

            // Check one level deeper
            if (response[key] && typeof response[key] === 'object') {
              for (const nestedKey in response[key]) {
                if (Array.isArray(response[key][nestedKey])) {
                  console.log(`Found data in response.${key}.${nestedKey}`);
                  projectsData = response[key][nestedKey];
                  break;
                }
              }
              if (projectsData) break;
            }
          }
        }

        if (projectsData && projectsData.length > 0) {
          console.log("Projects data found:", projectsData);
          setProjects(projectsData);
        } else {
          console.log("No projects data found in response - this is normal if annotator has no projects");
          setProjects([]);
        }
      } catch (err) {
        console.error("Error fetching annotator projects:", err);
        // Don't set error, just use empty array
        setProjects([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [annotatorId]);

  // If annotator ID is missing, show error
  if (!annotatorId) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold">Error</p>
          <p>Missing annotator ID. Please select an annotator first.</p>
          <button
            onClick={() => navigate('/coordinator/coordinatorannotator')}
            className="mt-4 px-4 py-2 bg-[#FF577F] text-white rounded-md hover:bg-[#ff3c6a]"
          >
            Go to Annotators
          </button>
        </div>
      </div>
    );
  }

  // Show loading state
  if (loading) {
    return <BrandedGlobalLoader isLoading={true} />;
  }

  // Don't show error state, just treat it as empty data
  // This ensures the table headers are always shown

  return (
    <div className="bg-white h-full space-y-2">
      <div className="flex flex-wrap gap-3 items-center mx-2">
        <FaArrowLeft
          className="text-2xl text-[#FF577F] cursor-pointer"
          onClick={() => navigate(-1)}
        />

        <h1 className="text-[#282828] text-[24px]">{annotatorName}'s Projects</h1>
      </div>

      <DataTable
        title="Projects"
        columns={columns}
        data={projects.length === 0 ? [{}] : projects}
        loading={false}
        disablePagination
      />
    </div>
  );
};

export default CoordinatorAnnotatorProjects;
