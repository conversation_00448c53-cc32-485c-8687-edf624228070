import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "react-toastify";
import { AnnonatordescriptionTitle } from "../../../annonator_api/annonator_api";

interface DescriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentDescription: string;
  refetch: () => void; // Function to refetch user profile data
}

const DescriptionModal = ({ isOpen, onClose, currentDescription, refetch }: DescriptionModalProps) => {
  const [description, setDescription] = useState(currentDescription);
  const [isLoading, setIsLoading] = useState(false);

  // Function to count words
  const countWords = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  // Handle description change with word limit validation
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    const wordCount = countWords(newDescription);

    if (wordCount <= 20) {
      setDescription(newDescription);
    } else {
      toast.error("Description cannot exceed 20 words.");
    }
  };

  const handleSave = async () => {
    const wordCount = countWords(description);
    if (wordCount > 20) {
      toast.error("Description cannot exceed 20 words.");
      return;
    }

    try {
      setIsLoading(true);
      await AnnonatordescriptionTitle({ description }); // Pass description in the expected format
      toast.success("Description updated successfully");
      refetch(); // Refetch user profile data
      onClose(); // Close the modal
    } catch (error) {
      toast.error("Failed to update description");
      console.error("Error updating description:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-[25px]">Update Description</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="border-gradient rounded-lg relative">
            <Textarea
              placeholder="Enter your description"
              value={description}
              onChange={handleDescriptionChange}
              className="min-h-[100px] max-h-[6rem] h-[6rem]"
            />
         
          </div>
             <div className="text-sm text-gray-500 text-right">
              {countWords(description)}/20 words
            </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            className="border-gradient px-8 border-none"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            onClick={handleSave}
            disabled={isLoading}
            className="px-8"
          >
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DescriptionModal;