import { Navigate, Route, Routes } from "react-router-dom";
import ProjectCordinator from "./dashboard/projectCoardinator";
import CordTaskDetails from "./cordinayordetailscard/AdminTaskDetails";
import Clientscord from "./cordinayordetailscard/clientadmin";
import CordAnnotators from "./cordinayordetailscard/annonatorcoordinator";
import CoordirProjects from "./cordinayordetailscard/projectadmin";
import CordinatorNotification from "./notification/notification";

const ProjectCordinatorRoute = () => {
  return (
    <Routes>
      <Route path="/" element={<ProjectCordinator />}>
        <Route path="/projectdetails" element={<CordTaskDetails />}>
          <Route index element={<Navigate to="clients" replace />} />
          <Route path="clients" element={<Clientscord />} />
          <Route path="annotators" element={<CordAnnotators />} />
          <Route path="projects" element={<CoordirProjects />} />
          <Route path="notification" element={<CordinatorNotification />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default ProjectCordinatorRoute;
